import "./utils/apm";

import express from "express";
import http from "http";
import moment from "moment";
import bodyParser from "body-parser";
import helmet from "helmet";
import cors from "cors";
import compression from "compression";
import "reflect-metadata";

import "./migration";
import { connectMongoDB } from "./utils/MongoDB";

import { SERVER_PORT } from "./utils/EnvConfig";
import { DateFormat } from "./utils/Types";
import routes from "./routes";
import { logExceptionToFile } from "./utils/Helper";
// import Test from "./Test";

const app = express();
app.use(helmet());
app.use(cors());
app.use(compression());
app.use(express.static("public"));
app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());
routes(app);

const server = http.createServer(app);

const DEFAULT_SERVER_PORT = 443;

process.on("uncaughtException", (err) => logExceptionToFile(err));

// Connect to MongoDB before starting the server
connectMongoDB().then(() => {
  server.listen(SERVER_PORT || DEFAULT_SERVER_PORT, () => {
    console.log(
      `Server starts on http://localhost:${
        SERVER_PORT || DEFAULT_SERVER_PORT
      } at ${moment().format(DateFormat.DEFAULT)}`,
    );
  });
});
