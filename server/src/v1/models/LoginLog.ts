import {
  En<PERSON>ty,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  ManyToOne,
  Column,
} from "typeorm";

import User from "./User";
import Organisation from "./Organisation";

@Entity()
class LoginLog {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @ManyToOne(() => User, (user) => user.loginLog)
  user: User;

  @ManyToOne(() => Organisation)
  org: Organisation;

  @Column({ nullable: true })
  ip: string;

  @Column({ nullable: true })
  countryShort: string;

  @Column({ nullable: true })
  countryLong: string;

  @Column({ nullable: true })
  region: string;

  @Column({ nullable: true })
  city: string;
}

export default LoginLog;
