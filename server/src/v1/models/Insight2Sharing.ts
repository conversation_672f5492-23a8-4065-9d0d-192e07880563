import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
} from "typeorm";
import Insight2 from "./Insight2";
import User from "./User";

@Entity()
class Insight2Sharing {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @ManyToOne(() => Insight2)
  insight: Insight2;

  @ManyToOne(() => User)
  toUser: User;

  @Column({ default: false })
  isToUserWritable: boolean;

  @Column({ default: false })
  isEntireOrg: boolean;

  @Column({ default: false })
  isEntireOrgWritable: boolean;

  @UpdateDateColumn({ type: "timestamptz", nullable: true })
  updatedAt: Date;
}

export default Insight2Sharing;
