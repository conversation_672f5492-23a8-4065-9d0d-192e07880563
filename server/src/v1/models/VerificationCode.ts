import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
} from "typeorm";

@Entity()
class VerificationCode {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @Column()
  type: string; // sms or email

  @Column()
  origin: string; // this record comes from which host

  @Column()
  verifyFor: string;

  @Column()
  sentTo: string;

  @Column()
  code: string;

  @Column({ nullable: true })
  ip: string;

  @Column({ nullable: true })
  messageId: string;

  @Column({ nullable: true })
  note: string;

  @Column({ default: false })
  isValid: boolean;
}

export default VerificationCode;
