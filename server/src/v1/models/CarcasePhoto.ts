import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  JoinTable,
  ManyToOne,
} from "typeorm";
import Organisation from "./Organisation";

@Entity()
class CarcasePhoto {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @Column()
  barcode: string; //it is used for identifying the carcase and should be uniq within an org

  @Column()
  photoDate: string;

  @ManyToOne(() => Organisation)
  @JoinTable()
  org: Organisation;

  @Column()
  orgOwner: string; //it is used for identifying the orgnisation

  @Column({ nullable: true })
  isBlurry: boolean;

  @Column({ nullable: true })
  isBadImage: boolean;

  @Column({ nullable: true })
  notes: string;

  @UpdateDateColumn({ type: "timestamptz", nullable: true })
  updatedAt: Date;
}

export default CarcasePhoto;
