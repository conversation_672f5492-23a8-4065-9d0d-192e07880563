import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";

@Entity()
class Terms {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @Column()
  version: number;

  @Column()
  title: string;

  @Column()
  country: string; // two letters of country code, eg, AU, US

  @UpdateDateColumn({ type: "timestamptz", nullable: true })
  updatedAt: Date;
}

export default Terms;
