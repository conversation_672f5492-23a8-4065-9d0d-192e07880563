import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
} from "typeorm";
import Organisation from "./Organisation";
import User from "./User";
import Insight2Item from "./Insight2Item";
import { InsightSharingDetail } from "../../v1/Types";

@Entity()
class Insight2 {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @ManyToOne(() => User)
  user: User;

  @ManyToOne(() => Organisation)
  org: Organisation;

  @Column()
  title: string;

  @Column({ nullable: true })
  description: string;

  @OneToMany(() => Insight2Item, (item) => item.insight)
  items: Insight2Item[];

  @Column({ type: "jsonb", default: [] })
  sortedIds: number[];

  @Column({ default: false })
  isValid: boolean;

  @UpdateDateColumn({ type: "timestamptz", nullable: true })
  updatedAt: Date;

  // for display only, no value saved in DB
  sharedToEntireOrg: InsightSharingDetail;
  sharedToUsers: InsightSharingDetail[];
}

export default Insight2;
