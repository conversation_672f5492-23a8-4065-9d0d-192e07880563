import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  JoinTable,
  ManyToOne,
} from "typeorm";
import Organisation from "./Organisation";

@Entity()
class Carcase {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @Column()
  barcode: string; //it is used for identifying the carcase and should be uniq within an org

  @Column()
  photoDate: string;

  @ManyToOne(() => Organisation)
  @JoinTable()
  org: Organisation;

  @Column()
  orgOwner: string; //it is used for identifying the orgnisation

  @Column({ type: "jsonb", nullable: true })
  traits: string;

  @Column({ nullable: true })
  isGlare: boolean;

  @Column({ nullable: true })
  isBlur: boolean;

  @Column({ nullable: true })
  isExposure: boolean;

  @Column({ nullable: true })
  isWrong: boolean;

  isUpdatedByDataMaintainer: boolean; // for display only, no value saved in DB

  @UpdateDateColumn({ type: "timestamptz", nullable: true })
  updatedAt: Date;
}

export default Carcase;
