import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
} from "typeorm";
import Organisation from "./Organisation";
import User from "./User";

@Entity()
class SnowflakeQuery {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @ManyToOne(() => User)
  user: User;

  @ManyToOne(() => Organisation)
  org: Organisation;

  @Column()
  name: string;

  @Column()
  view: string;

  @Column({ type: "jsonb", nullable: true })
  columnsInterested: string[];

  @Column()
  sqlConditions: string;

  @Column({ type: "jsonb", nullable: true })
  binds: any[];

  @Column({ default: false })
  isValid: boolean;

  @UpdateDateColumn({ type: "timestamptz", nullable: true })
  updatedAt: Date;
}

export default SnowflakeQuery;
