import {
  En<PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
} from "typeorm";
import Organisation from "./Organisation";

@Entity()
class Plugin {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @ManyToMany(() => Organisation, (org) => org.plugins)
  orgs: Organisation[];

  @UpdateDateColumn({ type: "timestamptz", nullable: true })
  updatedAt: Date;
}

export default Plugin;
