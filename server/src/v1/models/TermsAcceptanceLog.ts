import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
} from "typeorm";
import Organisation from "./Organisation";
import User from "./User";

@Entity()
class TermsAcceptanceLog {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @ManyToOne(() => User)
  user: User;

  @ManyToOne(() => Organisation)
  org: Organisation;

  @Column()
  termsVersion: number;

  @UpdateDateColumn({ type: "timestamptz", nullable: true })
  updatedAt: Date;
}

export default TermsAcceptanceLog;
