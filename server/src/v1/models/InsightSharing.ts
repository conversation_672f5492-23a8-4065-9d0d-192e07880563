import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  JoinTable,
  ManyToOne,
} from "typeorm";
import Insight from "./Insight";
import { LooseObject } from "../../utils/Types";
import User from "./User";
import Organisation from "./Organisation";

@Entity()
class InsightSharing {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @ManyToOne(() => Insight)
  insight: Insight;

  //   @ManyToOne(() => Organisation)
  //   org: Organisation;

  @ManyToOne(() => User)
  toUser: User;

  @Column({ default: false })
  isToUserWritable: boolean;

  @Column({ default: false })
  isEntireOrg: boolean;

  @Column({ default: false })
  isEntireOrgWritable: boolean;

  @UpdateDateColumn({ type: "timestamptz", nullable: true })
  updatedAt: Date;
}

export default InsightSharing;
