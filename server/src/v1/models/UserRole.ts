import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
} from "typeorm";

import Organisation from "./Organisation";
import User from "./User";

@Entity()
class UserRole {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @ManyToOne(() => User, (user) => user.roles)
  user: User;

  @ManyToOne(() => Organisation)
  org: Organisation;

  @Column({ default: 0 })
  role: number;

  @Column({ type: "jsonb", nullable: true })
  config: string;

  @UpdateDateColumn({ type: "timestamptz", nullable: true })
  updatedAt: Date;

  @Column({ default: false })
  isValid: boolean;
}

export default UserRole;
