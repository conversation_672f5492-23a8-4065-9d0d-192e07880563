import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  JoinTable,
  ManyToOne,
} from "typeorm";
import Insight from "./Insight";
import { LooseObject } from "../../utils/Types";

@Entity()
class InsightItem {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @ManyToOne(() => Insight, (insight) => insight.items)
  @JoinTable()
  insight: Insight;

  @Column()
  title: string;

  @Column()
  itemType: string; // chart or datatable

  @Column()
  dataSourceKey: string;

  @Column({ type: "jsonb", nullable: true })
  dataSourceParams: LooseObject;

  @Column({ nullable: true })
  chartType: string; // barchart, scatter plot etc

  @Column({ type: "jsonb", default: [] })
  chartTraces: string[];

  @Column({ type: "jsonb", nullable: true })
  chartConfig: LooseObject;

  @Column({ default: false })
  isValid: boolean;

  @UpdateDateColumn({ type: "timestamptz", nullable: true })
  updatedAt: Date;
}

export default InsightItem;
