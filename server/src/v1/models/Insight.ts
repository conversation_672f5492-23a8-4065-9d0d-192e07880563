import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
} from "typeorm";
import Organisation from "./Organisation";
import User from "./User";
import InsightItem from "./InsightItem";
import { InsightSharingDetail } from "../../v1/Types";

@Entity()
class Insight {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @ManyToOne(() => User)
  user: User;

  @ManyToOne(() => Organisation)
  org: Organisation;

  @Column()
  title: string;

  @Column({ type: "jsonb", default: {} })
  startingDate: object;

  @OneToMany(() => InsightItem, (item) => item.insight)
  items: InsightItem[];

  @Column({ type: "jsonb", default: [] })
  sortedIds: number[];

  @Column({ default: false })
  isValid: boolean;

  @UpdateDateColumn({ type: "timestamptz", nullable: true })
  updatedAt: Date;

  // for display only, no value saved in DB
  sharedToEntireOrg: InsightSharingDetail;
  sharedToUsers: InsightSharingDetail[];
}

export default Insight;
