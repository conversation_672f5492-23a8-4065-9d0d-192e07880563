import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  JoinTable,
  ManyToOne,
} from "typeorm";
import Organisation from "./Organisation";
import User from "./User";

@Entity()
class CarcaseMsa {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @Column()
  barcode: string; //it is used for identifying the carcase and should be uniq within an org

  @Column()
  photoDate: string;

  @ManyToOne(() => Organisation)
  @JoinTable()
  org: Organisation;

  @Column()
  orgOwner: string; //it is used for identifying the orgnisation

  @ManyToOne(() => User)
  @JoinTable()
  user: User; // updated by user

  @Column({ nullable: true })
  msa: number; // MSA Marbling updated by user - before 2nd of July 2024

  @Column({ nullable: true })
  msaOriginal: number; // MSA Marbling Original - before 2nd of July 2024

  @Column({ nullable: true })
  msaJbsModelManual: number; // MSA JBS Model updated by user - starting from 2nd of July 2024

  @Column({ nullable: true })
  msaJbsModelOriginal: number; // MSA JBS Model Original - starting from 2nd of July 2024

  @UpdateDateColumn({ type: "timestamptz", nullable: true })
  updatedAt: Date;
}

export default CarcaseMsa;
