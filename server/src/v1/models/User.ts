import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  OneToMany,
  JoinTable,
} from "typeorm";
import Organisation from "./Organisation";
import LoginLog from "./LoginLog";
import UserRole from "./UserRole";

export type OrgsWithRole = {
  name: string;
  orgOwner: string;
  role: number;
  isValid: boolean;
};

@Entity()
class User {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @Column()
  firstName: string;

  @Column()
  lastName: string;

  @Column()
  email: string;

  @Column()
  password: string;

  @ManyToMany(() => Organisation)
  @JoinTable()
  orgs: Organisation[];

  orgId: number; // for display only, no value saved in DB

  orgOwner: string; // for display only, no value saved in DB

  plugins: string[]; // for display only, no value saved in DB

  @OneToMany(() => UserRole, (role) => role.user)
  roles: UserRole[];

  role: number; // for display only, no value saved in DB
  roleConfig: any; // for display only, no value saved in DB
  orgsWithRole: OrgsWithRole[]; // for display only, no value saved in DB

  @Column({ default: 0 })
  version: number;

  @OneToMany(() => LoginLog, (loginLog) => loginLog.user)
  loginLog: LoginLog[];

  lastLogin: Date; // for display only, no value saved in DB

  @UpdateDateColumn({ type: "timestamptz", nullable: true })
  updatedAt: Date;

  @Column({ nullable: true })
  otpAuthSecret: string;

  @Column({ default: false })
  is2FA: boolean;

  @Column({ default: false })
  isDataMaintainer: boolean;

  isValid: boolean; // for display only, no value saved in DB

  isTCAccepted: boolean; // for display only, tag if latest terms and conditions accepted

  @Column({ default: false })
  isDeleted: boolean;
}

export default User;
