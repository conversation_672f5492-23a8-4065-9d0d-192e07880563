import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  JoinTable,
  ManyToOne,
} from "typeorm";
import Insight2 from "./Insight2";
import { LooseObject } from "../../utils/Types";

@Entity()
class Insight2Item {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @ManyToOne(() => Insight2, (insight) => insight.items)
  @JoinTable()
  insight: Insight2;

  @Column()
  title: string;

  @Column({ nullable: true })
  description: string;

  @Column()
  itemType: string; // chart or datatable

  @Column()
  dataSourceKey: string; // id of SnowflakeQuery if isQuery

  @Column({ type: "jsonb", nullable: true })
  dataSourceParams: LooseObject;

  @Column({ default: false })
  isQuery: boolean; // is SnowflakeQuery

  @Column({ nullable: true })
  chartType: string; // barchart, scatter plot etc

  @Column({ type: "jsonb", default: [] })
  chartTraces: string[];

  @Column({ type: "jsonb", nullable: true })
  chartConfig: LooseObject;

  @UpdateDateColumn({ type: "timestamptz", nullable: true })
  updatedAt: Date;
}

export default Insight2Item;
