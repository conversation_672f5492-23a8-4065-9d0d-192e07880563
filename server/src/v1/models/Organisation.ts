import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  JoinTable,
} from "typeorm";

import User from "./User";
import Plugin from "./Plugin";

@Entity()
class Organisation {
  @PrimaryGeneratedColumn()
  id: number;

  @CreateDateColumn({ type: "timestamptz" })
  createdAt: Date;

  @Column()
  owner: string; //it is used for identifying the orgnisation and has to be uinique

  @Column({ nullable: true })
  hostname: string;

  @Column()
  name: string;

  @Column("text")
  logo: string;

  @Column({ type: "jsonb" })
  theme: string;

  @ManyToMany(() => Plugin, (plugin) => plugin.orgs)
  @JoinTable()
  plugins: Plugin[];

  @Column({ default: false })
  isWhiteLabel: boolean; // used to determine the logo and org name display

  @Column({ default: false })
  isUSA: boolean; // used to determine date format

  @Column({ default: false })
  isTCRequired: boolean; // whether terms and conditions are required at users login

  @UpdateDateColumn({ type: "timestamptz", nullable: true })
  updatedAt: Date;

  @Column({ default: false })
  isValid: boolean;
}

export default Organisation;
