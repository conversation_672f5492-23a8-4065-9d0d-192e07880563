import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { Message, ResultCode, ServerResponse } from "../utils/Types";
import { ORG_JSON_PROPERTIES } from "../utils/Constants";
import { Org, OrgCountry, User } from "../models/Interfaces";
import { Organisation, UserRole, User as UserV1 } from "./models";
import AppDataSource from "./AppDataSource";
import { UserRole as UserRoleType } from "./Types";

const isMeqSuperAdmin = async (user: UserV1) => {
  const meqOrg = user.orgs?.find((i) => i.owner === "meq");
  if (meqOrg) {
    const userRoleRepository = AppDataSource.getRepository(UserRole);
    const userRole = await userRoleRepository
      .createQueryBuilder("userRole")
      .leftJoinAndSelect("userRole.user", "user")
      .leftJoinAndSelect("userRole.org", "org")
      .where(
        "userRole.isValid = true AND userRole.role = :superAdmin AND user.id = :userId AND org.id = :orgId",
        {
          superAdmin: UserRoleType.SUPER_ADMIN,
          userId: user.id,
          orgId: meqOrg.id,
        },
      )
      .distinct(true)
      .getOne();

    return userRole;
  }
  return null;
};

export const userOrgsFromV1: RequestHandler = async (req, res) => {
  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const user = req.user as UserV1;

  if (!AppDataSource.isInitialized) {
    result.msg = Message.SUCCESS;
    result.code = ResultCode.SUCCESS;
    result.data = JSON.stringify([]);
    res.json(result);
    return;
  }

  const userRepository = AppDataSource.getRepository(UserV1);
  let userFromDB = await userRepository
    .createQueryBuilder("user")
    .leftJoinAndSelect("user.orgs", "org")
    .where("user.isDeleted = false AND user.email = :email", {
      email: user.email.toLowerCase(),
    })
    .distinct(true)
    .getOne();

  let orgsFromDB = userFromDB?.orgs;

  if (userFromDB && (await isMeqSuperAdmin(userFromDB))) {
    const orgRepository = AppDataSource.getRepository(Organisation);
    orgsFromDB = await orgRepository.find({ where: { isValid: true } });
  }

  const orgs: Org[] =
    orgsFromDB?.map((i) => ({
      createdAt: i.createdAt,
      updatedAt: i.updatedAt,
      zitadelOrgIdString: "MEQ",
      idString: i.id.toString(),
      country: "" as OrgCountry,
      displayName: i.name,
      description: "",
      isTCRequired: false,
      pkgs: [],
      isValid: i.isValid,
    })) || [];

  result.data = JSON.stringify(orgs, ORG_JSON_PROPERTIES);
  result.msg = Message.SUCCESS;
  result.code = ResultCode.SUCCESS;

  res.status(200).json(result);
};
