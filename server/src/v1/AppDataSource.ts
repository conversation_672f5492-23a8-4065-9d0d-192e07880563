import "reflect-metadata";
import { DataSource } from "typeorm";

import {
  User,
  UserRole,
  Organisation,
  Plugin,
  Log,
  LoginLog,
  Carcase,
  CarcaseMsa,
  CarcasePhoto,
  VerificationCode,
  Insight,
  InsightItem,
  InsightSharing,
  Insight2,
  Insight2Item,
  Insight2Sharing,
  SnowflakeQuery,
  Terms,
  TermsAcceptanceLog,
} from "./models";
import { DB_URI_V1 } from "../utils/EnvConfig";

const AppDataSource = new DataSource({
  type: "postgres",
  url: DB_URI_V1,
  synchronize: true,
  logging: false,
  entities: [
    User,
    UserRole,
    Organisation,
    Plugin,
    Log,
    LoginLog,
    Carcase,
    CarcaseMsa,
    CarcasePhoto,
    VerificationCode,
    Insight,
    InsightItem,
    InsightSharing,
    SnowflakeQuery,
    Insight2,
    Insight2Item,
    Insight2Sharing,
    Terms,
    TermsAcceptanceLog,
  ],
  migrations: [],
  subscribers: [],
  ssl: true,
  extra: {
    ssl: {
      rejectUnauthorized: false,
    },
  },
});

AppDataSource.initialize()
  .then(() => console.log("Postgres DB connected"))
  .catch((err) => {
    console.log(err);
  });

export default AppDataSource;
