import { Request<PERSON>and<PERSON> } from "express";
import { RouteItem } from "../utils/Types";
import { getScanFilters } from "../controllers/MeqLive/ScanFilters";
import { queryDeviceUsage } from "../controllers/MeqLive/DeviceUsage";
import { queryLocationUsage } from "../controllers/MeqLive/LocationUsage";
import { queryOrgDeviceLocations } from "../controllers/MeqLive/OrgDeviceLocations";
import { queryOrgLocationDevices } from "../controllers/MeqLive/OrgLocationDevices";
import { queryScanReport } from "../controllers/MeqLive/ScanReport";
import { queryScans } from "../controllers/MeqLive/Scans";
import { querySummaryOfScans } from "../controllers/MeqLive/SummaryOfScans";
import { queryUserReport } from "../controllers/MeqLive/UserReport";

const routes: RouteItem[] = [
  {
    method: "post",
    path: "/user/meq/live/device-usage",
    controller: queryDeviceUsage as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/live/location-usage",
    controller: queryLocationUsage as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/meq/live/org-device-locations",
    controller: queryOrgDeviceLocations as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/meq/live/org-location-devices",
    controller: queryOrgLocationDevices as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/meq/live/scan-filters",
    controller: getScanFilters as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/meq/live/scan-report",
    controller: queryScanReport as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/meq/live/scans",
    controller: queryScans as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/meq/live/summary-of-scans",
    controller: querySummaryOfScans as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/meq/live/user-report",
    controller: queryUserReport as RequestHandler,
    isAuthRequired: true,
  },
];

export default routes;
