import { RequestHand<PERSON> } from "express";

import { RouteItem } from "../utils/Types";
import { generateOmmFile } from "../controllers/AusMeatOmm/generateOmmFile";
import { queryOmmFileRecords } from "../controllers/AusMeatOmm/queryOmmFileRecords";
import { setUserSettings } from "../controllers/AusMeatOmm/setUserSettings";
import { queryOmmUserSettings } from "../controllers/AusMeatOmm/queryOmmUserSettings";
import { setOmmFilePreset } from "../controllers/AusMeatOmm/setOmmFilePreset";
import { queryOmmFile } from "../controllers/AusMeatOmm/queryOmmFile";
import { sendOmmFile } from "../controllers/AusMeatOmm/sendOmmFile";

const routes: RouteItem[] = [
  {
    method: "post",
    path: "/user/meq/aus-meat-omm/omm-file/generate",
    controller: generateOmmFile as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/aus-meat-omm/omm-file-records",
    controller: queryOmmFileRecords as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/aus-meat-omm/omm-file",
    controller: queryOmmFile as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/aus-meat-omm/omm-file/send",
    controller: sendOmmFile as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/aus-meat-omm/user-settings/set",
    controller: setUserSettings as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/aus-meat-omm/user-settings",
    controller: queryOmmUserSettings as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/aus-meat-omm/omm-file-preset/set",
    controller: setOmmFilePreset as RequestHandler,
    isAuthRequired: true,
  },
];

export default routes;
