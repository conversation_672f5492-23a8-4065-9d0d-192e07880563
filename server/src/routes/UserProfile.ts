import { RouteItem } from "../utils/Types";
import {
  userConfig,
  userInfo,
  userOrgs,
  userRoleDisplayNames,
  userRolePermission,
  userRoles,
} from "../controllers/UserProfile";

const routes: RouteItem[] = [
  {
    method: "post",
    path: "/user/UserInfo",
    controller: userInfo,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/UserOrgs",
    controller: userOrgs,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/UserRoles",
    controller: userRoles,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/UserRoleDisplayNames",
    controller: userRoleDisplayNames,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/UserConfig",
    controller: userConfig,
    isAuthRequired: true,
  },

  {
    method: "post",
    path: "/user/UserRolePermission",
    controller: userRolePermission,
    isAuthRequired: true,
  },
];

export default routes;
