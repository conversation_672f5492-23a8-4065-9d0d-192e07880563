import type { Request<PERSON><PERSON><PERSON> } from "express";
import { RouteItem } from "../utils/Types";
import {
  addBooking,
  addGrid,
  addGridPricing,
  addPic,
  assignPics,
  completeSupplyAgreements,
  deleteBooking,
  deleteGrid,
  deletePic,
  getAllAgentsAndProducers,
  getAllBusinessNames,
  getBookings,
  getBookingSupplyAgreements,
  getGridPricings,
  getGrids,
  getMyBookings,
  getMySupplyAgreements,
  getPicsForUser,
  getSettings,
  getSupplyAgreements,
  getTraitsOverTimeLamb,
  getTraitsOverTimeLambAllSuppliers,
  getWeeklySettings,
  loadPics,
  postPicsForUser,
  sendSupplyAgreements,
  updateBooking,
  updateGrid,
  updateGridPricing,
  updatePic,
  updateSettings,
  updateWeeklySettings,
} from "../controllers/GMP";
import {
  getGLQScoreAward,
  getGridComplianceAward,
  getProducerOfTheYear,
  saveProducerOfTheYearConfig,
  getProducerOfTheYearConfig,
  saveGridComplianceConfig,
  getGridComplianceConfig,
  saveGLQScoreConfig,
  getGLQScoreConfig,
} from "../controllers/GMP/getAwards";

const routes: RouteItem[] = [
  {
    method: "get",
    path: "/user/gmp/getPicsForUser",
    controller: getPicsForUser as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/gmp/traitsOverTimeLambAllSuppliers",
    controller: getTraitsOverTimeLambAllSuppliers as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/gmp/traitsOverTimeLamb",
    controller: getTraitsOverTimeLamb as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/LoadPics",
    controller: loadPics as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/AddPic",
    controller: addPic,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/UpdatePic",
    controller: updatePic,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/DeletePic",
    controller: deletePic,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/GetPicsForUser",
    controller: postPicsForUser as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/management/users/AssignGmpPics",
    controller: assignPics,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/GetSettings",
    controller: getSettings,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/UpdateSettings",
    controller: updateSettings,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/GetWeeklySettings",
    controller: getWeeklySettings,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/UpdateWeeklySettings",
    controller: updateWeeklySettings,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/GetGrids",
    controller: getGrids as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/AddGrid",
    controller: addGrid as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/DeleteGrid",
    controller: deleteGrid,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/UpdateGrid",
    controller: updateGrid as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/GetGridPricings",
    controller: getGridPricings,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/AddGridPricing",
    controller: addGridPricing,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/UpdateGridPricing",
    controller: updateGridPricing,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/GetAllBusinessNames",
    controller: getAllBusinessNames,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/GetBookings",
    controller: getBookings as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/AddBooking",
    controller: addBooking as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/UpdateBooking",
    controller: updateBooking,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/DeleteBooking",
    controller: deleteBooking,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/GetSupplyAgreements",
    controller: getSupplyAgreements,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/SendSupplyAgreements",
    controller: sendSupplyAgreements as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/CompleteSupplyAgreements",
    controller: completeSupplyAgreements,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/GetAllAgentsAndProducers",
    controller: getAllAgentsAndProducers,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/GetMyBookings",
    controller: getMyBookings,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/GetBookingSupplyAgreements",
    controller: getBookingSupplyAgreements,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/GetMySupplyAgreements",
    controller: getMySupplyAgreements,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/gmp/awards/gridCompliance",
    controller: getGridComplianceAward as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/gmp/awards/glqScore",
    controller: getGLQScoreAward as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/gmp/awards/producerOfTheYear",
    controller: getProducerOfTheYear as RequestHandler,
    isAuthRequired: true,
  },
  // Configuration management endpoints
  {
    method: "get",
    path: "/user/gmp/awards/config/producerOfTheYear",
    controller: getProducerOfTheYearConfig as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/awards/config/producerOfTheYear",
    controller: saveProducerOfTheYearConfig as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/gmp/awards/config/gridCompliance",
    controller: getGridComplianceConfig as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/awards/config/gridCompliance",
    controller: saveGridComplianceConfig as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/gmp/awards/config/glqScore",
    controller: getGLQScoreConfig as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/gmp/awards/config/glqScore",
    controller: saveGLQScoreConfig as RequestHandler,
    isAuthRequired: true,
  },
];

export default routes;
