import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";

import {
  getBrands,
  getLsrAnalysisBeef,
  getLsrAnalysisLamb,
  getLsrAnalysisLambSweetSpots,
  getLSRsForUser,
  getRegionAnalysisBeef,
  getRegionAnalysisLamb,
  getRegionAnalysisLambSuppliers,
  getRegionAnalysisLambSweetSpots,
  getSuppliersForUser,
  getSuppliersMap,
  getTraitsOverTimeBeef,
  getTraitsOverTimeBeefAllSuppliers,
  getTraitsOverTimeLamb,
  getTraitsOverTimeLambAllSuppliers,
  getTraitsOverTimeLambBrand,
} from "../controllers/Alliance";
import { RouteItem } from "../utils/Types";
import { queryAllianceBeefAggOnSupplierKillsheet } from "../controllers/Alliance/queryAllianceBeefAggOnSupplierKillsheet";
import { queryAllianceBeefGetIndividualDataForFarmerKillsheet } from "../controllers/Alliance/queryAllianceBeefGetIndividualDataForFarmerKillsheet";
import { queryAllianceBeefGetGridReportKillsheet } from "../controllers/Alliance/queryAllianceBeefGetGridReportKillsheet";
import { queryAllianceLambGetAggMobBrandData } from "../controllers/Alliance/queryAllianceLambGetAggMobBrandData";
import { queryAllianceLambAggOnSupplierKillsheet } from "../controllers/Alliance/queryAllianceLambAggOnSupplierKillsheet";
import { queryAllianceLambGetGlobalPercentiles } from "../controllers/Alliance/queryAllianceLambGetGlobalPercentiles";
import { queryAllianceLambGetGridReportKillsheet } from "../controllers/Alliance/queryAllianceLambGetGridReportKillsheet";
import { queryAllianceLambGetIndividualCarcaseData } from "../controllers/Alliance/queryAllianceLambGetIndividualCarcaseData";
import { queryAllianceLambGetIndividualDataForFarmerKillsheet } from "../controllers/Alliance/queryAllianceLambGetIndividualDataForFarmerKillsheet";

const routes: RouteItem[] = [
  {
    method: "get",
    path: "/user/alliance/brands",
    controller: getBrands as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/alliance/lsrAnalysisLamb",
    controller: getLsrAnalysisLamb as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/alliance/lsrAnalysisBeef",
    controller: getLsrAnalysisBeef as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/alliance/lsrAnalysisLambSweetSpots",
    controller: getLsrAnalysisLambSweetSpots as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/alliance/lsrsForUser",
    controller: getLSRsForUser as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/alliance/regionAnalysisLamb",
    controller: getRegionAnalysisLamb as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/alliance/regionAnalysisBeef",
    controller: getRegionAnalysisBeef as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/alliance/regionAnalysisLambSweetSpots",
    controller: getRegionAnalysisLambSweetSpots as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/alliance/regionAnalysisLambSuppliers",
    controller: getRegionAnalysisLambSuppliers as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/alliance/suppliersMap",
    controller: getSuppliersMap as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/alliance/GetSuppliersForUser",
    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    controller: getSuppliersForUser,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/alliance/traitsOverTimeLamb",
    controller: getTraitsOverTimeLamb as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/alliance/traitsOverTimeBeef",
    controller: getTraitsOverTimeBeef as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/alliance/traitsOverTimeLambBrand",
    controller: getTraitsOverTimeLambBrand as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/alliance/traitsOverTimeLambAllSuppliers",
    controller: getTraitsOverTimeLambAllSuppliers(false) as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/alliance/traitsOverTimeBeefAllSuppliers",
    controller: getTraitsOverTimeBeefAllSuppliers(false) as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/alliance/traitsOverTimeLambRmSuppliers",
    controller: getTraitsOverTimeLambAllSuppliers(true) as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "get",
    path: "/user/alliance/traitsOverTimeBeefRmSuppliers",
    controller: getTraitsOverTimeBeefAllSuppliers(true) as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/alliance/alliance_beef_agg_on_supplier_killsheet",
    controller: queryAllianceBeefAggOnSupplierKillsheet as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/alliance/alliance_beef_get_individual_data_for_farmer_killsheet",
    controller:
      queryAllianceBeefGetIndividualDataForFarmerKillsheet as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/alliance/alliance_beef_get_grid_report_killsheet",
    controller: queryAllianceBeefGetGridReportKillsheet as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/alliance/alliance_lamb_get_agg_mob_brand_data",
    controller: queryAllianceLambGetAggMobBrandData as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/alliance/alliance_lamb_agg_on_supplier_killsheet",
    controller: queryAllianceLambAggOnSupplierKillsheet as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/alliance/alliance_lamb_get_global_percentiles",
    controller: queryAllianceLambGetGlobalPercentiles as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/alliance/alliance_lamb_get_grid_report_killsheet",
    controller: queryAllianceLambGetGridReportKillsheet as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/alliance/alliance_lamb_get_individual_carcase_data",
    controller: queryAllianceLambGetIndividualCarcaseData as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/alliance/alliance_lamb_get_individual_data_for_farmer_killsheet",
    controller:
      queryAllianceLambGetIndividualDataForFarmerKillsheet as RequestHandler,
    isAuthRequired: true,
  },
];

export default routes;
