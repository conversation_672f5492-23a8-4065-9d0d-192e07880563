import { RouteItem } from "../../utils/Types";
import {
  addOrg,
  addPermission,
  addRole,
  addUser,
  deletePermission,
  deleteUser,
  getAllActionLog,
  getAllOrgs,
  getAllPermissions,
  getAllRoles,
  getAllUsers,
  getAllZitadelOrgs,
  toggleUserState,
  updateOrg,
  updatePermission,
  updateRole,
  updateRoles,
  updateUser,
} from "../../controllers/Administration";

const routes: RouteItem[] = [
  {
    method: "post",
    path: "/user/administration/GetAllOrgs",
    controller: getAllOrgs,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/administration/AddOrg",
    controller: addOrg,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/administration/UpdateOrg",
    controller: updateOrg,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/administration/GetAllUsers",
    controller: getAllUsers,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/administration/AddUser",
    controller: addUser,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/administration/UpdateUser",
    controller: updateUser,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/administration/DeleteUser",
    controller: deleteUser,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/administration/ToggleUserState",
    controller: toggleUserState,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/administration/GetAllRoles",
    controller: getAllRoles,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/administration/UpdateRoles",
    controller: updateRoles,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/administration/UpdateRole",
    controller: updateRole,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/administration/AddRole",
    controller: addRole,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/administration/GetAllPermissions",
    controller: getAllPermissions,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/administration/UpdatePermission",
    controller: updatePermission,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/administration/AddPermission",
    controller: addPermission,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/administration/DeletePermission",
    controller: deletePermission,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/administration/GetAllActionLog",
    controller: getAllActionLog,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/administration/GetAllZitadelOrgs",
    controller: getAllZitadelOrgs,
    isAuthRequired: true,
  },
];

export default routes;
