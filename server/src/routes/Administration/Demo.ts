import { Request<PERSON>and<PERSON> } from "express";
import {
  addUser,
  deleteUser,
  editUser,
  getUsers,
  inviteUser,
} from "../../controllers/Administration/Demo";
import { RouteItem } from "../../utils/Types";

const routes: RouteItem[] = [
  {
    method: "get",
    path: "/user/administration/demo/users",
    controller: getUsers as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/administration/demo/user",
    controller: addUser as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/administration/demo/deleteUser",
    controller: deleteUser as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/administration/demo/editUser",
    controller: editUser as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/administration/demo/inviteUser",
    controller: inviteUser as RequestHandler,
    isAuthRequired: true,
  },
];

export default routes;
