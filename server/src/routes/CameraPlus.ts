import { Request<PERSON><PERSON><PERSON> } from "express";
import { RouteItem } from "../utils/Types";
import { queryDateRangeAverages } from "../controllers/CameraPlus/queryDateRangeAverages";
import { queryDailyOverrideDiffTrend } from "../controllers/CameraPlus/queryDailyOverrideDiffTrend";
import { queryOverrideDiffHistogram } from "../controllers/CameraPlus/queryOverrideDiffHistogram";
import { queryDailyGradingFreq } from "../controllers/CameraPlus/queryDailyGradingFreq";
import { queryUserStats } from "../controllers/CameraPlus/queryUserStats";
import { queryBrands } from "../controllers/CameraPlus/queryBrands";
import { queryBarcodes } from "../controllers/CameraPlus/queryBarcodes";
import { queryProbeCameraAverages } from "../controllers/CameraPlus/queryProbeCameraAverages";
import { queryMarblingVsEyeMuscleArea } from "../controllers/CameraPlus/queryMarblingVsEyeMuscleArea";
import { queryDarkCutters } from "../controllers/CameraPlus/queryDarkCutters";

const routes: RouteItem[] = [
  {
    method: "post",
    path: "/user/meq/camera-plus/date-range-averages",
    controller: queryDateRangeAverages as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/camera-plus/daily-override-diff-trend",
    controller: queryDailyOverrideDiffTrend as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/camera-plus/override-diff-histogram",
    controller: queryOverrideDiffHistogram as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/camera-plus/daily-grading-freq",
    controller: queryDailyGradingFreq as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/camera-plus/user-stats",
    controller: queryUserStats as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/camera-plus/brands",
    controller: queryBrands as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/camera-plus/barcodes",
    controller: queryBarcodes as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/camera-plus/probe-camera-averages",
    controller: queryProbeCameraAverages as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/camera-plus/marbling-vs-eye-muscle-area",
    controller: queryMarblingVsEyeMuscleArea as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/camera-plus/dark-cutters",
    controller: queryDarkCutters as RequestHandler,
    isAuthRequired: true,
  },
];

export default routes;
