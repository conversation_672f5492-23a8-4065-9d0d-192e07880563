import { RouteItem } from "../../utils/Types";
import {
  addUser,
  deleteUser,
  getAllRoles,
  getAllUsersOfOrg,
  updateRoles,
  updateUser,
} from "../../controllers/Management/Users";

const routes: RouteItem[] = [
  {
    method: "post",
    path: "/user/management/users/GetAllUsersOfOrg",
    controller: getAllUsersOfOrg,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/management/users/AddUser",
    controller: addUser,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/management/users/UpdateUser",
    controller: updateUser,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/management/users/DeleteUser",
    controller: deleteUser,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/management/users/GetAllRoles",
    controller: getAllRoles,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/management/users/UpdateRoles",
    controller: updateRoles,
    isAuthRequired: true,
  },
];

export default routes;
