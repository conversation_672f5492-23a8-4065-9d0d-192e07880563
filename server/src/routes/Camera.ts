import { RequestHand<PERSON> } from "express";
import { RouteItem } from "../utils/Types";
import { queryUserSettings } from "../controllers/Camera/queryUserSettings";
import { queryIndividualImages } from "../controllers/Camera/queryIndividualImages";
import { queryDailySummaryMetric } from "../controllers/Camera/queryDailySummaryMetric";

const routes: RouteItem[] = [
  {
    method: "post",
    path: "/user/meq/camera/user-settings",
    controller: queryUserSettings as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/camera/individual-images",
    controller: queryIndividualImages as <PERSON>quest<PERSON>and<PERSON>,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/camera/daily-summary-metric",
    controller: queryDailySummaryMetric as RequestHandler,
    isAuthRequired: true,
  },
];

export default routes;
