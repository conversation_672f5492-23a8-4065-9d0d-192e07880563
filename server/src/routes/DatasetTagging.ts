import { <PERSON>questHand<PERSON> } from "express";
import { RouteItem } from "../utils/Types";
import { addTaggingEvent } from "../controllers/DatasetTagging/addTaggingEvent";
import { queryTags } from "../controllers/DatasetTagging/queryTags";
import { queryUserSettings } from "../controllers/DatasetTagging/queryUserSettings";

const routes: RouteItem[] = [
  {
    method: "post",
    path: "/user/meq/dataset-tagging/add",
    controller: addTaggingEvent as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/dataset-tagging/tags",
    controller: queryTags as <PERSON>questHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/dataset-tagging/user-settings",
    controller: queryUserSettings as RequestHandler,
    isAuthRequired: true,
  },
];

export default routes;
