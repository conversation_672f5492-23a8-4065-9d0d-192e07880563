import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";

import { queryBeefProbeScans } from "../controllers/Probe/queryBeefProbeScans";
import { queryBeefProbeDailyStats } from "../controllers/Probe/queryBeefProbeDailyStats";
import { queryLambProbeScans } from "../controllers/Probe/queryLambProbeScans";
import { queryLambProbeDailyStats } from "../controllers/Probe/queryLambProbeDailyStats";
import { queryUserSettings } from "../controllers/Probe/queryUserSettings";
import { RouteItem } from "../utils/Types";

const probeRoutes: RouteItem[] = [
  {
    method: "post",
    path: "/user/meq/probe/beef-scans",
    controller: queryBeefProbeScans as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/probe/beef-daily-stats",
    controller: queryBeefProbeDailyStats as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/probe/lamb-scans",
    controller: queryLambProbeScans as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/probe/lamb-daily-stats",
    controller: queryLambProbeDailyStats as RequestHandler,
    isAuthRequired: true,
  },
  {
    method: "post",
    path: "/user/meq/probe/user-settings",
    controller: queryUserSettings as RequestHandler,
    isAuthRequired: true,
  },
];

export default probeRoutes;
