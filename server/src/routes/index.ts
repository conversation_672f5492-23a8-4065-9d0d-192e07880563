import express, { Express, Router } from "express";
import asyncHandler from "express-async-handler";
import _ from "lodash";

import {
  loadUserFromDB,
  logAccess,
  unauthorized,
  validateZitadelAccessToken,
} from "../controllers/Common";
import commonRoutes from "./Common";
import authRoutes from "./Auth";
import pluginWideRoutes from "./PluginWide";
import meqLiveRoutes from "./MeqLive";
import cameraRoutes from "./Camera";
import cameraPlusRoutes from "./CameraPlus";
import ausMeatOmmRoutes from "./AusMeatOmm";
import datasetTaggingRoutes from "./DatasetTagging";
import probeRoutes from "./Probe";
import userProfileRoutes from "./UserProfile";
import userRoutes from "./User";
import administrationRoutes from "./Administration";
import administrationDemoRoutes from "./Administration/Demo";
import userManagementRoutes from "./Management/Users";
import gmpRoutes from "./GMP";
import allianceRoutes from "./Alliance";
import v1Routes from "../v1/Routes";
import { RouteItem } from "../utils/Types";

const registerRoutes = (router: Router, items: RouteItem[]) => {
  if (router && items && Array.isArray(items) && items.length > 0) {
    const methodAndPath = items.map((item) => ({
      method: item.method,
      path: item.path,
    }));
    const isDuplicate =
      _.uniqWith(methodAndPath, _.isEqual).length !== methodAndPath.length;

    if (isDuplicate) {
      throw new Error("Duplicated routes detected!");
    } else {
      items.forEach((item) => {
        switch (item.method) {
          case "get":
            if (item.isAuthRequired) {
              router
                .route(item.path)
                .get(
                  validateZitadelAccessToken,
                  loadUserFromDB,
                  logAccess,
                  asyncHandler(item.controller),
                  unauthorized,
                );
            } else {
              router.route(item.path).get(item.controller);
            }
            break;
          case "post":
            if (item.isAuthRequired) {
              router
                .route(item.path)
                .post(
                  validateZitadelAccessToken,
                  loadUserFromDB,
                  logAccess,
                  asyncHandler(item.controller),
                  unauthorized,
                );
            } else {
              router.route(item.path).post(item.controller);
            }
            break;
        }
      });
    }
  }
};

const routes = (app: Express) => {
  const router = express.Router();
  registerRoutes(
    router,
    _.concat(
      commonRoutes,
      authRoutes,
      pluginWideRoutes,
      meqLiveRoutes,
      probeRoutes,
      cameraRoutes,
      cameraPlusRoutes,
      ausMeatOmmRoutes,
      datasetTaggingRoutes,
      userProfileRoutes,
      userRoutes,
      userManagementRoutes,
      administrationRoutes,
      administrationDemoRoutes,
      v1Routes,
      gmpRoutes,
      allianceRoutes,
    ),
  );

  app.use("/", router);
};

export default routes;
