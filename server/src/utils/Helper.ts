import log4js from "log4js";
import axios from "axios";
import _ from "lodash";
import * as openpgp from "openpgp";
import crypto from "crypto";
import {
  CRYPTO_ALGRORITHM,
  CRYPTO_SECRET,
  SYSTEM_EMAIL_PASSWORD,
} from "./EnvConfig";
import {
  LooseObject,
  SnowflakeStatementResultColumn,
  SystemEmail,
} from "./Types";
import * as Models from "../models";
import nodemailer from "nodemailer";
import validator from "validator";
import {
  ALL,
  ALL_LOCATIONS,
  INVALID_ORG,
  SUPER_ADMIN,
  SUPER_USER,
} from "./Constants";
import { ActionLog, GmpGridPricing, User } from "../models/Interfaces";

export const logExceptionToFile = (log: any) => {
  // save to local file server_error.log
  log4js.configure({
    appenders: { serverLog: { type: "file", filename: "server_error.log" } },
    categories: { default: { appenders: ["serverLog"], level: "info" } },
  });
  const logger = log4js.getLogger();
  logger.info(log);
};

export const logExceptionToDB = (text: any) => {
  // save to database
  const log = new Models.Exception({ text });
  log
    .save()
    .then()
    .catch((err) => console.log(err));
};

export const logActionToDB = (actionLog: ActionLog) => {
  const log = new Models.ActionLog(actionLog);
  log
    .save()
    .then()
    .catch((err) => console.log(err));
};

export const encrypt = (str: string) => {
  if (CRYPTO_SECRET && CRYPTO_ALGRORITHM) {
    const key = crypto.scryptSync(CRYPTO_SECRET, "salt", 32);
    const iv = Buffer.alloc(16, 0);
    const cipher = crypto.createCipheriv(CRYPTO_ALGRORITHM, key, iv);

    let encrypted = cipher.update(str, "utf8", "hex");
    encrypted += cipher.final("hex");
    return encrypted;
  } else {
    throw new Error("Please a valid provide secret and algorithm");
  }
};

export const decrypt = (str: string) => {
  if (CRYPTO_SECRET && CRYPTO_ALGRORITHM) {
    const key = crypto.scryptSync(CRYPTO_SECRET, "salt", 32);
    const iv = Buffer.alloc(16, 0);
    const decipher = crypto.createDecipheriv(CRYPTO_ALGRORITHM, key, iv);

    let decrypted = decipher.update(str, "hex", "utf8");
    decrypted += decipher.final("utf8");
    return decrypted;
  } else {
    throw new Error("Please provide a valid secret and algorithm");
  }
};

export const generateRsaKey = (passphrase: string) =>
  openpgp.generateKey({
    type: "rsa", // Type of the key
    rsaBits: 4096, // RSA key size (defaults to 4096 bits)
    userIDs: [{ name: "MEQ Insights" }], // you can pass multiple user IDs
    passphrase, // protects the private key
  });

export const asyEncrypt = async ({
  text,
  pubKey,
}: {
  text: string;
  pubKey: string;
}) => {
  const publicKey = await openpgp.readKey({ armoredKey: pubKey });
  const encrypted = await openpgp.encrypt({
    message: await openpgp.createMessage({ text }), // input as Message object
    encryptionKeys: publicKey,
  });
  return encrypted;
};

export const asyDecrypt = async ({
  encryptedText,
  passphrase,
  privKey,
}: {
  encryptedText: openpgp.WebStream<string>;
  passphrase: string;
  privKey: string;
}) => {
  const privateKey = await openpgp.decryptKey({
    privateKey: await openpgp.readPrivateKey({
      armoredKey: privKey,
    }),
    passphrase,
  });
  const message = await openpgp.readMessage({
    armoredMessage: encryptedText,
  });
  const { data: decrypted } = await openpgp.decrypt({
    message,
    decryptionKeys: privateKey,
  });
  return decrypted;
};

export const isPositiveNumber = (num: string): boolean => {
  const regex = /^([0-9]*[1-9][0-9]*(\.[0-9]+)?|[0]+\.[0-9]*[1-9][0-9]*)$/;
  return regex.test(num);
};

export const isEvenNumber = (n: number): boolean => n % 2 === 0;

export const isNotEmpty = (data: any) =>
  data !== undefined &&
  data !== null &&
  data !== "" &&
  JSON.stringify(data) !== "{}";

export const sanitiseParams = (obj: LooseObject) =>
  _.mapValues(obj, (o) => (typeof o === "string" ? o.trim() : o));

export const delay = (ms: number) => new Promise((res) => setTimeout(res, ms));

type sendHttpRequestProps = {
  method?: string;
  url: string;
  params: {};
};
export const sendHttpRequest = async ({
  method,
  url,
  params,
}: sendHttpRequestProps) => {
  axios.defaults.timeout = 20 * 1000; // 20 seconds
  let result = null;
  switch (method?.toLowerCase()) {
    default: // default as post
      await axios
        .post(url, params)
        .then((response) => {
          if (response && response.data) {
            result = response.data;
          }
        })
        .catch((e) => {
          result = null;
          console.log(e.message);
          logExceptionToFile(e);
        });
      break;
  }
  return result;
};

const EMAIL_SENDER = "<EMAIL>";
export const sendMail = async (email: SystemEmail) => {
  const result = { isSuccess: false, messageId: "" };
  if (email.html && email.subject && email.to && validator.isEmail(email.to)) {
    const msg = {
      from: `MEQ Insights no_reply <${EMAIL_SENDER}>`,
      to: email.to,
      subject: email.subject,
      html: email.html,
    };

    // send mail using nodemailer, limit: 500 emails/day
    await nodemailer
      .createTransport({
        host: "smtp.gmail.com",
        port: 465,
        secure: true, // true for 465, false for other ports
        auth: {
          user: EMAIL_SENDER,
          pass: SYSTEM_EMAIL_PASSWORD,
        },
      })
      .sendMail(msg)
      .then((data) => {
        if (data?.accepted?.indexOf(email.to) >= 0) {
          result.isSuccess = true;
          result.messageId = data.messageId;
        }
      })
      .catch((e) => {
        console.log(e.message);
        logExceptionToFile(e);
      });
  }

  return result;
};

export const getOrgFromRole = (role: string) => role.split("-")[0];

export const getOrgIdStringsFromUser = (user: User) =>
  user.roles
    .filter((role) => role !== SUPER_ADMIN && role !== SUPER_USER)
    .map((role) => getOrgFromRole(role));

export const getOrgFromOrgIdString = (orgIdString: string) =>
  orgIdString ? orgIdString.split("_")[0] : "NotValidOrg";

export const getLocationFromOrgIdString = (idString?: string) => {
  let location = "";
  if (idString) {
    const strArray = idString.split("_");
    if (strArray.length > 1) {
      location = strArray[1];
    }
  }
  return location;
};

export function isLocationAllowedByOrgIdString(
  location?: string,
  orgIdString?: string,
): boolean {
  if (!location || !orgIdString) {
    return false;
  }

  const orgLocation = getLocationFromOrgIdString(orgIdString);
  if (orgLocation === ALL_LOCATIONS) {
    return true;
  } else {
    return location === orgLocation;
  }
}

export const getDbLocationFromOrgIdString = (idString?: string) => {
  let location = getLocationFromOrgIdString(idString);
  if (location === ALL_LOCATIONS) {
    location = ALL;
  }
  return location;
};

export const getDbLocationFromOrgLocation = (orgLocation: string) => {
  return orgLocation === ALL_LOCATIONS ? ALL : orgLocation;
};

function getOrgAndLocationFromOrgId(
  orgId: string,
): { success: false } | { success: true; org: string; location: string } {
  const [org, location] = orgId.split("_");

  if (!org) {
    return { success: false };
  }

  return { success: true, org, location: location ?? "" };
}

export const getCameraOwnerStringFromOrgIdString = (idString?: string) =>
  idString ? idString.toLowerCase().replaceAll("_", "-") : "";

export const hasPermission = ({
  roles,
  requiredRoles,
}: {
  roles: string[];
  requiredRoles: string[];
}) => {
  let hasPermission =
    _.intersection(roles, requiredRoles).length > 0 ||
    roles.includes(SUPER_ADMIN) ||
    roles.includes(SUPER_USER);

  if (!hasPermission) {
    // allow all locations user to access single locations
    requiredRoles.forEach((requiredRole) => {
      const roleAllLocations = requiredRole.replace(
        getLocationFromOrgIdString(getOrgFromRole(requiredRole)),
        ALL_LOCATIONS,
      );
      if (roles.find((i) => i === roleAllLocations)) {
        hasPermission = true;
      }
    });
  }
  return hasPermission;
};

export function isRoleValidInOrg(role: string, targetOrgId: string): boolean {
  const roleOrgId = getOrgFromRole(role);
  const roleOrgLoc = getOrgAndLocationFromOrgId(roleOrgId);
  const targetOrgLoc = getOrgAndLocationFromOrgId(targetOrgId);
  if (roleOrgLoc.success && targetOrgLoc.success) {
    if (roleOrgLoc.org === targetOrgLoc.org) {
      if (roleOrgLoc.location === ALL_LOCATIONS) {
        return true;
      } else {
        return roleOrgLoc.location === targetOrgLoc.location;
      }
    }
  }
  return false;
}

export const getOptimisedColumns = ({
  columns,
  rows,
  columnsToRemove,
  columnsToRename,
}: {
  columns?: SnowflakeStatementResultColumn[];
  rows?: any[];
  columnsToRemove?: string[];
  columnsToRename?: { [key: string]: string };
}) => {
  if (columns && rows) {
    const redundantColumnsToRemove = ["ROW_NUM"];
    const columnsToKeep: SnowflakeStatementResultColumn[] = [];
    columns
      .filter((column) => {
        let included = !redundantColumnsToRemove.includes(column.name);
        if (columnsToRemove) {
          included = included && !columnsToRemove.includes(column.name);
        }
        return included;
      })
      .forEach((column) => {
        // remove columns with null values in all rows
        let isNonEmptyColumn = false;
        rows.forEach((row) => {
          if (row[column.name] !== null) {
            isNonEmptyColumn = true;
          }
        });
        if (isNonEmptyColumn) {
          columnsToKeep.push(column);
        }
      });

    const namesOfColumnsToKeep = columnsToKeep.map((i) => i.name);
    const rowsOnlyWithKeptColumns = rows.map((row) => {
      const updatedRow: LooseObject = {};
      Object.keys(row).forEach((key) => {
        if (namesOfColumnsToKeep.includes(key)) {
          const colName = columnsToRename?.[key] || key;
          updatedRow[colName] = row[key];
        }
      });
      return updatedRow;
    });

    columnsToKeep.forEach((column) => {
      const colName = columnsToRename?.[column.name] || column.name;
      column.name = colName;
    });

    return { columns: columnsToKeep, rows: rowsOnlyWithKeptColumns };
  }

  return { columns, rows };
};

export const isValidGmpGridPricingData = (gridPricingData: GmpGridPricing) => {
  let isValid = !!gridPricingData;
  if (gridPricingData.year < 2020 || gridPricingData.year > 2050) {
    isValid = false;
  }
  if (gridPricingData.week < 1 || gridPricingData.week > 52) {
    isValid = false;
  }
  gridPricingData.prices.forEach((i) => {
    if (!i.lmyCategory1 || !i.lmyCategory2) {
      isValid = false;
    }
  });
  if (gridPricingData.gridType === "GL") {
    gridPricingData.prices.forEach((i) => {
      if (!i.lmyCategory3 || !i.lmyCategory4 || !i.lmyCategory5) {
        isValid = false;
      }
    });
  }
  return isValid;
};

export const createZitadelOrgManually = async (org: LooseObject) => {
  const existing = await Models.ZitadelOrg.findOne({
    idString: org.idString,
  }).then((o) => o);

  if (existing) {
    console.log(org.idString + " exists");
  } else {
    const newOrg = new Models.ZitadelOrg(org);
    await newOrg.save().then((o) => {
      if (o) {
        console.log(org.idString + " has been created successfully");
      } else {
        console.log("Faild to create " + org.idString);
      }
    });
  }
};

export function getRoleComponents(role: string):
  | {
      org: string;
      location?: string;
      roleName: string;
    }
  | undefined {
  const dashSeparatedComponents = role.split("-");
  if (dashSeparatedComponents.length != 2) {
    return;
  }

  const roleName = dashSeparatedComponents[1];
  const underscoreSeparatedComponents = dashSeparatedComponents[0].split("_");

  if (
    dashSeparatedComponents.length < 1 ||
    dashSeparatedComponents.length > 2
  ) {
    return;
  }

  return {
    org: underscoreSeparatedComponents[0],
    location:
      underscoreSeparatedComponents.length == 2
        ? underscoreSeparatedComponents[1]
        : undefined,
    roleName,
  };
}

export function hasAccessToOrg(
  roles: string[],
  targetOrgIdString: string,
): boolean {
  // SUPER_ADMIN and SUPER_USER have access to everything
  if (roles.includes(SUPER_ADMIN) || roles.includes(SUPER_USER)) {
    return true;
  }

  // Check if any of the user's roles grant access
  return roles.some((role) => {
    const roleComponents = getRoleComponents(role);
    if (!roleComponents) return false;

    const { org, location } = roleComponents;

    // If the target org string contains a location (has underscore)
    if (targetOrgIdString.includes("_")) {
      const [targetOrg, targetLocation] = targetOrgIdString.split("_");

      // Check if orgs match
      if (org !== targetOrg) return false;

      // If user has ALL_LOCATIONS access for this org, they can access any location
      if (location === ALL_LOCATIONS) return true;

      // Otherwise, locations must match exactly
      return location === targetLocation;
    } else {
      // For orgs without locations, just check if orgs match
      return org === targetOrgIdString;
    }
  });
}

export function getOrgIdStringComponents(orgIdString: string): {
  org: string;
  location: string;
} {
  if (!orgIdString) {
    return { org: INVALID_ORG, location: ALL_LOCATIONS };
  }

  const underscoreSeparatedComponents = orgIdString.split("_");
  const org = underscoreSeparatedComponents[0];

  // Handle Alliance_AllLocations case
  // if (orgIdString === "Alliance_AllLocations") {
  //   return { org: "Alliance", location: ALL_LOCATIONS };
  // }

  const location =
    underscoreSeparatedComponents.length > 1
      ? underscoreSeparatedComponents.slice(1).join("_")
      : ALL_LOCATIONS;

  return { org, location };
}

export { ALL, ALL_LOCATIONS, INVALID_ORG, SUPER_ADMIN, SUPER_USER };
