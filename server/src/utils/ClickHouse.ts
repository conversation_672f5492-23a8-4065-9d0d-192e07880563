import { createClient, ResponseJSO<PERSON> } from "@clickhouse/client";
import _ from "lodash";

import {
  CLICKHOUSE_URL,
  CLICKHOUSE_USER,
  CLICKHOUSE_PW,
  DEPLOYMENT_TARGET,
  isFeatureEnabled,
} from "../utils/EnvConfig";
import { PkgName } from "./Types";

let client: ReturnType<typeof createClient> | null = null;

export function chClient() {
  if (client == null) {
    client = createClient({
      url: CLICKHOUSE_URL ?? "http://localhost:8123",
      username: CLICKHOUSE_USER ?? "default",
      password: CLICKHOUSE_PW ?? "",
    });
  }
  return client;
}

export function isClickHouseEnabled(
  options: { devOnly: boolean } = { devOnly: false },
) {
  return isFeatureEnabled({
    featureFlagName: "clickhouse",
    deploymentTarget: options.devOnly ? "dev" : undefined,
  });
}

const database_prefixes: { [pkg: string]: string } = {
  [PkgName.MEQ_CAMERA_IMAGE_SUMMARY_EXT]: "meq_camera_image_summary_ext",
  [PkgName.MEQ_CAMERA_PLUS]: "meq_camera_plus",
  [PkgName.MEQ_CAMERA]: "meq_camera",
  [PkgName.ALLIANCE_DATA_ANALYSIS]: "client_alliance",
  [PkgName.MEQ_PROBE]: "meq_probe",
};

export function findDatabaseForPackage(pkg: PkgName) {
  if (DEPLOYMENT_TARGET === "dev" || DEPLOYMENT_TARGET === "prd") {
    const database_prefix = database_prefixes[pkg];
    if (database_prefix != null) {
      return `${database_prefix}_${DEPLOYMENT_TARGET}`;
    }
  }

  return null;
}

export function removeUnusedColumnsInQueryResult<T>(
  queryResult: ResponseJSON<T>,
  columnsToRemove?: string[],
  columnsToRename?: { [key: string]: string },
) {
  let colsToRemove = ["ROW_NUM"];
  const { data, meta } = queryResult;

  if (columnsToRemove) {
    colsToRemove = colsToRemove.concat(columnsToRemove);
  }

  colsToRemove = colsToRemove.concat(
    meta
      ?.map((v) => v.name)
      .filter((col) => data.every((r) => _.get(r, col) == null)) || [],
  );

  queryResult.meta = queryResult.meta?.filter(
    (v) => !colsToRemove.some((c) => c === v.name),
  );
  queryResult.meta?.forEach((v) => {
    const colName = columnsToRename?.[v.name] || v.name;
    v.name = colName;
  });
  queryResult.data.forEach((row) => {
    const r = row as Record<string, unknown>;
    colsToRemove.forEach((c) => delete r[c]);
    Object.keys(r).forEach((k) => {
      const colName = columnsToRename?.[k];
      if (colName) {
        r[colName] = r[k];
        delete r[k];
      }
    });
  });

  return queryResult;
}

export function jsonCompactToJsonResultSet(
  compactResultSet: ResponseJSON<unknown>,
): ResponseJSON<unknown> {
  const cols = compactResultSet.meta?.map((v) => v.name) || [];
  const objectData = compactResultSet.data.map((r) => {
    const pairs = _.zip(cols, r as unknown[]);
    return _.fromPairs(pairs);
  });
  return _.set(compactResultSet, "data", objectData);
}
