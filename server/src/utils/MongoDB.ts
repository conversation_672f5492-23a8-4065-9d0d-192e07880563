import mongoose from "mongoose";
import { DB_URI } from "./EnvConfig";

let isConnected = false;

export const connectMongoDB = async () => {
  if (isConnected) {
    return;
  }

  mongoose.set("strictQuery", true);
  await mongoose.connect(DB_URI!);

  const db = mongoose.connection;
  db.on("error", console.error.bind(console, "connection error:"));
  db.once("open", () => {
    console.log("MongoDB connected!");
    isConnected = true;
  });
};

export const disconnectMongoDB = async () => {
  if (isConnected) {
    await mongoose.disconnect();
    isConnected = false;
  }
};
