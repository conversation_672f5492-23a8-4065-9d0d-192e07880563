import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import snowflake from "snowflake-sdk";
import { ALL_LOCATIONS } from "./Constants";
import { SnowflakeAccount } from "./SnowflakeConnection";

export enum Env {
  PRODUCTION = "production",
  DEVELOPMENT = "development",
}

export enum DateFormat {
  DEFAULT = "YYYY-MM-DD HH:mm:ss",
  SHORT = "YYYY-MM-DD",
  FULL = "YYYY-MM-DD HH:mm:ss SSS Z",
  DAY_AND_TIME = "MM-DD HH:mm",
  UNIX_TIMESTAMP = "x",
}

export enum JwtExpiredIn {
  DEFAULT = 60 * 24 * 7, // 7 days
  MOBILE = 60 * 24 * 10, // 10 days
}

export enum PasswordLength {
  MIN = 4,
  MAX = 50,
}

export enum Message {
  ERROR_2FA_ALREADY = "You already set up your 2-factor authentication.",
  ERROR_2FA_NOT_YET = "You have not set up your 2FA yet.",
  ERROR_BOOKING_EXISTS = "Booking with same year, week and supplier already exists",
  ERROR_BOOKING_NOT_FOUND = "Booking not found",
  ERROR_BOOKING_STATUS_COMPLETED = "The status of this booking is completed.",
  ERROR_DATA_SOURCE_NOT_AVAILABLE = "Data source not available",
  ERROR_DATABASE_ERROR = "Database error",
  ERROR_GRID_PRICING_EXISTS = "Grid pricing with same year and week already exists",
  ERROR_ITEM_NOT_FOUND = "Item not found",
  ERROR_LINK_EXPIRED = "Your link is expired.",
  ERROR_ORG_EXISTS = "Organisation already exists",
  ERROR_ORG_NOT_FOUND = "Organisation not found",
  ERROR_OTP_TOKEN_NOT_VALID = "Your one time password is not valid.",
  ERROR_PARAMETERS_NOT_VALID = "Parameters not valid",
  ERROR_PASSWORD_WRONG = "Wrong password",
  ERROR_PERMISSION_DENIED = "Permission denied",
  ERROR_PIC_EXISTS = "PIC already exists",
  ERROR_PLUGIN_EXISTS = "Plugin already exists",
  ERROR_PLUGIN_NOT_FOUND = "Plugin not found",
  ERROR_PLUGIN_NOT_FOUND_IN_TYPES = "Plugin not found in Types.ts file",
  ERROR_ROLE_EXISTS = "Role already exists",
  ERROR_SEND_EMAIL = "There was an error when sending out email.",
  ERROR_SERVER_INTERNAL = "There was an error happened.",
  ERROR_SHARING_EXISTS = "The sharing already exists",
  ERROR_TERMS_NOT_FOUND = "Terms not found",
  ERROR_THEME_NOT_VALID = "Theme format is not a valid json string",
  ERROR_USER_EXISTS = "User already exists",
  ERROR_USER_NO_DELETE_SELF = "Please do not delete yourself",
  ERROR_USER_NOT_FOUND = "User not found",
  ERROR_USER_NOT_IN_ANY_ORG = "User does not belong to any organisation",
  ERROR_USER_NOT_VALID = "User is invalid",
  ERROR_VENDOR_NOT_FOUND = "Vendor not found",
  FAILED = "Mission failed",
  OK_INSTRUCTION_EMAIL_SENT = "Please follow the instruction in your email to continue.",
  OK_RESET_PASSWORD = "Your password has been reset successfully.",
  OK_RESET_2FA = "Your 2FA has been reset successfully. Please login to configure it.",
  OK_SHARE_INSIGHT = "You shared an insight successfully.",
  INFO_DATA_TRUNCATED = "The data returned have been truncated due to oversize.",
  SUCCESS = "Mission completed.",
  UNAUTHORIZED = "Unauthorized",
}

export enum ResultCode {
  SUCCESS = 700,
  FAILED = 701,
}
export interface ServerResponse {
  data: string | null;
  msg: Message;
  code: ResultCode;
}

export type RouteItem = {
  method: string;
  path: string;
  controller: RequestHandler;
  isAuthRequired?: boolean;
};

export type LooseObject = {
  [key: string]: any;
};

export enum PkgName {
  ADMINISTRATION = "Administration", // for super admin only
  ALLIANCE_DATA_ANALYSIS = "Alliance Data Analysis",
  DATA_ANALYSIS_OVERVIEW = "Data Analysis Overview",
  MEQ_CAMERA = "MEQ Camera",
  GMP_LAMB_BOOKINGS = "GMP Lamb Bookings",
  GMP_LAMB_DATA_ANALYSIS = "GMP Lamb Data Analysis",
  MEQ_PROBE = "MEQ Probe",
  MANAGEMENT = "Management",
  MEQ_LIVE = "MEQ Live",
  MEQ_LIVE_ADMIN = "MEQ Live Admin",
  MEQ_CAMERA_PLUS = "MEQ Camera Plus",
  AUS_MEAT_OMM = "AUS-MEAT OMM",
  DATASET_TAGGING = "Dataset Tagging",
  MEQ_CAMERA_IMAGE_SUMMARY_EXT = "MEQ Camera Image Summary Ext",
}

export type SystemEmail = {
  to: string;
  subject: string;
  html: string;
};

export enum VerificationCodeType {
  SMS = "sms",
  EMAIL = "email",
}

export enum VerificationCodeVerifyFor {
  FORGOT_PASSWORD = "forgotPassword",
  TWO_FACTOR_AUTHENTICATION = "2FA",
}

export type SnowflakeStatementResultColumn = {
  name: string;
  type: string;
  scale: number;
};

export type ExecuteSnowflakeStatementResult = {
  stmt?: snowflake.RowStatement;
  rows?: any[];
  columns?: SnowflakeStatementResultColumn[];
  numRows?: number;
  numUpdatedRows?: number;
  requestId?: string;
};

export type SnowflakeStatementParams = {
  sqlText: string;
  binds?: any[];
  db: string; // database name
  account?: SnowflakeAccount;
  parameters?: {
    MULTI_STATEMENT_COUNT?: number;
  };
};

export enum QueryType {
  ALL_TABLES = "ALL TABLES",
  ALL_VIEWS = "All Views",
  ALL_COLUMNS = "All Columns",
  DISTINCT_VALUES_OF_COLUMN = "DISTINCT_VALUES_OF_COLUMN",
  ORDINARY_QUERY = "Ordinary Query",
  ORIGINAL_QUERY = "Original Query",
}

// this is from @zitadel/node/dist/commonjs/api/generated/zitadel/user
export enum UserState {
  USER_STATE_UNSPECIFIED = 0,
  USER_STATE_ACTIVE = 1,
  USER_STATE_INACTIVE = 2,
  USER_STATE_DELETED = 3,
  USER_STATE_LOCKED = 4,
  USER_STATE_SUSPEND = 5,
  USER_STATE_INITIAL = 6,
  UNRECOGNIZED = -1,
}

export enum ZitadelOrgIdString {
  ALLIANCE = "Alliance",
  GMP = "GMP",
  JBS = "JBS",
  MEQ = "MEQ",
}

export type ZitaDelIntrospectionUser = {
  active: boolean;
  aud: string[];
  client_id: string;
  email: string;
  email_verified: boolean;
  exp: number;
  family_name: string;
  given_name: string;
  iat: number;
  iss: string;
  jti: string;
  locale: string;
  name: string;
  nbf: number;
  preferred_username: string;
  scope: string;
  sub: string;
  token_type: string;
  updated_at: number;
  username: string;
};

export type GlGridPrice = {
  rowNumber: number;
  hscwCategory: string;
  lmyCategory1: number;
  lmyCategory2: number;
  lmyCategory3: number;
  lmyCategory4: number;
  lmyCategory5: number;
};

export type GmpGridPrice = {
  rowNumber: number;
  hscwCategory: string;
  lmyCategory1: number;
  lmyCategory2: number;
};

export enum GmpBookingStatus {
  NotApplicable = "Not Applicable",
  Scheduled = "Scheduled",
  Assigned = "Assigned",
  Completed = "Completed",
}

export enum ALLIANCE_ROLE {
  ALL_LOCATIONS_ADMIN = `Alliance_${ALL_LOCATIONS}-Admin`,
  ALL_LOCATIONS_USER = `Alliance_${ALL_LOCATIONS}-User`,
  ALL_LOCATIONS_SUPPLIER = `Alliance_${ALL_LOCATIONS}-Supplier`,
  ALL_LOCATIONS_RM = `Alliance_${ALL_LOCATIONS}-RM`,
  ALL_LOCATIONS_LSR = `Alliance_${ALL_LOCATIONS}-LSR`,
}
