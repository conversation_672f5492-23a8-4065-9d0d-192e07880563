import dotenv from "dotenv";
import { z } from "zod";

dotenv.config();

const envSchema = z.object({
  NODE_ENV: z.string().nonempty(),
  SERVER_PORT: z.string().nonempty(),
  CRYPTO_SECRET: z.string().nonempty(),
  CRYPTO_ALGRORITHM: z.string().nonempty(),
  RSA_PRIVATE_KEY_SECRET: z.string().nonempty(),
  DB_URI: z.string().nonempty(),
  ELASTIC_CLOUD_APM_API_KEY: z.string().optional(),
  ELASTIC_CLOUD_APM_SERVER_URL: z.string().optional(),
  SYSTEM_EMAIL_PASSWORD: z.string().nonempty(), // for sending email using gmail directly
  SNOWFLAKE_ACCOUNT: z.string().nonempty(),
  SNOWFLAKE_ACCOUNT_ALLIANCE: z.string().nonempty(),
  SNOW<PERSON>AKE_USERNAME: z.string().nonempty(),
  SNOWFLAKE_ROLE: z.string().nonempty(),
  SNOWFLAKE_PRIVATE_KEY: z.string().nonempty(),
  SNOWFLAKE_PRIVATE_KEY_PASSPHRASE: z.string(),
  ENABLE_ALLIANCE_SUPPLIER_SYNC: z.string().optional(),
  DB_URI_V1: z.string().nonempty(),
  AUS_MEAT_OMM_API_URL: z.string().nonempty(),
  LOGTO_TENANT_ID: z.string().nonempty(),
  LOGTO_APPLICATION_ID: z.string().nonempty(),
  LOGTO_APPLICATION_SECRET: z.string().nonempty(),
  CLICKHOUSE_URL: z.string().nonempty(),
  CLICKHOUSE_USER: z.string().nonempty(),
  CLICKHOUSE_PW: z.string().nonempty(),
  DEPLOYMENT_TARGET: z.string().nonempty(),
  FEATURE_FLAG_CLICKHOUSE: z.string(),
});

export const {
  NODE_ENV,
  SERVER_PORT,
  CRYPTO_SECRET,
  CRYPTO_ALGRORITHM,
  RSA_PRIVATE_KEY_SECRET,
  DB_URI,
  ELASTIC_CLOUD_APM_API_KEY,
  ELASTIC_CLOUD_APM_SERVER_URL,
  SYSTEM_EMAIL_PASSWORD, // for sending email using gmail directly
  SNOWFLAKE_ACCOUNT,
  SNOWFLAKE_ACCOUNT_ALLIANCE,
  SNOWFLAKE_USERNAME,
  SNOWFLAKE_ROLE,
  SNOWFLAKE_PRIVATE_KEY,
  SNOWFLAKE_PRIVATE_KEY_PASSPHRASE,
  ENABLE_ALLIANCE_SUPPLIER_SYNC,
  DB_URI_V1,
  AUS_MEAT_OMM_API_URL,
  LOGTO_TENANT_ID,
  LOGTO_APPLICATION_ID,
  LOGTO_APPLICATION_SECRET,
  CLICKHOUSE_URL,
  CLICKHOUSE_USER,
  CLICKHOUSE_PW,
  DEPLOYMENT_TARGET,
  FEATURE_FLAG_CLICKHOUSE,
} = envSchema.parse(process.env);

export function isFeatureEnabled({
  deploymentTarget,
  featureFlagName,
}: {
  deploymentTarget?: "dev" | "prd";
  featureFlagName: string;
}): boolean {
  return (
    (deploymentTarget === undefined ||
      DEPLOYMENT_TARGET === deploymentTarget) &&
    process.env[`FEATURE_FLAG_${featureFlagName.toUpperCase()}`] === "1"
  );
}
