import crypto from "crypto";
import snowflake from "snowflake-sdk";
import _ from "lodash";

import {
  ExecuteSnowflakeStatementResult,
  SnowflakeStatementParams,
} from "./Types";
import {
  SNOWFLAKE_ACCOUNT,
  SNOWFLAKE_USERNAME,
  SNOWFLAKE_ROLE,
  SNOWFLAKE_ACCOUNT_ALLIANCE,
  SNOWFLAKE_PRIVATE_KEY,
  SNOWFLAKE_PRIVATE_KEY_PASSPHRASE,
} from "./EnvConfig";
import { isNotEmpty } from "./Helper";

export enum SnowflakeAccount {
  AZURE,
  AWS,
}

const snowflakeAccountEnvs = {
  [SnowflakeAccount.AZURE]: SNOWFLAKE_ACCOUNT,
  [SnowflakeAccount.AWS]: SNOWFLAKE_ACCOUNT_ALLIANCE,
};

const createConnectionPool = (sfAccount: SnowflakeAccount) => {
  const account = snowflakeAccountEnvs[sfAccount];
  if (!account) {
    throw new Error(
      `Missing environment variable for Snowflake account ${sfAccount}.`,
    );
  }

  const privateKey = crypto
    .createPrivateKey({
      key: Buffer.from(SNOWFLAKE_PRIVATE_KEY, "base64").toString("utf-8"),
      format: "pem",
      type: "pkcs8",
      encoding: "utf-8",
      passphrase: SNOWFLAKE_PRIVATE_KEY_PASSPHRASE,
    })
    .export({ format: "pem", type: "pkcs8" }) as string;

  return snowflake.createPool(
    // connection options

    {
      authenticator: "SNOWFLAKE_JWT",
      privateKey,
      account,
      username: SNOWFLAKE_USERNAME,
      role: SNOWFLAKE_ROLE,
      warehouse: "WH_MEQINSIGHTS", // This is the way we track compute usage, and is always the same for MEQ Insights
      jsTreatIntegerAsBigInt: true,
      clientSessionKeepAlive: true,
    },
    // pool options
    {
      max: sfAccount === SnowflakeAccount.AZURE ? 20 : 10, // specifies the maximum number of connections in the pool
      min: 0, // specifies the minimum number of connections in the pool
    },
  );
};

const connectionPools = {
  [SnowflakeAccount.AZURE]: createConnectionPool(SnowflakeAccount.AZURE),
  [SnowflakeAccount.AWS]: createConnectionPool(SnowflakeAccount.AWS),
};

const dbAccountMatchers: { regex: RegExp; account: SnowflakeAccount }[] = [
  { regex: /^CLIENT_ALLIANCE_.*$/i, account: SnowflakeAccount.AWS },
  { regex: /^.*$/, account: SnowflakeAccount.AZURE },
];

const getConnectionPool = _.memoize(
  (db: string) =>
    connectionPools[dbAccountMatchers.find((m) => m.regex.test(db))!.account],
);

const optimiseSnowflakeRowsData = (
  columns?: { name: string; type: string; scale: number }[],
  rows?: any[],
) => {
  if (isNotEmpty(columns) && isNotEmpty(rows)) {
    rows?.forEach((r) =>
      Object.keys(r).forEach((k) => {
        const column = columns?.find((c) => c.name === k);
        if (column?.type === "fixed" && isNotEmpty(r[k])) {
          r[k] = Number(r[k]);
        }
      }),
    );
  }

  return rows;
};

export const executeSnowflakeStatement: ({
  sqlText,
  binds,
  db,
  account,
  parameters,
}: SnowflakeStatementParams) => Promise<ExecuteSnowflakeStatementResult> = async ({
  sqlText,
  binds,
  db,
  account,
  parameters,
}: SnowflakeStatementParams) => {
  // Use the connection pool and execute a statement
  const connectionPool = account
    ? connectionPools[account]
    : getConnectionPool(db);

  return new Promise((resolve, reject) => {
    void connectionPool.use(async (clientConnection) => {
      try {
        return Promise.resolve(
          clientConnection.execute({
            sqlText,
            binds,
            parameters,
            complete: (err, stmt, rows) => {
              if (err) {
                reject(err);
                return;
              }

              if ("hasNext" in stmt && "NextResult" in stmt && stmt.hasNext()) {
                stmt.NextResult();
                return;
              }

              const columns = stmt?.getColumns()?.map((i) => ({
                name: i.getName(),
                type: i.getType(),
                scale: i.getScale(),
              }));

              resolve({
                stmt,
                rows: optimiseSnowflakeRowsData(columns, rows),
                columns,
                numRows: stmt?.getNumRows(),
                numUpdatedRows: stmt?.getNumUpdatedRows(),
                requestId: stmt?.getRequestId(),
              });
            },
          }),
        );
      } catch (err) {
        console.error(err);
        reject(new Error("Failed to execute Snowflake query."));
      }
    });
  });
};
