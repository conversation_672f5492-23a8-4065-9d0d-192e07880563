import { LRUCache } from "lru-cache";
import axios from "axios";
import { SignJWT, importPKCS8 } from "jose";
import NodeRSA from "node-rsa";
import * as Models from "../models";
import { User, ZitadelOrg } from "../models/Interfaces";
import { ZitaDelIntrospectionUser } from "./Types";
import { logExceptionToFile } from "./Helper";

const zitadelOrgCache = new LRUCache<string, ZitadelOrg>({
  max: 100, // Maximum number of items in cache
  ttl: 60 * 60 * 1000, // Cache TTL set to 60 minutes
});

const introspectTokenCache = new LRUCache<string, ZitaDelIntrospectionUser>({
  max: 1000, // Maximum number of items in cache
  ttl: 60 * 5 * 1000, // Cache TTL set to 5 minutes
});

const zitadelUserCache = new LRUCache<string, User>({
  max: 1000, // Maximum number of items in cache
  ttl: 60 * 60 * 1000, // Cache TTL set to 60 minutes
});

export const getZitadelOrg = async (zitadelOrgId: string) => {
  const cachedData = zitadelOrgCache.get(zitadelOrgId);
  if (cachedData) {
    return cachedData;
  }

  const newData: ZitadelOrg | null = await Models.ZitadelOrg.findOne({
    idString: zitadelOrgId,
  }).then((o) => o);
  if (newData) {
    zitadelOrgCache.set(zitadelOrgId, newData); // Cache the result
    return newData;
  }

  return null;
};

const createClientAssertion = async (zitadelOrg: ZitadelOrg) => {
  const rsa = new NodeRSA(zitadelOrg.introspectionKey);
  const privateKey = await importPKCS8(
    rsa.exportKey("pkcs8-private-pem"),
    "RSA256",
  );

  const jwt = await new SignJWT({
    iss: zitadelOrg.introspectionClientId,
    sub: zitadelOrg.introspectionClientId,
    aud: zitadelOrg.authority,
    exp: Math.floor(Date.now() / 1000) + 60 * 60, // 60 minutes
    iat: Math.floor(Date.now() / 1000),
  })
    .setProtectedHeader({ alg: "RS256", kid: zitadelOrg.introspectionKeyId })
    .sign(privateKey);
  return jwt;
};

export const getZitadeIntrospectTokenInfo = async ({
  zitadelOrg,
  token,
}: {
  zitadelOrg: ZitadelOrg;
  token: string;
}) => {
  const cachedData = introspectTokenCache.get(token);
  if (cachedData) {
    return cachedData;
  }

  const response = await axios.post(
    zitadelOrg.authority + "/oauth/v2/introspect",
    new URLSearchParams({
      client_assertion_type:
        "urn:ietf:params:oauth:client-assertion-type:jwt-bearer",
      client_assertion: await createClientAssertion(zitadelOrg),
      token,
    }),
    {
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
    },
  );

  const newData = response.data;
  introspectTokenCache.set(token, newData); // Cache the result
  return newData;
};

export const getUserByZitadelUserId = async (zitadelUserId: string) => {
  const cachedData = zitadelUserCache.get(zitadelUserId);
  if (cachedData) {
    return cachedData;
  }
  return refreshZitadelUserCacheByUserId(zitadelUserId);
};

export const refreshZitadelUserCacheByUserId = async (
  zitadelUserId: string,
) => {
  const newData: User | null = await Models.User.findOne({
    zitadelId: zitadelUserId,
  }).then((o) => o);
  if (newData) {
    zitadelUserCache.set(zitadelUserId, newData); // Cache the result
    return newData;
  } else {
    zitadelUserCache.delete(zitadelUserId); // Delete user from cache if he doesn't exist anymore
  }
  return null;
};
