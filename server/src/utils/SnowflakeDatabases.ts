import _ from "lodash";
import { NODE_ENV } from "./EnvConfig";
import { PkgName } from "./Types";

export type Database = {
  id: string;
  database: string;
  excludeColumns: string[];
  replaceStrings?: ReplaceString[];
};

export type ReplaceString = {
  original: string;
  replaceWith: string;
};

export const PackageDatabases: Database[] = [
  {
    id: PkgName.MEQ_CAMERA,
    database: NODE_ENV === "production" ? "MEQCAMERA_PRD" : "MEQCAMERA_DEV",
    excludeColumns: [],
    replaceStrings: [],
  },
  {
    id: PkgName.MEQ_PROBE,
    database: NODE_ENV === "production" ? "MEQPROBE_PRD" : "MEQPROBE_DEV",
    excludeColumns: ["CLIENT_NAME"],
  },
  {
    id: PkgName.MEQ_LIVE,
    database: NODE_ENV === "production" ? "MEQLIVE_PRD" : "MEQLIVE_DEV",
    excludeColumns: [],
  },
  {
    id: PkgName.MEQ_CAMERA_PLUS,
    database: NODE_ENV === "production" ? "PLUGINS_PRD" : "PLUGINS_DEV",
    excludeColumns: [],
  },
  {
    id: PkgName.MEQ_CAMERA_IMAGE_SUMMARY_EXT,
    database: NODE_ENV === "production" ? "PLUGINS_PRD" : "PLUGINS_DEV",
    excludeColumns: [],
  },
];

// Please use lower case for org and locations
const snowflakeClientDbs: {
  org: string;
  locations: [string] | "*";
  database?: string;
}[] = [];

export const findSnowflakeClientDb = _.memoize(
  (org: string, location?: string): string => {
    function appendDbEnv(db: string): string {
      return NODE_ENV === "production" ? `${db}_PRD` : `${db}_DEV`;
    }
    const lowerCaseOrg = org.toLowerCase();
    const lowerCaseLoc = location?.toLowerCase();
    const found = snowflakeClientDbs.find(
      (clientDb) =>
        clientDb.org === lowerCaseOrg &&
        (clientDb.locations === "*" ||
          clientDb.locations.some((l) => l === lowerCaseLoc)),
    );

    return appendDbEnv(
      !found || !found.database ? `CLIENT_${org}` : found.database,
    );
  },
);

export function findPackageDb(pkg: PkgName): string {
  const db = PackageDatabases.find((d) => d.id === pkg.toString())?.database;
  if (db === undefined) {
    throw new Error(`Database not found for ${pkg}`);
  }
  return db;
}
