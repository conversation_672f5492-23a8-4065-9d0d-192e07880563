import { describe, expect, it } from "@jest/globals";
import {
  ALL_LOCATIONS,
  getOrgIdStringComponents,
  hasAccessToOrg,
  INVALID_ORG,
  SUPER_ADMIN,
  SUPER_USER,
} from "./Helper";

describe("hasAccessToOrg", () => {
  // Test super user access
  it("should grant access to SUPER_ADMIN for any org", () => {
    expect(hasAccessToOrg([SUPER_ADMIN], "Alliance_Dannevirke")).toBe(true);
    expect(hasAccessToOrg([SUPER_ADMIN], "GMP")).toBe(true);
    expect(hasAccessToOrg([SUPER_ADMIN], "MEQ")).toBe(true);
  });

  it("should grant access to SUPER_USER for any org", () => {
    expect(hasAccessToOrg([SUPER_USER], "Alliance_Dannevirke")).toBe(true);
    expect(hasAccessToOrg([SUPER_USER], "GMP")).toBe(true);
    expect(hasAccessToOrg([SUPER_USER], "MEQ")).toBe(true);
  });

  // Test AllLocations access
  it("should grant access to all locations when user has AllLocations role", () => {
    const roles = ["Alliance_AllLocations-RM"];
    expect(hasAccessToOrg(roles, "Alliance_Dannevirke")).toBe(true);
    expect(hasAccessToOrg(roles, "Alliance_Levin")).toBe(true);
    expect(hasAccessToOrg(roles, "Alliance_AllLocations")).toBe(true);
    expect(hasAccessToOrg(roles, "GMP")).toBe(false); // Different org
  });

  // Test specific location access
  it("should only grant access to specific location when user has location-specific role", () => {
    const roles = ["Alliance_Dannevirke-RM"];
    expect(hasAccessToOrg(roles, "Alliance_Dannevirke")).toBe(true);
    expect(hasAccessToOrg(roles, "Alliance_Levin")).toBe(false);
    expect(hasAccessToOrg(roles, "Alliance_AllLocations")).toBe(false);
  });

  // Test orgs without locations
  it("should handle orgs without locations correctly", () => {
    const roles = ["GMP-RM"];
    expect(hasAccessToOrg(roles, "GMP")).toBe(true);
    expect(hasAccessToOrg(roles, "Alliance_Dannevirke")).toBe(false);
  });

  // Test multiple roles
  it("should grant access if user has any matching role", () => {
    const roles = ["Alliance_Dannevirke-RM", "GMP-RM"];
    expect(hasAccessToOrg(roles, "Alliance_Dannevirke")).toBe(true);
    expect(hasAccessToOrg(roles, "GMP")).toBe(true);
    expect(hasAccessToOrg(roles, "Alliance_Levin")).toBe(false);
  });

  // Test invalid roles
  it("should handle invalid roles gracefully", () => {
    const roles = ["InvalidRole"];
    expect(hasAccessToOrg(roles, "Alliance_Dannevirke")).toBe(false);
    expect(hasAccessToOrg(roles, "GMP")).toBe(false);
  });

  // Test empty roles
  it("should handle empty roles array", () => {
    expect(hasAccessToOrg([], "Alliance_Dannevirke")).toBe(false);
    expect(hasAccessToOrg([], "GMP")).toBe(false);
  });
});

describe("getOrgIdStringComponents", () => {
  it("should parse orgIdString with location correctly", () => {
    const result = getOrgIdStringComponents("Alliance_Dannevirke");
    expect(result).toEqual({
      org: "Alliance",
      location: "Dannevirke",
    });
  });

  it("should use ALL_LOCATIONS when no location is specified", () => {
    const result = getOrgIdStringComponents("GMP");
    expect(result).toEqual({
      org: "GMP",
      location: ALL_LOCATIONS,
    });
  });

  it("should handle empty string", () => {
    const result = getOrgIdStringComponents("");
    expect(result).toEqual({
      org: INVALID_ORG,
      location: ALL_LOCATIONS,
    });
  });

  it("should handle orgIdString with multiple underscores", () => {
    const result = getOrgIdStringComponents("Alliance_Location_Extra");
    expect(result).toEqual({
      org: "Alliance",
      location: "Location_Extra",
    });
  });

  it("should handle Alliance_AllLocations correctly", () => {
    const result = getOrgIdStringComponents("Alliance_AllLocations");
    expect(result).toEqual({
      org: "Alliance",
      location: ALL_LOCATIONS,
    });
  });
});
