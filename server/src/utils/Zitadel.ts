import {
  createAccessTokenInterceptor,
  createAdminClient,
  createAuthClient,
  createManagementClient,
  createOidcClient,
  createOrganizationClient,
  createSettingsClient,
  createSystemClient,
  createUserClient,
} from "@zitadel/node/api";
import { getZitadelOrg } from "./Cache";

export const getZitadelAdminClient = async (zitadelOrgId: string) => {
  const zitadelOrg = await getZitadelOrg(zitadelOrgId);
  if (zitadelOrg) {
    return createAdminClient(
      zitadelOrg.authority,
      createAccessTokenInterceptor(
        zitadelOrg.serviceAccountPersonalAccessToken,
      ),
    );
  }
  return null;
};

export const getZitadelAuthClient = async (zitadelOrgId: string) => {
  const zitadelOrg = await getZitadelOrg(zitadelOrgId);
  if (zitadelOrg) {
    return createAuthClient(
      zitadelOrg.authority,
      createAccessTokenInterceptor(
        zitadelOrg.serviceAccountPersonalAccessToken,
      ),
    );
  }
  return null;
};

export const getZitadelManagementClient = async (zitadelOrgId: string) => {
  const zitadelOrg = await getZitadelOrg(zitadelOrgId);
  if (zitadelOrg) {
    return createManagementClient(
      zitadelOrg.authority,
      createAccessTokenInterceptor(
        zitadelOrg.serviceAccountPersonalAccessToken,
      ),
    );
  }
  return null;
};

export const getZitadelOidcClient = async (zitadelOrgId: string) => {
  const zitadelOrg = await getZitadelOrg(zitadelOrgId);
  if (zitadelOrg) {
    return createOidcClient(
      zitadelOrg.authority,
      createAccessTokenInterceptor(
        zitadelOrg.serviceAccountPersonalAccessToken,
      ),
    );
  }
  return null;
};

export const getZitadelOrganisationClient = async (zitadelOrgId: string) => {
  const zitadelOrg = await getZitadelOrg(zitadelOrgId);
  if (zitadelOrg) {
    return createOrganizationClient(
      zitadelOrg.authority,
      createAccessTokenInterceptor(
        zitadelOrg.serviceAccountPersonalAccessToken,
      ),
    );
  }
  return null;
};

export const getZitadelSettingsClient = async (zitadelOrgId: string) => {
  const zitadelOrg = await getZitadelOrg(zitadelOrgId);
  if (zitadelOrg) {
    return createSettingsClient(
      zitadelOrg.authority,
      createAccessTokenInterceptor(
        zitadelOrg.serviceAccountPersonalAccessToken,
      ),
    );
  }
  return null;
};

export const getZitadelSystemClient = async (zitadelOrgId: string) => {
  const zitadelOrg = await getZitadelOrg(zitadelOrgId);
  if (zitadelOrg) {
    return createSystemClient(
      zitadelOrg.authority,
      createAccessTokenInterceptor(
        zitadelOrg.serviceAccountPersonalAccessToken,
      ),
    );
  }
  return null;
};

export const getZitadelUserClient = async (zitadelOrgId: string) => {
  const zitadelOrg = await getZitadelOrg(zitadelOrgId);
  if (zitadelOrg) {
    return createUserClient(
      zitadelOrg.authority,
      createAccessTokenInterceptor(
        zitadelOrg.serviceAccountPersonalAccessToken,
      ),
    );
  }
  return null;
};
