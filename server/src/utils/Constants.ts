export const VERIFICATION_CODE_EXPIRE_IN_MINUTES = 10;
export const VERIFICATION_CODE_SAMPLE = "0123456789";
export const VERIFICATION_SERVICE_OPEN_FOR = [];

export const SUPER_ADMIN = "AllOrgs-SuperAdmin";
export const SUPER_USER = "AllOrgs-SuperUser";
export const ADMIN = "Admin"; // manage all resources for the org

export const QUERY_DATA_SOURCE_ENDPOINT = "GetDataSource";
export const QUERY_RESPONSE_VOLUMN_THRESHOLD = 20000;

export const ORG_JSON_PROPERTIES = [
  "_id",
  "createdAt",
  "updatedAt",
  "country",
  "zitadelOrgIdString",
  "idString",
  "displayName",
  "description",
  "isTCRequired",
  "pkgs",
  "isValid",
  "authority",
  "projectResourceId",
  "reactClientId",
  "reactRedirectUri",
];

export const ALL = "All";
export const ALL_COMPONENTS = "AllComponents";
export const ALL_LOCATIONS = "AllLocations";
export const ALL_ORGS = "AllOrgs";
export const ALL_PAGES = "AllPages";
export const ALL_PKGS = "AllPKGs";
export const INVALID_ORG = "INVALID_ORG";
export const MEQ_ORG = "MEQ";

export const GMP_ADMIN_EMAIL = "<EMAIL>";
