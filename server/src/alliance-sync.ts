import _ from "lodash";

import * as Models from "./models";
import { ALLIANCE_ROLE, LooseObject } from "./utils/Types";
import { delay } from "./utils/Helper";
import { executeSnowflakeStatement } from "./utils/SnowflakeConnection";
import { getZitadelManagementClient } from "./utils/Zitadel";
import { zitadelOrgIdString } from "./controllers/Alliance/shared";
import { findSnowflakeClientDb } from "./utils/SnowflakeDatabases";
import { ENABLE_ALLIANCE_SUPPLIER_SYNC } from "./utils/EnvConfig";

async function readUsersFromSnowflake() {
  const sanitiseValue = (v: string) => v?.trim()?.toLowerCase();

  const db = findSnowflakeClientDb(zitadelOrgIdString);
  // get all lamb users from snowflake
  const lambUsersFromSnowflake = await executeSnowflakeStatement({
    sqlText: `SELECT * FROM TABLE(${db}.MEQINSIGHTS.ALLIANCE_LAMB_GET_ALL_USER_INFO());`,
    db,
  });

  // get all beef users from snowflake
  const beefUsersFromSnowflake = await executeSnowflakeStatement({
    sqlText: `SELECT * FROM TABLE(${db}.MEQINSIGHTS.ALLIANCE_BEEF_GET_ALL_USER_INFO());`,
    db,
  });

  const allLambSuppliers: LooseObject[] =
    lambUsersFromSnowflake.rows?.filter((i) =>
      sanitiseValue(i.SUPPLIER_EMAIL),
    ) || [];
  const allBeefSuppliers: LooseObject[] =
    beefUsersFromSnowflake.rows?.filter((i) =>
      sanitiseValue(i.SUPPLIER_EMAIL),
    ) || [];

  // get all uniq suppliers
  const allUniqSuppliers = _.uniq(
    allLambSuppliers
      .filter((i) => sanitiseValue(i.SUPPLIER_EMAIL))
      .map((i) => sanitiseValue(i.SUPPLIER_EMAIL))
      .concat(
        allBeefSuppliers
          .filter((i) => sanitiseValue(i.SUPPLIER_EMAIL))
          .map((i) => sanitiseValue(i.SUPPLIER_EMAIL)),
      ),
  );

  // get all uniq RMs
  const allUniqRMs = _.uniq(
    allLambSuppliers
      .filter((i) => sanitiseValue(i.RM_EMAIL))
      .map((i) => sanitiseValue(i.RM_EMAIL))
      .concat(
        allBeefSuppliers
          .filter((i) => sanitiseValue(i.RM_EMAIL))
          .map((i) => sanitiseValue(i.RM_EMAIL)),
      ),
  );

  // get all uniq LSRs
  const allUniqLSRs = _.uniq(
    allLambSuppliers
      .filter((i) => sanitiseValue(i.LSR_EMAIL))
      .map((i) => sanitiseValue(i.LSR_EMAIL))
      .concat(
        allBeefSuppliers
          .filter((i) => sanitiseValue(i.LSR_EMAIL))
          .map((i) => sanitiseValue(i.LSR_EMAIL)),
      ),
  );

  // prepare data for suppliers
  let preparedSupplierData: LooseObject[] = [];
  if (allUniqSuppliers.length > 0) {
    preparedSupplierData = allUniqSuppliers.map((email) => {
      const dataForCurrentEmailLamb = allLambSuppliers.filter(
        (i) => sanitiseValue(i.SUPPLIER_EMAIL) === email,
      );
      const dataForCurrentEmailBeef = allBeefSuppliers.filter(
        (i) => sanitiseValue(i.SUPPLIER_EMAIL) === email,
      );
      let names: string[] | undefined = [];
      let allianceLambBusinessNames = [];
      let allianceBeefBusinessNames = [];
      let allianceLambSupplierNumbers = [];
      let allianceBeefSupplierNumbers = [];
      if (dataForCurrentEmailLamb.length > 0) {
        names = dataForCurrentEmailLamb[0].SUPPLIER_USER_NAME?.split(" ");
        const bizNameOfSupplierNos = _.fromPairs(
          dataForCurrentEmailLamb.map((d) => [
            d.SUPPLIER_NO,
            d.SUPPLIER_COMPANY_NAME,
          ]),
        );
        allianceLambSupplierNumbers = _.uniq(
          dataForCurrentEmailLamb.map((i) => i.SUPPLIER_NO),
        );
        allianceLambBusinessNames = allianceLambSupplierNumbers.map(
          (sn) => bizNameOfSupplierNos[sn],
        );
      }
      if (dataForCurrentEmailBeef.length > 0) {
        if (!names || names.length === 0) {
          names = dataForCurrentEmailBeef[0].SUPPLIER_USER_NAME?.split(" ");
        }
        const bizNameOfSupplierNos = _.fromPairs(
          dataForCurrentEmailBeef.map((d) => [
            d.SUPPLIER_NO,
            d.SUPPLIER_COMPANY_NAME,
          ]),
        );
        allianceBeefSupplierNumbers = _.uniq(
          dataForCurrentEmailBeef.map((i) => i.SUPPLIER_NO),
        );
        allianceBeefBusinessNames = allianceBeefSupplierNumbers.map(
          (sn) => bizNameOfSupplierNos[sn],
        );
      }

      return {
        email,
        firstName: Array.isArray(names) && names.length > 0 ? names[0] : "N/A",
        lastName:
          Array.isArray(names) && names.length > 1
            ? names[names.length - 1]
            : "N/A",
        allianceLambSupplierNumbers,
        allianceLambBusinessNames,
        allianceBeefSupplierNumbers,
        allianceBeefBusinessNames,
        allianceLambLSRs: [],
        allianceBeefLSRs: [],
        allianceLambSuppliers: [],
        allianceBeefSuppliers: [],
        allianceLambViewAllowed: dataForCurrentEmailLamb.length > 0,
        allianceBeefViewAllowed: dataForCurrentEmailBeef.length > 0,
        roles: [ALLIANCE_ROLE.ALL_LOCATIONS_SUPPLIER], // make sure to create this role and its permissions before use
      };
    });
  }

  // prepare data for RMs
  let preparedRMData: LooseObject[] = [];
  if (allUniqRMs.length > 0) {
    preparedRMData = allUniqRMs.map((email) => {
      const dataForCurrentEmailLamb = allLambSuppliers.filter(
        (i) => sanitiseValue(i.RM_EMAIL) === email,
      );
      const dataForCurrentEmailBeef = allBeefSuppliers.filter(
        (i) => sanitiseValue(i.RM_EMAIL) === email,
      );
      let names: string[] | undefined = [];
      if (dataForCurrentEmailLamb.length > 0) {
        names = dataForCurrentEmailLamb[0].RM_NAME?.split(" ");
      }
      if (
        (!names || names.length === 0) &&
        dataForCurrentEmailBeef.length > 0
      ) {
        names = dataForCurrentEmailBeef[0].RM_NAME?.split(" ");
      }

      return {
        email,
        firstName: Array.isArray(names) && names.length > 0 ? names[0] : "N/A",
        lastName:
          Array.isArray(names) && names.length > 1
            ? names[names.length - 1]
            : "N/A",
        allianceLambViewAllowed: dataForCurrentEmailLamb.length > 0,
        allianceBeefViewAllowed: dataForCurrentEmailBeef.length > 0,
        allianceLambSupplierNumbers: [],
        allianceBeefSupplierNumbers: [],
        allianceLambLSRs: _.uniq(
          dataForCurrentEmailLamb
            .filter((i) => sanitiseValue(i.LSR_EMAIL))
            .map((i) => i.LSR_EMAIL),
        ),
        allianceBeefLSRs: _.uniq(
          dataForCurrentEmailBeef
            .filter((i) => sanitiseValue(i.LSR_EMAIL))
            .map((i) => i.LSR_EMAIL),
        ),
        allianceLambSuppliers: [],
        allianceBeefSuppliers: [],
        roles: [ALLIANCE_ROLE.ALL_LOCATIONS_RM],
      };
    });
  }

  // prepare data for LSRs
  let preparedLSRData: LooseObject[] = [];
  if (allUniqLSRs.length > 0) {
    preparedLSRData = allUniqLSRs.map((email) => {
      const dataForCurrentEmailLamb = allLambSuppliers.filter(
        (i) => sanitiseValue(i.LSR_EMAIL) === email,
      );
      const dataForCurrentEmailBeef = allBeefSuppliers.filter(
        (i) => sanitiseValue(i.LSR_EMAIL) === email,
      );
      let names: string[] | undefined = [];
      if (dataForCurrentEmailLamb.length > 0) {
        names = dataForCurrentEmailLamb[0].LSR_NAME?.split(" ");
      }
      if (
        (!names || names.length === 0) &&
        dataForCurrentEmailBeef.length > 0
      ) {
        names = dataForCurrentEmailBeef[0].LSR_NAME?.split(" ");
      }

      return {
        email,
        firstName: Array.isArray(names) && names.length > 0 ? names[0] : "N/A",
        lastName:
          Array.isArray(names) && names.length > 1
            ? names[names.length - 1]
            : "N/A",
        allianceLambViewAllowed: dataForCurrentEmailLamb.length > 0,
        allianceBeefViewAllowed: dataForCurrentEmailBeef.length > 0,
        allianceLambSupplierNumbers: [],
        allianceBeefSupplierNumbers: [],
        allianceLambLSRs: [],
        allianceBeefLSRs: [],
        allianceLambSuppliers: _.uniq(
          dataForCurrentEmailLamb
            .filter((i) => sanitiseValue(i.SUPPLIER_EMAIL))
            .map((i) => i.SUPPLIER_EMAIL),
        ),
        allianceBeefSuppliers: _.uniq(
          dataForCurrentEmailBeef
            .filter((i) => sanitiseValue(i.SUPPLIER_EMAIL))
            .map((i) => i.SUPPLIER_EMAIL),
        ),
        roles: [ALLIANCE_ROLE.ALL_LOCATIONS_LSR],
      };
    });
  }

  // update the user info to database
  return preparedSupplierData.concat(preparedLSRData).concat(preparedRMData);
}

async function updateUsers(preparedData: LooseObject[]) {
  for (let i = 0; i < preparedData.length; i++) {
    const item = preparedData[i];
    // check if the user is already in our system
    const existingUser = await Models.User.findOne({ email: item.email });
    if (existingUser) {
      if (existingUser.isValid) {
        // update values from snowflake
        await Models.User.findOneAndUpdate({ email: item.email }, item);
      } else {
        // delete invalid user. Only delete it from mongodb as if mistaken we can easily add it back
        await Models.User.findOneAndDelete({ email: item.email });
      }
    } else {
      // create new user
      try {
        // create new account
        const target = await (
          await getZitadelManagementClient(zitadelOrgIdString)
        )?.addHumanUser({
          userName: item.email,
          profile: {
            firstName: item.firstName,
            lastName: item.lastName,
            preferredLanguage: "en",
          },
          email: { email: item.email },
        });
        if (target && target.userId) {
          await new Models.User({
            ...item,
            zitadelOrgIdString,
            zitadelId: target.userId,
            zitadelUsername: item.email,
            displayName: `${item.firstName} ${item.lastName}`,
            isValid: true,
          })
            .save()
            .then(() => {
              console.log("Created new user in zitadel " + item.email);
            });
        }
      } catch (error) {
        if ((error as any).message?.includes("User already exists")) {
          const client = await getZitadelManagementClient(zitadelOrgIdString);
          const existingZitadelUserResponse = await client?.listUsers({
            queries: [{ emailQuery: { emailAddress: item.email } }],
          });

          if (
            existingZitadelUserResponse &&
            existingZitadelUserResponse.result &&
            existingZitadelUserResponse.result.length > 0
          ) {
            await new Models.User({
              ...item,
              zitadelOrgIdString,
              zitadelId: existingZitadelUserResponse.result[0].id,
              zitadelUsername: item.email,
              displayName: `${item.firstName} ${item.lastName}`,
              roles: item.roles,
              isValid: true,
            })
              .save()
              .then(() => {
                console.log("Created new user in db " + item.email);
              });
          }
        }
      }
      await delay(200); // to avoid zitadel limit
    }
  }
}

if (ENABLE_ALLIANCE_SUPPLIER_SYNC === "1") {
  console.log("Alliance user sync enabled.");
  readUsersFromSnowflake()
    .then((preparedData) => updateUsers(preparedData))
    .catch((e) => console.error(e))
    .finally(() => {
      console.log("Alliance user sync completed.");
      process.exit(0);
    });
} else {
  console.log(
    "Alliance user sync disabled. Only reading users from snowflake and printing the result.",
  );
  readUsersFromSnowflake()
    .then((preparedData) => console.log(JSON.stringify(preparedData)))
    .catch((e) => console.error(e))
    .finally(() => {
      process.exit(0);
    });
}
