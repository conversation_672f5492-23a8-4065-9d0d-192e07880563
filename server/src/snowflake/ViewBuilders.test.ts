import snowflake from "snowflake-sdk";
// Configure snowflake before tests
snowflake.configure({ logLevel: "ERROR" });

import { ALL } from "../utils/Constants";
import { ViewBuilder, ViewParams } from "./ViewBuilders";

// Create a concrete class to test the abstract ViewBuilder
class TestViewBuilder extends ViewBuilder {
  build(params: ViewParams): Promise<string> {
    return Promise.resolve("");
  }
}

describe("ViewBuilder", () => {
  let viewBuilder: TestViewBuilder;

  beforeEach(() => {
    viewBuilder = new TestViewBuilder();
  });

  describe("checkLot", () => {
    it("should return true when lot is 'ALL'", () => {
      const params: ViewParams = {
        name: "test",
        isFunction: true,
        orgId: "test",
        db: "test",
        lot: ALL,
      };
      expect(viewBuilder.checkLot(params)).toBe(true);
    });

    it("should return true when lot is a valid number", () => {
      const params: ViewParams = {
        name: "test",
        isFunction: true,
        orgId: "test",
        db: "test",
        lot: "123",
      };
      expect(viewBuilder.checkLot(params)).toBe(true);
    });

    it("should return false when lot is undefined", () => {
      const params: ViewParams = {
        name: "test",
        isFunction: true,
        orgId: "test",
        db: "test",
      };
      expect(viewBuilder.checkLot(params)).toBe(false);
    });

    it("should return false when lot is not a valid number", () => {
      const params: ViewParams = {
        name: "test",
        isFunction: true,
        orgId: "test",
        db: "test",
        lot: "abc",
      };
      expect(viewBuilder.checkLot(params)).toBe(false);
    });
  });
});
