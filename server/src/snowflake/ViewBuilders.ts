import { User } from "../models/Interfaces";
import { ALL } from "../utils/Constants";
import { GMP_ROLE } from "../utils/Gmp";
import {
  getDbLocationFromOrgIdString,
  getDbLocationFromOrgLocation,
  getOrgFromOrgIdString,
  hasPermission,
  isLocationAllowedByOrgIdString,
} from "../utils/Helper";
import _ from "lodash";
import { querySuppliersForUser } from "../controllers/Alliance";
import { Message } from "../utils/Types";

export interface ViewParams {
  name: string;
  isFunction: boolean;
  orgId: string;
  db: string;
  startDate?: string;
  endDate?: string;
  location?: string;
  supplier?: string;
  sheet?: string;
  pic?: string;
  lot?: string;
}

export const QUERY_PARAMS_CHECK_FAILED = "Query parameter check failed.";

export abstract class ViewBuilder {
  isFunction: boolean;

  private dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  private supplierRegex = /^\d+$/;
  private killsheetRegex = /^\d+$/;
  private lotRegex = /^\d+$/;

  checkDate(params: ViewParams, key: "startDate" | "endDate"): boolean {
    return !!params[key] && this.dateRegex.test(params[key]);
  }

  checkIsFunction(params: ViewParams): boolean {
    return params.isFunction === this.isFunction;
  }

  checkLocation(params: ViewParams): boolean {
    return isLocationAllowedByOrgIdString(params.location, params.orgId);
  }

  async checkSupplier(
    params: ViewParams,
    user: User,
    isBeef: boolean,
  ): Promise<boolean> {
    if (!params.supplier || !this.supplierRegex.test(params.supplier)) {
      return false;
    }
    const result = await querySuppliersForUser(params.location!, user, isBeef);
    if (result.msg !== Message.SUCCESS || !Array.isArray(result.data)) {
      return false;
    }
    if (!result.data.some((d) => d.SUPPLIER_NO === params.supplier)) {
      return false;
    }
    return true;
  }

  checkSheet(params: ViewParams): boolean {
    return !!params.sheet && this.killsheetRegex.test(params.sheet);
  }

  checkPic(params: ViewParams, user: User, isAllAllowed: boolean): boolean {
    if (params.pic === undefined) {
      return false;
    }

    if (hasPermission({ roles: user.roles, requiredRoles: [GMP_ROLE.ADMIN] })) {
      return true;
    }

    if (isAllAllowed && params.pic === ALL) {
      return true;
    }

    return (
      !_.isEmpty(user.gmpPics) &&
      user.gmpPics.find((gp) => gp === params.pic) !== undefined
    );
  }

  checkLot(params: ViewParams): boolean {
    return (
      !!params.lot && (params.lot === ALL || this.lotRegex.test(params.lot))
    );
  }

  abstract build(params: ViewParams, user: User): Promise<string>;
}

class MeqProbeEodRawVb extends ViewBuilder {
  static view = "MEQPROBE_EOD_RAW";

  constructor() {
    super();
    this.isFunction = true;
  }

  build(params: ViewParams): Promise<string> {
    if (
      this.checkIsFunction(params) &&
      this.checkDate(params, "startDate") &&
      this.checkDate(params, "endDate")
    ) {
      return Promise.resolve(
        `TABLE(${params.db}.MEQINSIGHTS.${MeqProbeEodRawVb.view}('${params.startDate}', '${params.endDate}', '${getOrgFromOrgIdString(
          params.orgId,
        )}', '${getDbLocationFromOrgIdString(params.orgId)}'))`,
      );
    }
    throw new Error(QUERY_PARAMS_CHECK_FAILED);
  }
}

class AllianceLambGetAggMobBrandDataVb extends ViewBuilder {
  static view = "ALLIANCE_LAMB_GET_AGG_MOB_BRAND_DATA";

  constructor() {
    super();
    this.isFunction = true;
  }

  build(params: ViewParams): Promise<string> {
    if (
      this.checkIsFunction(params) &&
      this.checkDate(params, "startDate") &&
      this.checkDate(params, "endDate")
    ) {
      return Promise.resolve(
        `TABLE(${params.db}.MEQINSIGHTS.${AllianceLambGetAggMobBrandDataVb.view}('${params.startDate}', '${params.endDate}', '${getDbLocationFromOrgIdString(
          params.orgId,
        )}'))`,
      );
    }
    throw new Error(QUERY_PARAMS_CHECK_FAILED);
  }
}

class AllianceLambGetIndividualCarcaseDataVb extends ViewBuilder {
  static view = "ALLIANCE_LAMB_GET_INDIVIDUAL_CARCASE_DATA";

  constructor() {
    super();
    this.isFunction = true;
  }

  build(params: ViewParams): Promise<string> {
    if (
      this.checkIsFunction(params) &&
      this.checkDate(params, "startDate") &&
      this.checkDate(params, "endDate") &&
      this.checkLocation(params)
    ) {
      return Promise.resolve(
        `TABLE(${params.db}.MEQINSIGHTS.${AllianceLambGetIndividualCarcaseDataVb.view}('${params.startDate}', '${params.endDate}', '${getDbLocationFromOrgLocation(
          params.location!,
        )}', '${ALL}', '${ALL}'))`,
      );
    }
    throw new Error(QUERY_PARAMS_CHECK_FAILED);
  }
}

class AllianceLambGetIndividualDataForFarmerKillsheetVb extends ViewBuilder {
  static view = "ALLIANCE_LAMB_GET_INDIVIDUAL_DATA_FOR_FARMER_KILLSHEET";

  constructor() {
    super();
    this.isFunction = true;
  }

  async build(params: ViewParams, user: User): Promise<string> {
    if (
      this.checkIsFunction(params) &&
      this.checkDate(params, "startDate") &&
      this.checkDate(params, "endDate") &&
      this.checkLocation(params) &&
      (await this.checkSupplier(params, user, false))
    ) {
      return `TABLE(${params.db}.MEQINSIGHTS.${AllianceLambGetIndividualDataForFarmerKillsheetVb.view}('${params.startDate}', '${params.endDate}', '${getDbLocationFromOrgLocation(
        params.location!,
      )}', ${params.supplier}, '${ALL}'))`;
    }
    throw new Error(QUERY_PARAMS_CHECK_FAILED);
  }
}

class AllianceLambGetAggOnSupplierKillsheetVb extends ViewBuilder {
  static view = "ALLIANCE_LAMB_GET_AGG_ON_SUPPLIER_KILLSHEET";

  constructor() {
    super();
    this.isFunction = true;
  }

  async build(params: ViewParams, user: User): Promise<string> {
    if (
      this.checkIsFunction(params) &&
      this.checkDate(params, "startDate") &&
      this.checkDate(params, "endDate") &&
      this.checkLocation(params) &&
      (await this.checkSupplier(params, user, false)) &&
      this.checkSheet(params)
    ) {
      return `TABLE(${params.db}.MEQINSIGHTS.${AllianceLambGetAggOnSupplierKillsheetVb.view}('${params.startDate}', '${params.endDate}', '${getDbLocationFromOrgLocation(
        params.location!,
      )}', ${params.supplier}, '${params.sheet}'))`;
    }
    throw new Error(QUERY_PARAMS_CHECK_FAILED);
  }
}

class AllianceLambGetGridReportKillsheetVb extends ViewBuilder {
  static view = "ALLIANCE_LAMB_GET_GRID_REPORT_KILLSHEET";

  constructor() {
    super();
    this.isFunction = true;
  }

  async build(params: ViewParams, user: User): Promise<string> {
    if (
      this.checkIsFunction(params) &&
      this.checkDate(params, "startDate") &&
      this.checkLocation(params) &&
      (await this.checkSupplier(params, user, false)) &&
      this.checkSheet(params)
    ) {
      return `TABLE(${params.db}.MEQINSIGHTS.${AllianceLambGetGridReportKillsheetVb.view}('${params.startDate}', '${getDbLocationFromOrgLocation(
        params.location!,
      )}', ${params.supplier}, ${params.sheet}))`;
    }
    throw new Error(QUERY_PARAMS_CHECK_FAILED);
  }
}

class AllianceBeefGetIndividualDataForFarmerKillsheetVb extends ViewBuilder {
  static view = "ALLIANCE_BEEF_GET_INDIVIDUAL_DATA_FOR_FARMER_KILLSHEET";

  constructor() {
    super();
    this.isFunction = true;
  }

  async build(params: ViewParams, user: User): Promise<string> {
    if (
      this.checkIsFunction(params) &&
      this.checkDate(params, "startDate") &&
      this.checkDate(params, "endDate") &&
      this.checkLocation(params) &&
      (await this.checkSupplier(params, user, true))
    ) {
      return `TABLE(${params.db}.MEQINSIGHTS.${AllianceBeefGetIndividualDataForFarmerKillsheetVb.view}('${params.startDate}', '${params.endDate}', '${getDbLocationFromOrgLocation(
        params.location!,
      )}', ${params.supplier}, '${ALL}'))`;
    }
    throw new Error(QUERY_PARAMS_CHECK_FAILED);
  }
}

class AllianceBeefGetAggOnSupplierKillsheetVb extends ViewBuilder {
  static view = "ALLIANCE_BEEF_GET_AGG_ON_SUPPLIER_KILLSHEET";

  constructor() {
    super();
    this.isFunction = true;
  }

  async build(params: ViewParams, user: User): Promise<string> {
    if (
      this.checkIsFunction(params) &&
      this.checkDate(params, "startDate") &&
      this.checkDate(params, "endDate") &&
      this.checkLocation(params) &&
      (await this.checkSupplier(params, user, true)) &&
      this.checkSheet(params)
    ) {
      return `TABLE(${params.db}.MEQINSIGHTS.${AllianceBeefGetAggOnSupplierKillsheetVb.view}('${params.startDate}', '${params.endDate}', '${getDbLocationFromOrgLocation(
        params.location!,
      )}', ${params.supplier}, '${params.sheet}'))`;
    }
    throw new Error(QUERY_PARAMS_CHECK_FAILED);
  }
}

class AllianceBeefGetGridReportKillsheetVb extends ViewBuilder {
  static view = "ALLIANCE_BEEF_GET_GRID_REPORT_KILLSHEET";

  constructor() {
    super();
    this.isFunction = true;
  }

  async build(params: ViewParams, user: User): Promise<string> {
    if (
      this.checkIsFunction(params) &&
      this.checkDate(params, "startDate") &&
      this.checkLocation(params) &&
      (await this.checkSupplier(params, user, true)) &&
      this.checkSheet(params)
    ) {
      return `TABLE(${params.db}.MEQINSIGHTS.${AllianceBeefGetGridReportKillsheetVb.view}('${params.startDate}', '${getDbLocationFromOrgLocation(
        params.location!,
      )}', ${params.supplier}, ${params.sheet}))`;
    }
    throw new Error(QUERY_PARAMS_CHECK_FAILED);
  }
}

class AllianceLambGetGlobalPercentilesVb extends ViewBuilder {
  static view = "ALLIANCE_LAMB_GET_GLOBAL_PERCENTILES";

  constructor() {
    super();
    this.isFunction = true;
  }

  build(params: ViewParams): Promise<string> {
    if (this.checkIsFunction(params) && this.checkLocation(params)) {
      return Promise.resolve(
        `TABLE(${params.db}.MEQINSIGHTS.${AllianceLambGetGlobalPercentilesVb.view}('${getDbLocationFromOrgLocation(
          params.location!,
        )}'))`,
      );
    }
    throw new Error(QUERY_PARAMS_CHECK_FAILED);
  }
}

class GmpGetIndividualCarcasesVb extends ViewBuilder {
  static view = "GMP_GET_INDIVIDUAL_CARCASES";

  constructor() {
    super();
    this.isFunction = true;
  }

  build(params: ViewParams, user: User): Promise<string> {
    if (
      this.checkIsFunction(params) &&
      this.checkDate(params, "startDate") &&
      this.checkDate(params, "endDate") &&
      this.checkPic(params, user, false) &&
      this.checkLot(params)
    ) {
      return Promise.resolve(
        `TABLE(${params.db}.MEQINSIGHTS.${GmpGetIndividualCarcasesVb.view}('${params.startDate}', '${params.endDate}', '${params.pic}', '${params.lot}'))`,
      );
    }
    throw new Error(QUERY_PARAMS_CHECK_FAILED);
  }
}

class GmpListPicsVb extends ViewBuilder {
  static view = "GMP_LIST_PICS";

  constructor() {
    super();
    this.isFunction = true;
  }

  build(params: ViewParams): Promise<string> {
    if (
      this.checkIsFunction(params) &&
      this.checkDate(params, "startDate") &&
      this.checkDate(params, "endDate")
    ) {
      return Promise.resolve(
        `TABLE(${params.db}.MEQINSIGHTS.${GmpListPicsVb.view}('${params.startDate}', '${params.endDate}'))`,
      );
    }
    throw new Error(QUERY_PARAMS_CHECK_FAILED);
  }
}

class GmpGetAggCarcasesVb extends ViewBuilder {
  static view = "GMP_GET_AGG_CARCASES";

  constructor() {
    super();
    this.isFunction = true;
  }

  build(params: ViewParams, user: User): Promise<string> {
    if (
      this.checkIsFunction(params) &&
      this.checkDate(params, "startDate") &&
      this.checkDate(params, "endDate") &&
      this.checkPic(params, user, true) &&
      this.checkLot(params)
    ) {
      return Promise.resolve(
        `TABLE(${params.db}.MEQINSIGHTS.${GmpGetAggCarcasesVb.view}('${params.startDate}', '${params.endDate}', '${params.pic}', '${params.lot}'))`,
      );
    }
    throw new Error(QUERY_PARAMS_CHECK_FAILED);
  }
}

class GmpProducerGetGlobalPercentilesVb extends ViewBuilder {
  static view = "GMP_PRODUCER_GET_GLOBAL_PERCENTILES";

  constructor() {
    super();
    this.isFunction = true;
  }

  build(params: ViewParams): Promise<string> {
    if (this.checkIsFunction(params)) {
      return Promise.resolve(
        `TABLE(${params.db}.MEQINSIGHTS.${GmpProducerGetGlobalPercentilesVb.view}())`,
      );
    }
    throw new Error(QUERY_PARAMS_CHECK_FAILED);
  }
}

class GmpProducerLotGridReportVb extends ViewBuilder {
  static view = "GMP_PRODUCER_LOT_GRID_REPORT";

  constructor() {
    super();
    this.isFunction = true;
  }

  build(params: ViewParams, user: User): Promise<string> {
    if (
      this.checkIsFunction(params) &&
      this.checkDate(params, "startDate") &&
      this.checkPic(params, user, false) &&
      this.checkLot(params)
    ) {
      return Promise.resolve(
        `TABLE(${params.db}.MEQINSIGHTS.${GmpProducerLotGridReportVb.view}('${params.startDate}', '${params.pic}', '${params.lot}'))`,
      );
    }
    throw new Error(QUERY_PARAMS_CHECK_FAILED);
  }
}

export const VIEW_BUILDERS = {
  [MeqProbeEodRawVb.view]: new MeqProbeEodRawVb(),
  [AllianceLambGetAggMobBrandDataVb.view]:
    new AllianceLambGetAggMobBrandDataVb(),
  [AllianceLambGetIndividualCarcaseDataVb.view]:
    new AllianceLambGetIndividualCarcaseDataVb(),
  [AllianceLambGetIndividualDataForFarmerKillsheetVb.view]:
    new AllianceLambGetIndividualDataForFarmerKillsheetVb(),
  [AllianceLambGetAggOnSupplierKillsheetVb.view]:
    new AllianceLambGetAggOnSupplierKillsheetVb(),
  [AllianceLambGetGridReportKillsheetVb.view]:
    new AllianceLambGetGridReportKillsheetVb(),
  [AllianceBeefGetIndividualDataForFarmerKillsheetVb.view]:
    new AllianceBeefGetIndividualDataForFarmerKillsheetVb(),
  [AllianceBeefGetAggOnSupplierKillsheetVb.view]:
    new AllianceBeefGetAggOnSupplierKillsheetVb(),
  [AllianceBeefGetGridReportKillsheetVb.view]:
    new AllianceBeefGetGridReportKillsheetVb(),
  [AllianceLambGetGlobalPercentilesVb.view]:
    new AllianceLambGetGlobalPercentilesVb(),
  [GmpGetIndividualCarcasesVb.view]: new GmpGetIndividualCarcasesVb(),
  [GmpListPicsVb.view]: new GmpListPicsVb(),
  [GmpGetAggCarcasesVb.view]: new GmpGetAggCarcasesVb(),
  [GmpProducerGetGlobalPercentilesVb.view]:
    new GmpProducerGetGlobalPercentilesVb(),
  [GmpProducerLotGridReportVb.view]: new GmpProducerLotGridReportVb(),
};
