import * as mdb from "mongoose";
import { z } from "zod";

const isoDateValidationSchema = z.string().date();

export interface IAusMeatOmmSettings {
  apiKey: string;
  goLiveDate: string;
  establishmentNumber: string;
  chainNumber: number;
  timeZoneName: string;
}

export interface ICameraPlusSettings {
  topBrands: string[];
}

export interface ICameraSettings {
  image: {
    traits: string[];
    defaultSortingTrait: string;
    defaultSortingAscending: boolean;
  };
  summary: {
    colsBlackList: string[];
    ext: {
      projectionCols: string[];
      joiningCols: string[];
    };
  };
  crossOrgShare: {
    org: string;
    location: string;
    note: string;
  }[];
}

export interface IDatasetTaggingSettings {
  overrides: string[];
  tags: string[];
}

export interface IProbeSettings {
  pages?: ("DASHBOARD" | "DEVICE" | "BEEF_SUMMARY" | "LAMB_SUMMARY")[];
}

export interface IUserSettings {
  org: string;
  location: string;
  ausMeatOmm?: IAusMeatOmmSettings;
  cameraPlus?: ICameraPlusSettings;
  camera?: ICameraSettings;
  datasetTagging?: IDatasetTaggingSettings;
  probe?: IProbeSettings;
}

const ausMeatOmmSchema = new mdb.Schema<IAusMeatOmmSettings>({
  apiKey: {
    type: String,
    required: false,
  },
  goLiveDate: {
    type: String,
    required: true,
    validate: {
      validator: (v) => isoDateValidationSchema.safeParse(v).success,
      message: (props) =>
        `Property ${props.path} has invalid ISO date value "${props.value}".`,
    },
  },
  establishmentNumber: {
    type: String,
    required: true,
  },
  chainNumber: {
    type: Number,
    required: true,
  },
  timeZoneName: {
    type: String,
    required: true,
  },
});

const cameraPlusSchema = new mdb.Schema<ICameraPlusSettings>({
  topBrands: Array<{
    type: "String";
    required: true;
  }>,
});

const cameraSchema = new mdb.Schema<ICameraSettings>({
  image: new mdb.Schema<ICameraSettings["image"]>(
    {
      traits: Array<{
        type: mdb.Schema.Types.String;
      }>,
      defaultSortingTrait: mdb.Schema.Types.String,
      defaultSortingAscending: mdb.Schema.Types.Boolean,
    },
    {
      _id: false,
    },
  ),
  summary: new mdb.Schema<ICameraSettings["summary"]>(
    {
      colsBlackList: Array<{
        type: mdb.Schema.Types.String;
      }>,
      ext: {
        type: new mdb.Schema<ICameraSettings["summary"]["ext"]>({
          projectionCols: {
            type: [String],
            required: true,
          },
          joiningCols: {
            type: [String],
            required: true,
          },
        }),
      },
    },
    {
      _id: false,
    },
  ),
  crossOrgShare: [
    new mdb.Schema<ICameraSettings["crossOrgShare"][number]>({
      org: {
        type: mdb.Schema.Types.String,
        required: true,
      },
      location: {
        type: mdb.Schema.Types.String,
        required: true,
      },
      note: {
        type: mdb.Schema.Types.String,
        required: true,
      },
    }),
  ],
});

const datasetTaggingSchema = new mdb.Schema<IDatasetTaggingSettings>({
  overrides: Array<{
    type: mdb.Schema.Types.String;
  }>,
  tags: Array<{
    type: mdb.Schema.Types.String;
  }>,
});

const probeSchema = new mdb.Schema<IProbeSettings>(
  {
    pages: {
      type: [String],
      enum: ["DASHBOARD", "DEVICE", "BEEF_SUMMARY", "LAMB_SUMMARY"],
      required: false,
    },
  },
  { _id: false },
);

const userSettingsSchema = new mdb.Schema<IUserSettings>({
  org: {
    type: String,
    required: true,
    index: true,
  },
  location: {
    type: String,
    required: true,
    index: true,
  },
  ausMeatOmm: {
    type: ausMeatOmmSchema,
    required: false,
    _id: false,
  },
  cameraPlus: {
    type: cameraPlusSchema,
    required: false,
    _id: false,
  },
  camera: {
    type: cameraSchema,
    _id: false,
  },
  datasetTagging: {
    type: datasetTaggingSchema,
    _id: false,
  },
  probe: {
    type: probeSchema,
    required: false,
    _id: false,
  },
});

export const UserSettings = mdb.model<IUserSettings>(
  "user-settings",
  userSettingsSchema,
);
