import * as mdb from "mongoose";
import { z } from "zod";

const isoDateValidationSchema = z.string().date();
const isoDateTimeValidationSchema = z.string().datetime({ offset: true });
const integerValidationSchema = z.number().int();

export const MEASUREMENT_TYPES = [
  { columnSuffix: "MSA", code: "msaBovineMarbling" },
  { columnSuffix: "AUSMB", code: "ausmeatBovineMarbling" },
  { columnSuffix: "FC", code: "ausmeatBovineFatColour" },
  { columnSuffix: "MC", code: "ausmeatBovineMeatColour" },
  { columnSuffix: "EMA", code: "ausmeatBovineEyeMuscleArea" },
] as const;

export const NO_VALUE_RECORD = "NVR";

export interface IMeasurementsDatum {
  rmsccMeasurementSerialNumber: string; // TODO: add sql column.
  rmsccSoftwareVersion: string;
  rmsccMeasurementDateTime: string; // measurementDateTime
  rmsccMeasurementType: string; // the * suffix
  rmsccMeasurementDevice: string; // measurementDevice* (should not be null)
  rmsccMeasurementAssigned: string; // measurementAssigned*
  rmsccMeasurementOperator: string;
  rmsccMeasurementOperatorType: string;
  rmsccMeasurementHuman: string; // measurementHuman*
  rmsccActionReason: string | null; // "Override" if both device reading & human reading exist, otherwise null
}

export interface IDeviceDatum {
  rmsccEquipmentBrand: string;
  rmsccEquipmentModel: string;
  rmsccEquipmentSerialNumber: string; // equipmentSerialNumber. Group by this for device items
  rmsccHardwareVersion: string;
  rmsccFirmwareVersion: string;
  rmsccEquipmentSerialIncrement: string;
  rmsccMeasurementsData: IMeasurementsDatum[];
}

export interface ICarcaseDatum {
  ausmeatBodyNumber: number; // bodyNumber. Group by this for carcase items
  rmsccDeviceData: IDeviceDatum[];
}

export interface IOmmMessage {
  rmsccMessageType: {
    rmsccPayloadName: string;
    rmsccVersionNumber: string;
    rmsccAuthority: string;
    rmsccIndustryDataStandard: string;
  };
  rmsccBeginningOfMessage: {
    rmsccMessageGUID: string; // to generate by us
    rmsccMessageVersionIndicator: string;
    rmsccMessageFunction: string;
    ausmeatSpecies: string;
    ausmeatEstablishmentNumber: string;
    rmsccKillDate: string; // killDate
    iscChainNumber: number;
    ausmeatKillLot: string; // killLot
    ausmeatLotCount: number;
  };
  rmsccCarcaseData: ICarcaseDatum[];
}

const ommMessageSchema = new mdb.Schema<IOmmMessage>(
  {
    rmsccMessageType: {
      rmsccPayloadName: { type: String, required: true },
      rmsccVersionNumber: { type: String, required: true },
      rmsccAuthority: { type: String, required: true },
      rmsccIndustryDataStandard: { type: String, required: true },
    },
    rmsccBeginningOfMessage: {
      rmsccMessageGUID: { type: String, required: true },
      rmsccMessageVersionIndicator: { type: String, required: true },
      rmsccMessageFunction: { type: String, required: true },
      rmsccKillDate: { type: String, required: true },
      ausmeatEstablishmentNumber: { type: String, required: true },
      iscChainNumber: {
        type: Number,
        required: true,
        validate: {
          validator: (v) => integerValidationSchema.safeParse(v).success,
          message: (props) =>
            `Property ${props.path} has invalid value "${props.value}".`,
        },
      },
      ausmeatSpecies: { type: String, required: true },
      ausmeatKillLot: { type: String, required: true },
      ausmeatLotCount: {
        type: Number,
        required: true,
        validate: {
          validator: (v) => integerValidationSchema.safeParse(v).success,
          message: (props) =>
            `Property ${props.path} has invalid value "${props.value}".`,
        },
      },
    },
    rmsccCarcaseData: [
      new mdb.Schema<ICarcaseDatum>(
        {
          ausmeatBodyNumber: {
            type: Number,
            required: true,
            validate: {
              validator: (v: number) =>
                integerValidationSchema.safeParse(v).success,
              message: (props) =>
                `Property ${props.path} has invalid value "${props.value}".`,
            },
          },
          rmsccDeviceData: [
            new mdb.Schema<IDeviceDatum>(
              {
                rmsccEquipmentBrand: { type: String, required: true },
                rmsccEquipmentModel: { type: String, required: true },
                rmsccEquipmentSerialNumber: { type: String, required: true },
                rmsccHardwareVersion: { type: String, required: true },
                rmsccFirmwareVersion: { type: String, required: true },
                rmsccEquipmentSerialIncrement: { type: String, required: true },
                rmsccMeasurementsData: [
                  new mdb.Schema<IMeasurementsDatum>(
                    {
                      rmsccMeasurementSerialNumber: {
                        type: String,
                        required: true,
                      },
                      rmsccMeasurementOperator: {
                        type: String,
                      },
                      rmsccMeasurementOperatorType: {
                        type: String,
                        required: true,
                      },
                      rmsccMeasurementDateTime: {
                        type: String,
                        required: true,
                        validate: {
                          validator: (v) =>
                            isoDateTimeValidationSchema.safeParse(v).success,
                          message: (props) =>
                            `Property ${props.path} has invalid value "${props.value}".`,
                        },
                      },
                      rmsccMeasurementType: { type: String, required: true },
                      rmsccMeasurementAssigned: {
                        type: String,
                        required: true,
                      },
                      rmsccMeasurementDevice: {
                        type: String,
                        required: true,
                      },
                      rmsccMeasurementHuman: {
                        type: String,
                        required: true,
                      },
                      rmsccActionReason: { type: String },
                      rmsccSoftwareVersion: { type: String, required: true },
                    },
                    { _id: false },
                  ),
                ],
              },
              { _id: false },
            ),
          ],
        },
        { _id: false },
      ),
    ],
  },
  { _id: false },
);

export interface IOmmFile {
  messages: IOmmMessage[];
}

const ommFileSchema = new mdb.Schema({
  messages: [ommMessageSchema],
});

export const OmmFile = mdb.model<IOmmFile>("omm-files", ommFileSchema);

export interface IOmmFileRecord {
  org: string;
  location: string;
  fileDate: string;
  ommFileIdSent: string | undefined;
  ommFilesGenerated: {
    ommFileId: string;
    generatedAt: Date;
  }[];
}

const ommFileRecordSchema = new mdb.Schema<IOmmFileRecord>({
  org: {
    type: String,
    required: true,
    index: true,
  },
  location: {
    type: String,
    required: true,
    index: true,
  },
  fileDate: {
    type: String,
    require: true,
    index: true,
    validate: {
      validator: (v) => isoDateValidationSchema.safeParse(v).success,
      message: (props) =>
        `Property ${props.path} has invalid ISO date value "${props.value}".`,
    },
  },
  ommFileIdSent: {
    type: String,
    required: false,
  },
  ommFilesGenerated: [
    new mdb.Schema(
      {
        ommFileId: {
          type: String,
          required: true,
        },
        generatedAt: {
          type: Date,
          required: true,
        },
      },
      { _id: false },
    ),
  ],
});

export const OmmFileRecord = mdb.model<IOmmFileRecord>(
  "omm-file-records",
  ommFileRecordSchema,
);

export interface IOmmMessagePreset {
  rmsccMessageType: {
    rmsccPayloadName: string;
    rmsccVersionNumber: string;
    rmsccAuthority: string;
    rmsccIndustryDataStandard: string;
  };
  rmsccBeginningOfMessage: {
    rmsccMessageVersionIndicator: string;
    rmsccMessageFunction: string;
    ausmeatSpecies: string;
  };
  device: {
    rmsccEquipmentBrand: string;
    rmsccEquipmentModel: string;
    rmsccHardwareVersion: string;
    rmsccFirmwareVersion: string;
    rmsccEquipmentSerialIncrement: string;
  };
  measurements: {
    rmsccSoftwareVersion: string;
    rmsccMeasurementOperatorType: string;
  };
}

const ommMessagePresetSchema = new mdb.Schema<IOmmMessagePreset>({
  rmsccMessageType: {
    rmsccPayloadName: { type: String, required: true },
    rmsccVersionNumber: { type: String, required: true },
    rmsccAuthority: { type: String, required: true },
    rmsccIndustryDataStandard: { type: String, required: true },
  },
  rmsccBeginningOfMessage: {
    rmsccMessageVersionIndicator: { type: String, required: true },
    rmsccMessageFunction: { type: String, required: true },
    ausmeatSpecies: { type: String, required: true },
  },
  device: {
    rmsccEquipmentBrand: { type: String, required: true },
    rmsccEquipmentModel: { type: String, required: true },
    rmsccHardwareVersion: { type: String, required: true },
    rmsccFirmwareVersion: { type: String, required: true },
    rmsccEquipmentSerialIncrement: { type: String, required: true },
  },
  measurements: {
    rmsccSoftwareVersion: { type: String, required: true },
    rmsccMeasurementOperatorType: { type: String, required: true },
  },
});

export const OmmMessagePreset = mdb.model<IOmmMessagePreset>(
  "omm-message-presets",
  ommMessagePresetSchema,
);

export interface IOmmApiLogItem {
  timestamp: Date;
  org: string;
  location: string;
  fileDate: string;
  ommFileId: string;
  ommMessageIndex: number;
  response: {
    status: number;
    text: string;
  };
}

const ommApiLogItemSchema = new mdb.Schema<IOmmApiLogItem>({
  timestamp: { type: Date, required: true },
  org: { type: String, required: true },
  location: { type: String, required: true },
  fileDate: { type: String, required: true },
  ommFileId: { type: String, required: true },
  ommMessageIndex: { type: Number, required: true },
  response: {
    status: { type: Number, required: true },
    data: { type: mdb.Schema.Types.Mixed, required: true },
  },
});

export const OmmApiLogItem = mdb.model<IOmmApiLogItem>(
  "omm-api-log",
  ommApiLogItemSchema,
);
