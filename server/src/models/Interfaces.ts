import { GlGridPrice, GmpGridPrice, LooseObject } from "../utils/Types";

export interface VerificationCode {
  createdAt: Date;
  type: string;
  verifyFor: string;
  sendTo: string;
  code: string;
  ip: string;
  isValid: boolean;
  messageId: string;
}

export interface User {
  createdAt: Date;
  updatedAt: Date;
  zitadelOrgIdString: string;
  zitadelId: string;
  zitadelUsername: string;
  email: string;
  firstName: string;
  lastName: string;
  displayName: string; // display only
  lastAccessAt: Date;
  roles: string[];
  gmpBusinessName: string; // for gmp agent and producers only
  gmpPics: string[]; // for gmp agent and producers only
  gmpPicsWithLots: { pic: string; lots: string[] }[]; // for gmp agent only
  allianceLambSupplierNumbers: string[]; // for alliance agent and suppliers only
  allianceLambBusinessNames: string[];
  allianceBeefSupplierNumbers: string[]; // for alliance agent and suppliers only
  allianceBeefBusinessNames: string[];
  allianceLambLSRs: string[]; // for alliance RM only
  allianceBeefLSRs: string[]; // for alliance RM only
  allianceLambSuppliers: string[]; // for alliance LSR only
  allianceBeefSuppliers: string[]; // for alliance LSR only
  allianceBeefViewAllowed: boolean; // for alliance agent and suppliers only
  allianceLambViewAllowed: boolean; // for alliance agent and suppliers only
  isValid: boolean;
  termsOfServiceAcceptanceTimestamp: string;
}

export interface Role {
  createdAt: Date;
  updatedAt: Date;
  idString: string;
  displayName: string;
  description: string;
}

export interface Permission {
  createdAt: Date;
  updatedAt: Date;
  role: string; // role idString
  pkgs: string[]; // pkg idString
  pages: { id: string; components: string[] }[];
  isWrite: boolean; // has write permission
  description: string;
}

export type OrgCountry = "AU" | "NZ" | "US";

export interface Org {
  createdAt: Date;
  updatedAt: Date;
  zitadelOrgIdString: string; // which zitadel org it belongs to
  idString: string;
  country: OrgCountry;
  displayName: string;
  description: string;
  isTCRequired: boolean;
  pkgs: string[];
  isValid: boolean;
}

export interface ZitadelOrg {
  createdAt: Date;
  updatedAt: Date;
  idString: string; // the org idString
  authority: string; // the dedicated Zitadel url

  // create a project called Insights
  projectResourceId: string;

  // create an application called React
  reactClientId: string;
  reactRedirectUri: string;

  // create an application called Node With Keys. It is for validating access tokens from users
  //# ---- Zitadel private keys to validate introspection Start----
  introspectionAppId: string;
  introspectionKeyId: string;
  introspectionKey: string;
  introspectionClientId: string;
  // ---- Zitadel private keys to validate introspection End----

  // create a service account called machine for managing users
  serviceAccountPersonalAccessToken: string;
}

export interface Exception {
  createdAt: Date;
  updatedAt: Date;
  text: string;
  isResolved: boolean;
}

export interface ActionLog {
  createdAt: Date;
  username: string;
  firstName: string;
  lastName: string;
  roles: string[];
  route: string;
  params: LooseObject;
}

export interface GmpPic {
  createdAt: Date;
  updatedAt: Date;
  pic: string;
  businessName: string;
  isAgent: boolean;
}

export enum GmpGridStatus {
  ACTIVE = "ACTIVE",
  DELETED = "DELETED",
}

export enum GmpGridType {
  GL = "GL",
  GMP = "GMP",
}

export interface GmpGrid {
  createdAt: Date;
  updatedAt: Date;
  gridType: GmpGridType;
  name: string;
  status: GmpGridStatus;
}

export interface GmpGridPricing {
  createdAt: Date;
  updatedAt: Date;
  year: number;
  week: number;
  prices: GlGridPrice[];
  gridId: string;
  gridType: GmpGridType;
}

export interface GmpSettings {
  createdAt: Date;
  updatedAt: Date;
  weeklyLimit: number;
}

export interface GmpWeeklySettings {
  createdAt: Date;
  updatedAt: Date;
  year: number;
  week: number;
  weeklyLimit: number;
}

export interface GmpBooking {
  createdAt: Date;
  updatedAt: Date;
  year: number;
  week: number;
  bookingDate: string;
  quantity: number;
  isAgent: boolean;
  vendorEmail: string;
  vendorFirstName: string;
  vendorLastName: string;
  vendorBusinessName: string;
  status: string;
  bookedByEmail: string;
  bookedByFirstName: string;
  bookedByLastName: string;
  gridId: string;
  isValid: boolean;
}

export interface GmpSupplyAgreement {
  _id: string;
  createdAt: Date;
  updatedAt: Date;
  bookingId: string;
  year: number;
  week: number;
  agreedDeliveryDate: string;
  agreedQuantity: number;
  isAgent: boolean;
  pic: string;
  producerEmail: string;
  producerFirstName: string;
  producerLastName: string;
  producerBusinessName: string;
  breed: string;
  estimatedAverageWeightKg: number;
  woolLengthMm: number;
  finishingNutrition: string;
  ageMonths: number;
  bloodlines: string;
  nvdNumber: string;
  isMuelsed: boolean;
  isMsaAccredited: boolean;
  isComplyWithLPA: boolean;
  isGrassFed: boolean;
  isFreeRange: boolean;
  isAntibioticFree: boolean;
  isHormoneAndGrowthPromotantFree: boolean;
  isValid: boolean;
  sex: string;
  msaNumber: string;
}
