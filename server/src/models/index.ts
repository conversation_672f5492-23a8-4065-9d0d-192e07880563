import mongoose from "mongoose";
import mongoosePaginate from "mongoose-paginate-v2";
import { VERIFICATION_CODE_EXPIRE_IN_MINUTES } from "../utils/Constants";
import {
  ActionLog as IActionLog,
  Exception as IException,
  GmpBooking as IGmpBooking,
  GmpGrid as IGmpGrid,
  GmpGridPricing as IGmpGridPricing,
  GmpGridStatus,
  GmpGridType,
  GmpPic as IGmpPic,
  GmpSettings as IGmpSettings,
  GmpSupplyAgreement as IGmpSupplyAgreement,
  GmpWeeklySettings as IGmpWeeklySettings,
  Org as IOrg,
  Permission as IPermission,
  Role as IRole,
  User as IUser,
  VerificationCode as IVerificationCode,
  ZitadelOrg as IZitadelOrg,
} from "./Interfaces";

const Schema = mongoose.Schema;

// ------------ common starts ------------
const VerificationCodeSchema = new Schema<IVerificationCode>({
  createdAt: {
    type: Date,
    default: Date.now,
    index: { expires: `${VERIFICATION_CODE_EXPIRE_IN_MINUTES}m` },
  },
  type: { type: String, trim: true },
  verifyFor: { type: String, trim: true },
  sendTo: String,
  code: String,
  ip: String,
  isValid: { type: Boolean, default: false },
  messageId: String,
});
const VerificationCode = mongoose.model(
  "verificationCode",
  VerificationCodeSchema,
);

// ------------ user starts ------------

const UserSchema = new Schema<IUser>(
  {
    zitadelOrgIdString: { type: String, default: "MEQ" },
    zitadelId: String,
    zitadelUsername: String,
    email: { type: String, trim: true, lowercase: true },
    firstName: String,
    lastName: String,
    displayName: String, // display only
    lastAccessAt: Date,
    roles: [String], // idString of roles
    gmpBusinessName: String, // for gmp agent and producers only
    gmpPics: [String], // for gmp agent and producers only
    gmpPicsWithLots: [{ pic: String, lots: [String] }], // for gmp agent only
    allianceLambSupplierNumbers: [String], // for alliance agent and suppliers only
    allianceLambBusinessNames: [String],
    allianceBeefSupplierNumbers: [String], // for alliance agent and suppliers only
    allianceBeefBusinessNames: [String],
    allianceLambLSRs: [String], // for alliance RM only
    allianceBeefLSRs: [String], // for alliance RM only
    allianceLambSuppliers: [String], // for alliance LSR only
    allianceBeefSuppliers: [String],
    allianceBeefViewAllowed: { type: Boolean, default: false },
    allianceLambViewAllowed: { type: Boolean, default: false },
    isValid: { type: Boolean, default: false },
    termsOfServiceAcceptanceTimestamp: String,
  },
  { timestamps: true },
);
const User = mongoose.model("user", UserSchema);

const RoleSchema = new Schema<IRole>(
  {
    idString: String,
    displayName: String,
    description: String,
  },
  { timestamps: true },
);
const Role = mongoose.model("role", RoleSchema);

const PermissionSchema = new Schema<IPermission>(
  {
    role: String, // role idString
    pkgs: [String], // pkg idStrings
    pages: [{ id: String, components: [String] }],
    isWrite: { type: Boolean, default: false }, // has write permission
    description: String,
  },
  { timestamps: true },
);
const Permission = mongoose.model("permission", PermissionSchema);

const OrgSchema = new Schema<IOrg>(
  {
    zitadelOrgIdString: { type: String, default: "MEQ" }, // which zitadel org it belongs to
    idString: String,
    country: String, // for display date format
    displayName: String,
    description: String,
    isTCRequired: { type: Boolean, default: false },
    pkgs: { type: [String], default: [] }, // idStrings from pkg
    isValid: { type: Boolean, default: false },
  },
  { timestamps: true },
);
const Org = mongoose.model("org", OrgSchema);

const ZitadelOrgSchema = new Schema<IZitadelOrg>(
  {
    idString: String,
    authority: String,
    projectResourceId: String,
    reactClientId: String,
    reactRedirectUri: String,
    introspectionAppId: String,
    introspectionKeyId: String,
    introspectionKey: String,
    introspectionClientId: String,
    serviceAccountPersonalAccessToken: String,
  },
  { timestamps: true },
);
const ZitadelOrg = mongoose.model("zitadelOrg", ZitadelOrgSchema);

const ExceptionSchema = new Schema<IException>(
  {
    text: String,
    isResolved: { type: Boolean, default: false },
  },
  {
    capped: { size: 1024000, max: 100000, autoIndexId: true },
    timestamps: true,
  }, // size in bytes
);
ExceptionSchema.plugin(mongoosePaginate);
const Exception = mongoose.model("exception", ExceptionSchema);

const ActionLogSchema = new Schema<IActionLog>(
  {
    username: String,
    firstName: String,
    lastName: String,
    roles: [String],
    route: String,
    params: Object, // a query string or json object
  },
  {
    capped: { size: ********, max: 1000000, autoIndexId: true },
    timestamps: true,
  }, // size in bytes, 50MB
);
ExceptionSchema.plugin(mongoosePaginate);
const ActionLog = mongoose.model("actionLog", ActionLogSchema);

// ------------ Client specifics start ------------
const GmpPicSchema = new Schema<IGmpPic>(
  {
    pic: String,
    businessName: String,
    isAgent: { type: Boolean, default: false },
  },
  { timestamps: true },
);
const GmpPic = mongoose.model("gmpPic", GmpPicSchema);

const GmpGridSchema = new Schema<IGmpGrid>(
  {
    gridType: { type: String, enum: Object.keys(GmpGridType) },
    name: String,
    status: { type: String, enum: Object.keys(GmpGridStatus) },
  },
  { timestamps: true },
);
const GmpGrid = mongoose.model("gmpGrid", GmpGridSchema);

const GmpGridPricingSchema = new Schema<IGmpGridPricing>(
  {
    year: Number,
    week: Number,
    prices: [Object],
    gridId: String,
  },
  { timestamps: true },
);
const GmpGridPricing = mongoose.model("gmpGridPricing", GmpGridPricingSchema);

const GmpSettingsSchema = new Schema<IGmpSettings>(
  {
    weeklyLimit: Number,
  },
  { timestamps: true },
);
const GmpSettings = mongoose.model("gmpSettings", GmpSettingsSchema);

const GmpWeeklySettingsSchema = new Schema<IGmpWeeklySettings>(
  {
    year: Number,
    week: Number,
    weeklyLimit: Number,
  },
  { timestamps: true },
);
const GmpWeeklySettings = mongoose.model(
  "gmpWeeklySettings",
  GmpWeeklySettingsSchema,
);

const GmpBookingSchema = new Schema<IGmpBooking>(
  {
    year: Number,
    week: Number,
    bookingDate: String,
    quantity: Number,
    isAgent: Boolean,
    vendorEmail: String,
    vendorFirstName: String,
    vendorLastName: String,
    vendorBusinessName: String,
    status: String,
    bookedByEmail: String,
    bookedByFirstName: String,
    bookedByLastName: String,
    gridId: String,
    isValid: Boolean,
  },
  { timestamps: true },
);
const GmpBooking = mongoose.model("gmpBooking", GmpBookingSchema);

const GmpSupplyAgreementSchema = new Schema<IGmpSupplyAgreement>(
  {
    bookingId: String,
    year: Number,
    week: Number,
    agreedDeliveryDate: String,
    agreedQuantity: Number,
    isAgent: Boolean,
    pic: String,
    producerEmail: String,
    producerFirstName: String,
    producerLastName: String,
    producerBusinessName: String,
    breed: String,
    estimatedAverageWeightKg: Number,
    woolLengthMm: Number,
    finishingNutrition: String,
    ageMonths: Number,
    bloodlines: String,
    nvdNumber: String,
    isMuelsed: Boolean,
    isMsaAccredited: Boolean,
    isComplyWithLPA: Boolean,
    isGrassFed: Boolean,
    isFreeRange: Boolean,
    isAntibioticFree: Boolean,
    isHormoneAndGrowthPromotantFree: Boolean,
    isValid: Boolean,
    sex: String,
    msaNumber: String,
  },
  { timestamps: true },
);
const GmpSupplyAgreement = mongoose.model(
  "gmpSupplyAgreement",
  GmpSupplyAgreementSchema,
);
// ------------ Client specifics end ------------

export {
  ActionLog,
  Exception,
  GmpBooking,
  GmpGrid,
  GmpGridPricing,
  GmpGridStatus,
  GmpPic,
  GmpSettings,
  GmpSupplyAgreement,
  GmpWeeklySettings,
  Org,
  Permission,
  Role,
  User,
  VerificationCode,
  ZitadelOrg,
};
