import mongoose from "mongoose";

// Interfaces
export interface IProducerOfTheYearScoringConfig {
  org: string;
  consignment: {
    target: number;
    weight: number;
  };
  glqAvg: {
    target: number;
    weight: number;
  };
  glqStddev: {
    target: number;
    weight: number;
  };
  hscwAvg: {
    target: number;
    weight: number;
  };
  hscwStddev: {
    target: number;
    weight: number;
  };
  minConsignmentCount: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IGridComplianceScoringConfig {
  org: string;
  minHeadPerConsignment: number;
  minConsignmentCount: number;
  hscw: {
    min: number;
    max: number;
    weight: number;
  };
  lmy: {
    min: number;
    max: number;
    weight: number;
  };
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IGLQScoreScoringConfig {
  org: string;
  minHeadPerConsignment: number;
  minConsignmentCount: number;
  createdAt?: Date;
  updatedAt?: Date;
}

// Schemas
const consignmentScoreSchema = new mongoose.Schema(
  {
    target: { type: Number, required: true },
    weight: { type: Number, required: true },
  },
  { _id: false },
);

const rangeScoreSchema = new mongoose.Schema(
  {
    min: { type: Number, required: true },
    max: { type: Number, required: true },
    weight: { type: Number, required: true },
  },
  { _id: false },
);

const ProducerOfTheYearScoringConfigSchema =
  new mongoose.Schema<IProducerOfTheYearScoringConfig>(
    {
      org: { type: String, required: true, index: true },
      consignment: { type: consignmentScoreSchema, required: true },
      glqAvg: { type: consignmentScoreSchema, required: true },
      glqStddev: { type: consignmentScoreSchema, required: true },
      hscwAvg: { type: consignmentScoreSchema, required: true },
      hscwStddev: { type: consignmentScoreSchema, required: true },
      minConsignmentCount: { type: Number, required: true },
    },
    { timestamps: true },
  );

const GridComplianceScoringConfigSchema =
  new mongoose.Schema<IGridComplianceScoringConfig>(
    {
      org: { type: String, required: true, index: true },
      minHeadPerConsignment: { type: Number, required: true },
      minConsignmentCount: { type: Number, required: true },
      hscw: { type: rangeScoreSchema, required: true },
      lmy: { type: rangeScoreSchema, required: true },
    },
    { timestamps: true },
  );

const GLQScoreScoringConfigSchema = new mongoose.Schema<IGLQScoreScoringConfig>(
  {
    org: { type: String, required: true, index: true },
    minHeadPerConsignment: { type: Number, required: true },
    minConsignmentCount: { type: Number, required: true },
  },
  { timestamps: true },
);

// Ensure unique org per config type
ProducerOfTheYearScoringConfigSchema.index({ org: 1 }, { unique: true });
GridComplianceScoringConfigSchema.index({ org: 1 }, { unique: true });
GLQScoreScoringConfigSchema.index({ org: 1 }, { unique: true });

// Models
export const ProducerOfTheYearScoringConfig =
  mongoose.model<IProducerOfTheYearScoringConfig>(
    "gmpawardsconfig-producer-of-the-year",
    ProducerOfTheYearScoringConfigSchema,
  );

export const GridComplianceScoringConfig =
  mongoose.model<IGridComplianceScoringConfig>(
    "gmpawardsconfig-grid-compliance",
    GridComplianceScoringConfigSchema,
  );

export const GLQScoreScoringConfig = mongoose.model<IGLQScoreScoringConfig>(
  "gmpawardsconfig-glq-score",
  GLQScoreScoringConfigSchema,
);
