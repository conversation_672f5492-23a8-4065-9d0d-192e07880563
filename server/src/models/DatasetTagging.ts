import * as mdb from "mongoose";

export interface DatasetTaggingEvent {
  dataset: "camera" | "live";
  originId: {
    companyLocation?: string;
    meatType?: string;
    barcode?: string;
    photoDateTime?: string;
  };
  tagKey: string;
  tagValue?: string | number | boolean | object;
  eventAction: "create" | "delete";
  eventTime: Date;
  eventAuthor?: {
    id: string;
    role: string;
    company: string;
  };
  datasetSchemaVersion: 1;
  eventSchemaVersion: 1;
}

const datasetTaggingEventSchema = new mdb.Schema<DatasetTaggingEvent>({
  dataset: {
    type: String,
    required: true,
  },
  originId: {
    type: {
      companyLocation: String,
      meatType: String,
      barcode: String,
      photoDateTime: String,
    },
    required: true,
    _id: false,
  },
  tagKey: {
    type: String,
    required: true,
  },
  tagValue: {
    type: Object,
    _id: false,
  },
  eventAction: {
    type: String,
    required: true,
  },
  eventTime: {
    type: Date,
    required: true,
  },
  eventAuthor: {
    type: {
      id: String,
      role: String,
      company: String,
    },
    _id: false,
  },
  datasetSchemaVersion: {
    type: Number,
    required: true,
  },
  eventSchemaVersion: {
    type: Number,
    required: true,
  },
});

export const DatasetTaggingEventModel = mdb.model<DatasetTaggingEvent>(
  "dataset-tagging-events",
  datasetTaggingEventSchema,
);
