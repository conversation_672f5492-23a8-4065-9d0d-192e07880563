import * as Models from "./models";
import {
  ProducerOfTheYearScoringConfig,
  GridComplianceScoringConfig,
  GLQScoreScoringConfig,
} from "./models/GMPScoringConfigs";
import {
  DEFAULT_PRODUCER_OF_THE_YEAR_CONFIG,
  DEFAULT_GRID_COMPLIANCE_CONFIG,
  DEFAULT_GLQ_SCORE_CONFIG,
} from "./controllers/GMP/getAwards";

const migrateProbeLamgPkg = async () => {
  await Models.Org.updateMany(
    {
      pkgs: "MEQ Probe Lamb",
    },
    { $set: { "pkgs.$[element]": "MEQ Probe" } },
    { arrayFilters: [{ element: { $eq: "MEQ Probe Lamb" } }] },
  );

  await Models.Permission.updateMany(
    {
      pkgs: "MEQ Probe Lamb",
    },
    { $set: { "pkgs.$[element]": "MEQ Probe" } },
    { arrayFilters: [{ element: { $eq: "MEQ Probe Lamb" } }] },
  );

  await Models.Permission.updateMany(
    {
      role: "Alliance_AllLocations-Admin",
    },
    { $set: { "pages.$[element].id": "MEQ_PROBE" } },
    { arrayFilters: [{ "element.id": "MEQ_PROBE_LAMB" }] },
  );
};
migrateProbeLamgPkg();

const migrateGmpLambBookings = async () => {
  await Models.GmpGrid.updateMany(
    {
      gridType: null,
    },
    { gridType: "GMP" },
  );
  await Models.GmpBooking.updateMany(
    {
      status: "ScheduledHigh",
    },
    { status: "Scheduled High" },
  );
};
migrateGmpLambBookings();

const migrateGMPScoringConfigs = async () => {

  try {
    const createdConfigs = [];

    // Check and create Producer of the Year config for GMP if it doesn't exist
    const existingProducer = await ProducerOfTheYearScoringConfig.findOne({
      org: "GMP",
    });
    if (!existingProducer) {
      await ProducerOfTheYearScoringConfig.create({
        ...DEFAULT_PRODUCER_OF_THE_YEAR_CONFIG,
        org: "GMP",
      });
      createdConfigs.push("Producer of the Year");
    }

    // Check and create Grid Compliance config for GMP if it doesn't exist
    const existingGrid = await GridComplianceScoringConfig.findOne({
      org: "GMP",
    });
    if (!existingGrid) {
      await GridComplianceScoringConfig.create({
        ...DEFAULT_GRID_COMPLIANCE_CONFIG,
        org: "GMP",
      });
      createdConfigs.push("Grid Compliance");
    }

    // Check and create GLQ Score config for GMP if it doesn't exist
    const existingGlq = await GLQScoreScoringConfig.findOne({ org: "GMP" });
    if (!existingGlq) {
      await GLQScoreScoringConfig.create({
        ...DEFAULT_GLQ_SCORE_CONFIG,
        org: "GMP",
      });
      createdConfigs.push("GLQ Score");
    }

    // Only log if any configs were actually created
    if (createdConfigs.length > 0) {
      console.log(
        `✓ Created GMP scoring configs: ${createdConfigs.join(", ")}`,
      );
    }
  } catch (error) {
    console.error("✗ Failed to migrate GMP scoring configs:", error);
  }
};

// Run the migration
migrateGMPScoringConfigs();
