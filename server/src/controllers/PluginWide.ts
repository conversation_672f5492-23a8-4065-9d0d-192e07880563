import { Response } from "express";

import {
  Message,
  ServerResponse,
  ResultCode,
  QueryType,
  LooseObject,
} from "../utils/Types";
import {
  getCameraOwnerStringFromOrgIdString,
  getOptimisedColumns,
  getOrgFromOrgIdString,
  getOrgFromRole,
  isNotEmpty,
  sanitiseParams,
} from "../utils/Helper";
import { executeSnowflakeStatement } from "../utils/SnowflakeConnection";
import {
  PackageDatabases,
  Database,
  findSnowflakeClientDb,
} from "../utils/SnowflakeDatabases";
import {
  ALL_LOCATIONS,
  QUERY_RESPONSE_VOLUMN_THRESHOLD,
  SUPER_ADMIN,
  SUPER_USER,
} from "../utils/Constants";
import { User } from "../models/Interfaces";
import {
  QUERY_PARAMS_CHECK_FAILED,
  VIEW_BUILDERS,
  ViewParams,
} from "../snowflake/ViewBuilders";
import { AuthenticatedRequest } from "./Common";

const getSqlTextWithConditions = ({
  sqlText,
  orgDatabase,
  view,
  columnsInterested,
  columnsNotInterested,
}: {
  sqlText: string;
  orgDatabase: Database;
  view: string;
  columnsInterested: string[];
  columnsNotInterested: string[];
}) => {
  const WITH_SAMPLE = (query: string, columns: string) => `
    WITH SAMPLED_DATA AS (
      SELECT * ${columns} ${query}
    )
    SELECT *
    FROM SAMPLED_DATA
    ORDER BY RANDOM(12345678)
    LIMIT ${QUERY_RESPONSE_VOLUMN_THRESHOLD};
  `;

  const QUERY = `FROM ${view}`;

  if (orgDatabase.excludeColumns && orgDatabase.excludeColumns.length > 0) {
    sqlText = WITH_SAMPLE(
      QUERY,
      `EXCLUDE (${orgDatabase.excludeColumns.join(",")})`,
    );
  } else {
    sqlText = WITH_SAMPLE(QUERY, "");
  }

  // only show interested columns
  if (
    columnsInterested &&
    Array.isArray(columnsInterested) &&
    JSON.stringify(columnsInterested) !== "[]"
  ) {
    const columns = columnsInterested.filter(
      (x) => !new Set(orgDatabase.excludeColumns).has(x),
    );
    sqlText = WITH_SAMPLE(QUERY, columns.join(", "));
  }

  // exclude non-interested columns
  if (
    columnsNotInterested &&
    Array.isArray(columnsNotInterested) &&
    JSON.stringify(columnsNotInterested) !== "[]"
  ) {
    sqlText = WITH_SAMPLE(
      QUERY,
      `EXCLUDE (${columnsNotInterested.concat(orgDatabase.excludeColumns).join(",")})`,
    );
  }

  return sqlText;
};

export async function getDataSource(req: AuthenticatedRequest, res: Response) {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const {
    id,
    type,
    view,
    columnsInterested,
    columnsNotInterested,
    binds,
    isFunction,
    pkg,
  } = sanitiseParams(req.body as LooseObject);

  // check permissions
  const roles = user.roles;

  let idIsIncludedInRole = false;
  if (roles.includes(SUPER_ADMIN) || roles.includes(SUPER_USER)) {
    idIsIncludedInRole = true;
  } else if (roles.filter((role) => getOrgFromRole(role) === id).length > 0) {
    idIsIncludedInRole = true;
  } else {
    roles.forEach((role) => {
      if (
        getOrgFromRole(role).replace("_" + ALL_LOCATIONS, "") ===
        getOrgFromOrgIdString(id)
      ) {
        idIsIncludedInRole = true;
      }
    });
  }

  let orgDatabase = null;
  if (idIsIncludedInRole) {
    if (pkg) {
      orgDatabase = PackageDatabases.find((i) => i.id === pkg);
    } else {
      orgDatabase = {
        id: getOrgFromOrgIdString(id as string),
        database: findSnowflakeClientDb(getOrgFromOrgIdString(id as string)),
        excludeColumns: [] as string[],
      };
    }
  }

  if (orgDatabase?.database) {
    let isValidBinds = true;
    if (isNotEmpty(binds) && (!Array.isArray(binds) || binds.length <= 0)) {
      isValidBinds = false;
    }

    let sqlText = "";

    if (type === QueryType.ORDINARY_QUERY && view && isValidBinds) {
      const sqlView = await buildSqlView(
        view as ViewParams,
        !!isFunction,
        id as string,
        user,
        orgDatabase?.database,
      );
      let colsValid = true;
      if (columnsInterested) {
        colsValid = validateColNames(columnsInterested);
      }
      if (columnsNotInterested) {
        colsValid = validateColNames(columnsNotInterested);
      }

      if (sqlView && colsValid) {
        sqlText = getSqlTextWithConditions({
          sqlText,
          orgDatabase,
          view: sqlView,
          columnsInterested,
          columnsNotInterested,
        });
      }
    }

    if (sqlText) {
      orgDatabase.replaceStrings?.forEach(({ original, replaceWith }) => {
        sqlText = sqlText.replaceAll(original, replaceWith);
      });

      const dataSource = await executeSnowflakeStatement({
        sqlText,
        binds,
        db: orgDatabase.database,
      });

      if (isNotEmpty(dataSource?.columns)) {
        result.code = ResultCode.SUCCESS;
        result.msg = Message.SUCCESS;
        let rows = dataSource.rows;
        const numRows = dataSource.numRows;
        let columns = dataSource.columns;

        const opimisedColumns = getOptimisedColumns({ columns, rows });
        columns = opimisedColumns.columns;
        rows = opimisedColumns.rows;

        result.data = JSON.stringify({
          rows,
          columns,
          numRows,
          numUpdatedRows: dataSource.numUpdatedRows,
          requestId: dataSource.requestId,
        });
      } else {
        result.msg = Message.ERROR_DATA_SOURCE_NOT_AVAILABLE;
      }
    } else {
      result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
    }
  } else {
    result.msg = Message.ERROR_PERMISSION_DENIED;
  }
  res.status(200).json(result);
}

async function buildSqlView(
  view: ViewParams,
  isFunction: boolean,
  orgId: string,
  user: User,
  db: string,
): Promise<string> {
  if (!view.name) {
    throw new Error("Missing view name.");
  }

  const vb = VIEW_BUILDERS[view.name];
  if (vb) {
    return vb.build({ ...view, name: view.name, isFunction, orgId, db }, user);
  }

  switch (view.name) {
    case "MEQCAMERA_DAILY_SUMMARY_METRIC":
      if (
        !dateRegex.test(view.startDate || "") ||
        !dateRegex.test(view.endDate || "") ||
        !isFunction
      ) {
        throw new Error(QUERY_PARAMS_CHECK_FAILED);
      }

      return `TABLE(${db}.MEQINSIGHTS.${view.name}('${view.startDate}', '${view.endDate}', '${getCameraOwnerStringFromOrgIdString(orgId)}'))`;
    case "MEQPROBE_EOD_RAW":
      return `${db}.MEQINSIGHTS.MEQPROBE_EOD_RAW`;
  }

  throw new Error(QUERY_PARAMS_CHECK_FAILED);
}

function validateColNames(colName: any): boolean {
  if (
    !Array.isArray(colName) ||
    !colName.every((c) => typeof c === "string") ||
    !colName.every((c) => snowflakeIdentifierRegex.test(c))
  ) {
    return false;
  }
  return true;
}

const dateRegex = /\d{4}-\d{2}-\d{2}/;
const snowflakeIdentifierRegex = /^[A-Za-z_](\w|\$)*$/;
