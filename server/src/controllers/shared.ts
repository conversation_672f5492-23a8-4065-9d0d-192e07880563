import { z } from "zod";
import _ from "lodash";

import { PkgName } from "../utils/Types";
import { getRoleComponents } from "../utils/Helper";
import {
  ALL_LOCATIONS,
  ALL_ORGS,
  ALL_PKGS,
  SUPER_ADMIN,
  SUPER_USER,
} from "../utils/Constants";
import { StatusCodes } from "http-status-codes";
import { AuthenticatedRequest } from "./Common";
import { Org, Permission } from "../models";
import { OrgCountry } from "../models/Interfaces";

export async function isPackageUser(
  req: AuthenticatedRequest,
  pkgName: PkgName,
): Promise<boolean> {
  if (req.user?.roles == null) {
    return false;
  }

  const roles = req.user.roles as string[];
  if (roles.some((role) => role === SUPER_ADMIN || role === SUPER_USER)) {
    return true;
  }

  const permissions = await Permission.find({ role: { $in: roles } }).exec();

  let permitted = permissions.some((permission) =>
    permission.pkgs.some(
      (pkg) => pkg === ALL_PKGS || pkg === pkgName.toString(),
    ),
  );

  if (!permitted) {
    const orgIdStrings = roles
      .map((role) => getRoleComponents(role))
      .filter(
        (roleComponents) =>
          roleComponents !== undefined && roleComponents.org !== ALL_ORGS,
      )
      .map(
        (roleComponents) =>
          `${roleComponents!.org}_${roleComponents!.location}`,
      );

    const orgs = await Org.find({ idString: { $in: orgIdStrings } });
    permitted = orgs.some((org) =>
      org.pkgs.some((pkg) => pkg === ALL_PKGS || pkg === pkgName.toString()),
    );
  }

  return permitted;
}

export const orgLocationBodySchema = z.object({
  org: z.string().min(1, "org should not be empty").toLowerCase(),
  location: z.string().min(1, "location should not be empty").toLowerCase(),
});

export function isOrgLocationPermitted<
  ReqBody extends z.infer<typeof orgLocationBodySchema>,
>(reqBody: ReqBody, req: AuthenticatedRequest): boolean {
  if (!_.isArray(req.user?.roles)) {
    throw new Error("Invalid user roles.");
  }

  const roles = req.user.roles as string[];
  if (roles.some((role) => role === SUPER_ADMIN || role === SUPER_USER)) {
    return true;
  }

  return roles
    .map((role) => getRoleComponents(role))
    .some((roleComponents) => {
      if (!roleComponents) {
        return false;
      }

      if (
        roleComponents.org !== ALL_ORGS &&
        roleComponents.org.toLowerCase() !== reqBody.org
      ) {
        return false;
      }

      return (
        roleComponents.location === ALL_LOCATIONS ||
        roleComponents.location?.toLowerCase() === reqBody.location
      );
    });
}

export async function getReqBodyIfOrgLocationPermitted<
  ReqBodySchema extends typeof orgLocationBodySchema,
>(
  req: AuthenticatedRequest,
  pkgName: PkgName,
  reqBodySchema: ReqBodySchema,
): Promise<{
  reqBody?: z.infer<typeof reqBodySchema>;
  error?: {
    statusCode: StatusCodes.BAD_REQUEST | StatusCodes.FORBIDDEN;
    message?: string;
  };
}> {
  if (!(await isPackageUser(req, pkgName))) {
    return { error: { statusCode: StatusCodes.FORBIDDEN } };
  }

  const { success, data: reqBody, error } = reqBodySchema.safeParse(req.body);

  if (!success) {
    return {
      error: {
        statusCode: StatusCodes.BAD_REQUEST,
        message: error.message,
      },
    };
  }

  if (!isOrgLocationPermitted(reqBody, req)) {
    return { error: { statusCode: StatusCodes.FORBIDDEN } };
  }

  return { reqBody };
}

export function parseReqBodyBySchema<ReqBodySchema extends z.AnyZodObject>(
  req: AuthenticatedRequest,
  schema: ReqBodySchema,
): {
  data?: z.infer<typeof schema>;
  error?: { statusCode: number; message?: string };
} {
  const { success, data, error } = schema.safeParse(req.body);

  if (!success) {
    return {
      error: {
        statusCode: StatusCodes.BAD_REQUEST,
        message: error.message,
      },
    };
  }

  return { data };
}

export const COLUMNS_TO_RENAME_BY_ORG_COUNTRY: Record<
  OrgCountry,
  Record<string, string>
> = {
  US: {
    EYE_MUSCLE_AREA: "RIB_EYE_AREA",
    AVG_EYE_MUSCLE_AREA: "AVG_RIB_EYE_AREA",
    EYE_MUSCLE_AREA_UNITS: "RIB_EYE_AREA_UNITS",
    MEAT_COLOR: "MEQ_MEAT_COLOR",
    OVERRIDE_MEAT_COLOR: "OPERATOR_MEAT_COLOUR",
  },
  AU: {
    MEAT_COLOR: "MEQ_MEAT_COLOR",
    OVERRIDE_MEAT_COLOR: "OPERATOR_MEAT_COLOUR",
  },
  NZ: {
    MEAT_COLOR: "MEQ_MEAT_COLOR",
    OVERRIDE_MEAT_COLOR: "OPERATOR_MEAT_COLOUR",
  },
};
