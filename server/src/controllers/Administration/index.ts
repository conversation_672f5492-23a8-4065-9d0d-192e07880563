import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import {
  Message,
  ResultCode,
  ServerResponse,
  PkgName,
} from "../../utils/Types";
import * as Models from "../../models";
import {
  ALL_COMPONENTS,
  ORG_JSON_PROPERTIES,
  SUPER_ADMIN,
} from "../../utils/Constants";
import { logExceptionToDB, sanitiseParams } from "../../utils/Helper";
import {
  getZitadelManagementClient,
  getZitadelUserClient,
} from "../../utils/Zitadel";
import { refreshZitadelUserCacheByUserId } from "../../utils/Cache";
import validator from "validator";
import { User } from "../../models/Interfaces";
import PageId from "../../utils/PageId";

// The functions in this file are only available to super admin

const getAllOrgsFromDb = async () =>
  await Models.Org.find({})
    .sort({ _id: -1 })
    .then((o) => o)
    .catch((err) => logExceptionToDB(err));

export const getAllOrgs: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const roles = user.roles;
  if (roles.includes(SUPER_ADMIN)) {
    const orgs = await getAllOrgsFromDb();

    result.data = JSON.stringify(orgs, ORG_JSON_PROPERTIES);
    result.msg = Message.SUCCESS;
    result.code = ResultCode.SUCCESS;
  } else {
    result.msg = Message.ERROR_PERMISSION_DENIED;
  }

  res.status(200).json(result);
};

export const addOrg: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const {
    zitadelOrgIdString,
    idString,
    displayName,
    country,
    pkgs,
    description,
    isTCRequired,
  } = sanitiseParams(req.body);

  if (
    zitadelOrgIdString &&
    idString &&
    displayName &&
    country &&
    pkgs &&
    Array.isArray(pkgs)
  ) {
    // only accept valid pkgs
    const allPkgs = Object.values(PkgName);
    const isValidPkgs =
      pkgs.filter((pkg) => !allPkgs.includes(pkg)).length === 0;

    if (isValidPkgs) {
      const roles = user.roles;
      if (roles.includes(SUPER_ADMIN)) {
        const existingTarget = await Models.Org.findOne({
          idString,
          isValid: true,
        })
          .then((o) => o)
          .catch((err) => logExceptionToDB(err));

        if (existingTarget) {
          result.msg = Message.ERROR_ORG_EXISTS;
        } else {
          const target = await new Models.Org({
            zitadelOrgIdString,
            idString,
            displayName,
            country,
            pkgs,
            description,
            isTCRequired,
            isValid: true,
          })
            .save()
            .then((o) => o)
            .catch((err) => logExceptionToDB(err));
          if (target) {
            const orgs = await getAllOrgsFromDb();

            result.data = JSON.stringify(orgs, ORG_JSON_PROPERTIES);
            result.msg = Message.SUCCESS;
            result.code = ResultCode.SUCCESS;
          }
        }
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const updateOrg: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { idString, displayName, country, pkgs, description, isTCRequired } =
    sanitiseParams(req.body);

  if (idString && displayName && country && pkgs && Array.isArray(pkgs)) {
    // only accept valid pkgs
    const allPkgs = Object.values(PkgName);
    const isValidPkgs =
      pkgs.filter((pkg) => !allPkgs.includes(pkg)).length === 0;

    if (isValidPkgs) {
      const roles = user.roles;
      if (roles.includes(SUPER_ADMIN)) {
        const target = await Models.Org.findOneAndUpdate(
          { idString, isValid: true },
          { displayName, country, pkgs, description, isTCRequired },
        )
          .then((o) => o)
          .catch((err) => logExceptionToDB(err));

        if (target) {
          const orgs = await getAllOrgsFromDb();

          result.data = JSON.stringify(orgs, ORG_JSON_PROPERTIES);
          result.msg = Message.SUCCESS;
          result.code = ResultCode.SUCCESS;
        } else {
          result.msg = Message.ERROR_ORG_NOT_FOUND;
        }
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

const getAllUsersFromDB = async () => {
  const users: User[] = await Models.User.find({})
    .sort({ _id: -1 })
    .then((o) => o);
  return users;
};

export const getAllUsers: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const roles = user.roles;
  if (roles.includes(SUPER_ADMIN)) {
    const users = await getAllUsersFromDB();

    result.data = JSON.stringify(users);
    result.msg = Message.SUCCESS;
    result.code = ResultCode.SUCCESS;
  } else {
    result.msg = Message.ERROR_PERMISSION_DENIED;
  }

  res.status(200).json(result);
};

export const addUser: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  let {
    zitadelOrgId,
    email,
    firstName,
    lastName,
    roles: newRoles,
  } = sanitiseParams(req.body);

  if (
    zitadelOrgId &&
    email &&
    validator.isEmail(email) &&
    firstName &&
    lastName &&
    newRoles
  ) {
    email = email.toLowerCase();

    const roles = user.roles;
    if (roles.includes(SUPER_ADMIN)) {
      let isValidRoles = true;
      for (let i = 0; i < newRoles.length; i++) {
        const role = await Models.Role.findOne({ idString: newRoles[i] });
        if (!role) {
          isValidRoles = false;
        }
      }

      if (isValidRoles) {
        const existing = await Models.User.findOne({ email }).then((o) => o);
        if (existing) {
          result.msg = Message.ERROR_USER_EXISTS;
        } else {
          try {
            // create new account
            const target = await (
              await getZitadelManagementClient(zitadelOrgId)
            )?.addHumanUser({
              userName: email,
              profile: { firstName, lastName, preferredLanguage: "en" },
              email: { email },
            });
            if (target && target.userId) {
              await new Models.User({
                zitadelOrgIdString: zitadelOrgId,
                zitadelId: target.userId,
                email,
                zitadelUsername: email,
                firstName,
                lastName,
                displayName: `${firstName} ${lastName}`,
                roles: newRoles,
                isValid: true,
              })
                .save()
                .then();
              await refreshZitadelUserCacheByUserId(target.userId);
              const users = await getAllUsersFromDB();

              result.data = JSON.stringify(users);
              result.msg = Message.SUCCESS;
              result.code = ResultCode.SUCCESS;
            }
          } catch (error) {
            if ((error as any).message?.includes("User already exists")) {
              const existingZitadelUserResponse = await (
                await getZitadelManagementClient(zitadelOrgId)
              )?.listUsers({
                queries: [{ emailQuery: { emailAddress: email } }],
              });

              if (
                existingZitadelUserResponse &&
                existingZitadelUserResponse.result &&
                existingZitadelUserResponse.result.length > 0
              ) {
                await new Models.User({
                  zitadelOrgIdString: zitadelOrgId,
                  zitadelId: existingZitadelUserResponse.result[0].id,
                  email,
                  zitadelUsername: email,
                  firstName,
                  lastName,
                  displayName: `${firstName} ${lastName}`,
                  roles: newRoles,
                  isValid: true,
                })
                  .save()
                  .then();
                await refreshZitadelUserCacheByUserId(
                  existingZitadelUserResponse.result[0].id,
                );
                const users = await getAllUsersFromDB();

                result.data = JSON.stringify(users);
                result.msg = Message.SUCCESS;
                result.code = ResultCode.SUCCESS;
              }
            }
          }
        }
      } else {
        result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const updateUser: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { id, firstName, lastName } = sanitiseParams(req.body);

  if (id && firstName && lastName) {
    const roles = user.roles;
    if (roles.includes(SUPER_ADMIN)) {
      const target = await Models.User.findOne({ zitadelId: id }).then(
        (o) => o,
      );
      if (target) {
        const zitadelUserUpdated = await (
          await getZitadelUserClient(target.zitadelOrgIdString)
        )?.updateHumanUser({
          userId: id,
          profile: {
            givenName: firstName,
            familyName: lastName,
            displayName: `${firstName} ${lastName}`,
          },
        });

        if (zitadelUserUpdated) {
          await Models.User.findByIdAndUpdate(target._id, {
            firstName,
            lastName,
            displayName: `${firstName} ${lastName}`,
          });
          await refreshZitadelUserCacheByUserId(id);
          const users = await getAllUsersFromDB();

          result.data = JSON.stringify(users);
          result.msg = Message.SUCCESS;
          result.code = ResultCode.SUCCESS;
        } else {
          result.msg = Message.ERROR_USER_NOT_FOUND;
        }
      } else {
        result.msg = Message.ERROR_USER_NOT_FOUND;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

const getAllRolesFromDB = async () =>
  await Models.Role.find({})
    .sort({ _id: -1 })
    .then((o) =>
      o.map((i) => ({
        label: i.displayName,
        value: i.idString,
        description: i.description,
      })),
    );

export const getAllRoles: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const roles = user.roles;
  if (roles.includes(SUPER_ADMIN)) {
    const projectRoles = await getAllRolesFromDB();

    result.data = JSON.stringify(projectRoles);
    result.msg = Message.SUCCESS;
    result.code = ResultCode.SUCCESS;
  } else {
    result.msg = Message.ERROR_PERMISSION_DENIED;
  }

  res.status(200).json(result);
};

export const updateRoles: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { id, roles: newRoles } = sanitiseParams(req.body);

  if (id && newRoles) {
    const roles = user.roles;
    if (roles.includes(SUPER_ADMIN)) {
      let isValidRoles = true;
      for (let i = 0; i < newRoles.length; i++) {
        const role = await Models.Role.findOne({ idString: newRoles[i] });
        if (!role) {
          isValidRoles = false;
        }
      }

      if (isValidRoles) {
        await Models.User.findOneAndUpdate(
          { zitadelId: id },
          { roles: newRoles },
        );
        await refreshZitadelUserCacheByUserId(id);
        const users = await getAllUsersFromDB();

        result.data = JSON.stringify(users);
        result.msg = Message.SUCCESS;
        result.code = ResultCode.SUCCESS;
      } else {
        result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const toggleUserState: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { id, action } = sanitiseParams(req.body);

  if (id && action) {
    const roles = user.roles;
    if (roles.includes(SUPER_ADMIN)) {
      const target = await Models.User.findOne({ zitadelId: id }).then(
        (o) => o,
      );
      if (target) {
        const zitadelUserUpdated =
          action === "activate"
            ? await (
                await getZitadelManagementClient(target.zitadelOrgIdString)
              )?.reactivateUser({ id })
            : await (
                await getZitadelManagementClient(target.zitadelOrgIdString)
              )?.deactivateUser({ id });

        if (zitadelUserUpdated) {
          await Models.User.findByIdAndUpdate(target._id, {
            isValid: action === "activate",
          });
          await refreshZitadelUserCacheByUserId(id);
          const users = await getAllUsersFromDB();

          result.data = JSON.stringify(users);
          result.msg = Message.SUCCESS;
          result.code = ResultCode.SUCCESS;
        } else {
          result.msg = Message.ERROR_USER_NOT_FOUND;
        }
      } else {
        result.msg = Message.ERROR_USER_NOT_FOUND;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const deleteUser: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { id } = sanitiseParams(req.body);

  if (id) {
    const roles = user.roles;
    if (roles.includes(SUPER_ADMIN)) {
      const target = await Models.User.findOne({ zitadelId: id }).then(
        (o) => o,
      );
      if (target) {
        const isDeletedFromZitadel = await (
          await getZitadelManagementClient(target.zitadelOrgIdString)
        )?.removeUser({ id });

        if (isDeletedFromZitadel) {
          const isDeleted = !!(await Models.User.findByIdAndDelete(target._id));
          await refreshZitadelUserCacheByUserId(id);

          if (isDeleted) {
            const users = await getAllUsersFromDB();

            result.data = JSON.stringify(users);
            result.msg = Message.SUCCESS;
            result.code = ResultCode.SUCCESS;
          } else {
            result.msg = Message.ERROR_USER_NOT_FOUND;
          }
        } else {
          result.msg = Message.ERROR_USER_NOT_FOUND;
        }
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const updateRole: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { roleKey, displayName, description } = sanitiseParams(req.body);

  if (roleKey && displayName && description) {
    const roles = user.roles;
    if (roles.includes(SUPER_ADMIN)) {
      const target = await Models.Role.findOneAndUpdate(
        { idString: roleKey },
        { displayName, description },
      ).then((o) => o);

      if (target) {
        const projectRoles = await getAllRolesFromDB();

        result.data = JSON.stringify(projectRoles);
        result.msg = Message.SUCCESS;
        result.code = ResultCode.SUCCESS;
      } else {
        result.msg = Message.ERROR_ITEM_NOT_FOUND;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const addRole: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { roleKey, displayName, description } = sanitiseParams(req.body);

  if (roleKey && displayName && description) {
    const roles = user.roles;
    if (roles.includes(SUPER_ADMIN)) {
      const existing = await Models.Role.findOne({ idString: roleKey }).then(
        (o) => o,
      );
      if (existing) {
        result.msg = Message.ERROR_ROLE_EXISTS;
      } else {
        await new Models.Role({ idString: roleKey, displayName, description })
          .save()
          .then();
        const projectRoles = await getAllRolesFromDB();
        result.data = JSON.stringify(projectRoles);
        result.msg = Message.SUCCESS;
        result.code = ResultCode.SUCCESS;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

const getAllPermissionsFromDb = async () => {
  const permissions = await Models.Permission.find({})
    .sort({ role: 1 })
    .then((o) => o)
    .catch((err) => logExceptionToDB(err));

  return permissions;
};

export const getAllPermissions: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const roles = user.roles;
  if (roles.includes(SUPER_ADMIN)) {
    const permissions = await getAllPermissionsFromDb();

    result.data = JSON.stringify(permissions);
    result.msg = Message.SUCCESS;
    result.code = ResultCode.SUCCESS;
  } else {
    result.msg = Message.ERROR_PERMISSION_DENIED;
  }

  res.status(200).json(result);
};

export const addPermission: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { role, pkgs, pages, isWrite, description } = sanitiseParams(req.body);

  if (role && pkgs && Array.isArray(pkgs) && pages && Array.isArray(pages)) {
    const roles = user.roles;
    if (roles.includes(SUPER_ADMIN)) {
      const allPkgs = Object.values(PkgName);
      const isValidPkgs =
        pkgs.filter((pkg) => !allPkgs.includes(pkg)).length === 0;

      if (isValidPkgs) {
        const validPages: { id: string; components: string[] }[] = [];

        pkgs.forEach((pkg) => {
          pages
            .filter((i) => Object.values(PageId).includes(i))
            .forEach((page) => {
              if (
                page.startsWith(pkg.toUpperCase().split(" ").join("_")) &&
                !validPages.map((i) => i.id).includes(page)
              ) {
                validPages.push({ id: page, components: [ALL_COMPONENTS] });
              }
            });
        });

        const addedItem = await new Models.Permission({
          role,
          pkgs,
          pages: validPages,
          description,
          isWrite: !!isWrite,
        })
          .save()
          .then((o) => o)
          .catch((err) => {
            console.log(err);
            logExceptionToDB(err);
          });

        if (addedItem) {
          const permissions = await getAllPermissionsFromDb();

          result.data = JSON.stringify(permissions);
          result.msg = Message.SUCCESS;
          result.code = ResultCode.SUCCESS;
        } else {
          result.msg = Message.ERROR_ITEM_NOT_FOUND;
        }
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const updatePermission: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { id, role, pkgs, pages, isWrite, description } = sanitiseParams(
    req.body,
  );

  if (
    id &&
    role &&
    pkgs &&
    Array.isArray(pkgs) &&
    pages &&
    Array.isArray(pages)
  ) {
    const roles = user.roles;
    if (roles.includes(SUPER_ADMIN)) {
      const allPkgs = Object.values(PkgName);
      const isValidPkgs =
        pkgs.filter((pkg) => !allPkgs.includes(pkg)).length === 0;

      if (isValidPkgs) {
        const validPages: { id: string; components: string[] }[] = [];

        pkgs.forEach((pkg) => {
          pages
            .filter((i) => Object.values(PageId).includes(i))
            .forEach((page) => {
              if (
                page.startsWith(pkg.toUpperCase().split(" ").join("_")) &&
                !validPages.map((i) => i.id).includes(page)
              ) {
                validPages.push({ id: page, components: [ALL_COMPONENTS] });
              }
            });
        });

        const updatedItem = await Models.Permission.findByIdAndUpdate(id, {
          role,
          pkgs,
          pages: validPages,
          description,
          isWrite: !!isWrite,
        })
          .then((o) => o)
          .catch((err) => logExceptionToDB(err));

        if (updatedItem) {
          const permissions = await getAllPermissionsFromDb();

          result.data = JSON.stringify(permissions);
          result.msg = Message.SUCCESS;
          result.code = ResultCode.SUCCESS;
        } else {
          result.msg = Message.ERROR_ITEM_NOT_FOUND;
        }
      } else {
        result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const deletePermission: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { id } = sanitiseParams(req.body);

  if (id) {
    const roles = user.roles;
    if (roles.includes(SUPER_ADMIN)) {
      const deletedItem = await Models.Permission.findByIdAndDelete(id)
        .then((o) => o)
        .catch((err) => logExceptionToDB(err));

      if (deletedItem) {
        const permissions = await getAllPermissionsFromDb();

        result.data = JSON.stringify(permissions);
        result.msg = Message.SUCCESS;
        result.code = ResultCode.SUCCESS;
      } else {
        result.msg = Message.ERROR_ITEM_NOT_FOUND;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const getAllActionLog: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const roles = user.roles;
  if (roles.includes(SUPER_ADMIN)) {
    const actionLog = await await Models.ActionLog.find({})
      .sort({ _id: -1 })
      .then((o) => o)
      .catch((err) => logExceptionToDB(err));

    result.data = JSON.stringify(actionLog);
    result.msg = Message.SUCCESS;
    result.code = ResultCode.SUCCESS;
  } else {
    result.msg = Message.ERROR_PERMISSION_DENIED;
  }

  res.status(200).json(result);
};

export const getAllZitadelOrgs: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const roles = user.roles;
  if (roles.includes(SUPER_ADMIN)) {
    const zitadelOrgs = (
      await Models.ZitadelOrg.find({}).then((o) => o.map((i) => i.idString))
    ).sort();

    result.data = JSON.stringify(zitadelOrgs);
    result.msg = Message.SUCCESS;
    result.code = ResultCode.SUCCESS;
  } else {
    result.msg = Message.ERROR_PERMISSION_DENIED;
  }

  res.status(200).json(result);
};
