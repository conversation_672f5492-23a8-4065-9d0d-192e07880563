import axios from "axios";
import fs from "fs/promises";
import moment from "moment";
import { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { z } from "zod";

import { AuthenticatedRequest } from "../Common";
import {
  LOGTO_APPLICATION_ID,
  LOGTO_APPLICATION_SECRET,
  LOGTO_TENANT_ID,
} from "../../utils/EnvConfig";
import {
  Message,
  ResultCode,
  ServerResponse,
  SystemEmail,
} from "../../utils/Types";
import { isAdministrationUser } from "./shared";
import { sendMail } from "../../utils/Helper";

const tenantId = LOGTO_TENANT_ID;
const logtoEndpoint = `https://${tenantId}.logto.app`;
const apiEndpoint = `${logtoEndpoint}/api`;
const tokenEndpoint = `${logtoEndpoint}/oidc/token`;
const applicationId = LOGTO_APPLICATION_ID;
const applicationSecret = LOGTO_APPLICATION_SECRET;

async function fetchApiToken() {
  try {
    const response = await axios.post(
      tokenEndpoint,
      new URLSearchParams({
        grant_type: "client_credentials",
        resource: apiEndpoint,
        scope: "all",
      }),
      {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          Authorization: `Basic ${Buffer.from(
            `${applicationId}:${applicationSecret}`,
          ).toString("base64")}`,
        },
      },
    );
    return response.data?.access_token as string;
  } catch (error) {
    console.error("Error fetching API token:", error);
    throw new Error("Error fetching API token");
  }
}

const sendUserInviteEmail = async ({
  email,
  name,
  url = "https://demo.meqinsights.com/",
}: {
  email: string;
  name: string;
  url?: string;
}) => {
  const emailTemplate = await fs.readFile(
    "src/email_templates/demo-invite.html",
    { encoding: "utf8" },
  );
  const html = emailTemplate
    .replaceAll("[INSERT_NAME]", name)
    .replaceAll("[INSERT_LINK]", url)
    .replaceAll("[INSERT_YEAR]", moment().year().toString());
  const emailToSend: SystemEmail = {
    to: email,
    subject: "MEQ Insights Demo Invite",
    html,
  };
  return sendMail(emailToSend);
};

export async function getUsers(req: AuthenticatedRequest, res: Response) {
  if (!isAdministrationUser(req)) {
    res.status(StatusCodes.FORBIDDEN).send();
    return;
  }

  try {
    const apiToken = await fetchApiToken();

    const response = await axios.get(`${apiEndpoint}/users`, {
      headers: {
        Authorization: `Bearer ${apiToken}`,
      },
    });

    res.json(response.data);
  } catch (error) {
    console.error("Error fetching users:", error);
    res.status(500).send("Error fetching users");
  }
}

export const addUserSchema = z.object({
  givenName: z.string().min(1),
  familyName: z.string().min(1),
  email: z.string().email(),
  demoScenario: z.enum(["DemoUSA", "DemoAus"]),
});

export async function addUser(req: AuthenticatedRequest, res: Response) {
  if (!isAdministrationUser(req)) {
    res.status(StatusCodes.FORBIDDEN).send();
    return;
  }

  const { data: reqBody, error } = addUserSchema.safeParse(req.body);

  if (error) {
    res.status(400).send("Invalid user");
    return;
  }

  try {
    const apiToken = await fetchApiToken();

    const response = await axios.post(
      `${apiEndpoint}/users`,
      {
        name: `${reqBody.givenName} ${reqBody.familyName}`,
        primaryEmail: reqBody.email,
        profile: {
          givenName: reqBody.givenName,
          familyName: reqBody.familyName,
        },
        customData: { demoScenario: reqBody.demoScenario },
      },
      {
        headers: {
          Authorization: `Bearer ${apiToken}`,
        },
      },
    );

    const sentMessageInfo = await sendUserInviteEmail({
      email: reqBody.email,
      name: reqBody.givenName,
    });
    if (!sentMessageInfo.isSuccess) {
      res.status(500).send(Message.ERROR_SEND_EMAIL);
      return;
    }

    const result: ServerResponse = {
      msg: Message.SUCCESS,
      data: JSON.stringify(response.data as object),
      code: ResultCode.SUCCESS,
    };

    res.json(result);
  } catch (error) {
    console.error("Error adding user:", error);
    res.status(500).send("Error adding user");
  }
}

export const deleteUserSchema = z.object({
  id: z.string(),
});

export async function deleteUser(req: AuthenticatedRequest, res: Response) {
  if (!isAdministrationUser(req)) {
    res.status(StatusCodes.FORBIDDEN).send();
    return;
  }

  const { data: reqBody, error } = deleteUserSchema.safeParse(req.body);

  if (error) {
    res.status(400).send("Invalid user");
    return;
  }

  try {
    const apiToken = await fetchApiToken();

    const response = await axios.delete(`${apiEndpoint}/users/${reqBody.id}`, {
      data: { reqBody },
      headers: {
        Authorization: `Bearer ${apiToken}`,
      },
    });
    const result: ServerResponse = {
      msg: Message.SUCCESS,
      data: JSON.stringify(response.data as object),
      code: ResultCode.SUCCESS,
    };

    res.json(result);
  } catch (error) {
    console.error("Error deleting user:", error);
    res.status(500).send("Error deleting user");
  }
}

export const editUserSchema = z.object({
  id: z.string(),
  givenName: z.string().min(1),
  familyName: z.string().min(1),
  demoScenario: z.enum(["DemoUSA", "DemoAus"]),
});

export async function editUser(req: AuthenticatedRequest, res: Response) {
  if (!isAdministrationUser(req)) {
    res.status(StatusCodes.FORBIDDEN).send();
    return;
  }

  const { data: reqBody, error } = editUserSchema.safeParse(req.body);

  if (error) {
    res.status(400).send("Invalid user");
    return;
  }

  try {
    const apiToken = await fetchApiToken();

    const response = await axios.patch(
      `${apiEndpoint}/users/${reqBody.id}`,
      {
        name: `${reqBody.givenName} ${reqBody.familyName}`,
        profile: {
          givenName: reqBody.givenName,
          familyName: reqBody.familyName,
        },
        customData: { demoScenario: reqBody.demoScenario },
      },
      {
        headers: {
          Authorization: `Bearer ${apiToken}`,
        },
      },
    );

    const result: ServerResponse = {
      msg: Message.SUCCESS,
      data: JSON.stringify(response.data as object),
      code: ResultCode.SUCCESS,
    };

    res.json(result);
  } catch (error) {
    console.error("Error editing user:", error);
    res.status(500).send("Error editing user");
  }
}

export const inviteUserSchema = z.object({
  name: z.string().min(3),
  email: z.string().email(),
});

export async function inviteUser(req: AuthenticatedRequest, res: Response) {
  if (!isAdministrationUser(req)) {
    res.status(StatusCodes.FORBIDDEN).send();
    return;
  }

  const { data: reqBody, error } = inviteUserSchema.safeParse(req.body);

  if (error) {
    res.status(400).send("Invalid user");
    return;
  }

  const sentMessageInfo = await sendUserInviteEmail({
    email: reqBody.email,
    name: reqBody.name,
  });
  if (sentMessageInfo.isSuccess) {
    const result: ServerResponse = {
      msg: Message.SUCCESS,
      data: JSON.stringify(sentMessageInfo),
      code: ResultCode.SUCCESS,
    };

    res.json(result);
  } else {
    res.status(500).send(Message.ERROR_SEND_EMAIL);
  }
}
