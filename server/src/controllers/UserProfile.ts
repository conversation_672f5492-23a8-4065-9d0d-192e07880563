import { Request<PERSON>and<PERSON> } from "express";
import {
  ALLIANCE_ROLE,
  Message,
  ResultCode,
  ServerResponse,
  ZitadelOrgIdString,
} from "../utils/Types";
import * as Models from "../models";
import {
  ALL_LOCATIONS,
  ORG_JSON_PROPERTIES,
  SUPER_ADMIN,
  SUPER_USER,
} from "../utils/Constants";
import {
  getOrgIdStringsFromUser,
  hasPermission,
  logExceptionToDB,
  sanitiseParams,
} from "../utils/Helper";
import { User } from "../models/Interfaces";

export const userInfo: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.SUCCESS,
    code: ResultCode.SUCCESS,
    data: JSON.stringify(user),
  };

  res.status(200).json(result);
};

export const userOrgs: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  let orgs: any = [];

  const roles = user.roles;
  if (roles) {
    if (roles.includes(SUPER_ADMIN) || roles.includes(SUPER_USER)) {
      // return all orgs
      orgs = await Models.Org.find(
        req.zitadelOrgIdString === ZitadelOrgIdString.MEQ
          ? {}
          : { zitadelOrgIdString: req.zitadelOrgIdString },
      )
        .sort({ displayName: 1 })
        .then((o) => o)
        .catch((err) => logExceptionToDB(err));
    } else {
      const rolesWithAllLocations = roles.filter((i) =>
        i.includes(ALL_LOCATIONS),
      );
      if (rolesWithAllLocations.length > 0) {
        for (let i = 0; i < rolesWithAllLocations.length; i++) {
          if (
            rolesWithAllLocations[i] === ALLIANCE_ROLE.ALL_LOCATIONS_SUPPLIER ||
            rolesWithAllLocations[i] === ALLIANCE_ROLE.ALL_LOCATIONS_LSR ||
            rolesWithAllLocations[i] === ALLIANCE_ROLE.ALL_LOCATIONS_RM
          ) {
            orgs = orgs.concat(
              await Models.Org.find({ idString: `Alliance_${ALL_LOCATIONS}` }),
            );
          } else {
            const client = rolesWithAllLocations[i].split("_")[0];
            if (client) {
              const orgsOfAllLocations = await Models.Org.find(
                req.zitadelOrgIdString === ZitadelOrgIdString.MEQ
                  ? { idString: { $regex: new RegExp(`^${client}`, "i") } }
                  : {
                      zitadelOrgIdString: req.zitadelOrgIdString,
                      idString: { $regex: new RegExp(`^${client}`, "i") },
                    },
              )
                .then((o) => o)
                .catch((err) => logExceptionToDB(err));
              orgs = orgs.concat(orgsOfAllLocations);
            }
          }
        }
      } else {
        const orgIdStrings = getOrgIdStringsFromUser(user);
        orgs = await Models.Org.find(
          req.zitadelOrgIdString === ZitadelOrgIdString.MEQ
            ? { idString: { $in: orgIdStrings } }
            : {
                zitadelOrgIdString: req.zitadelOrgIdString,
                idString: { $in: orgIdStrings },
              },
        )
          .then((o) => o)
          .catch((err) => logExceptionToDB(err));
      }
    }
  }

  if (orgs) {
    result.data = JSON.stringify(orgs, ORG_JSON_PROPERTIES);
    result.msg = Message.SUCCESS;
    result.code = ResultCode.SUCCESS;
  }

  res.status(200).json(result);
};

export const userRoles: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.SUCCESS,
    data: JSON.stringify(user.roles),
    code: ResultCode.SUCCESS,
  };

  res.status(200).json(result);
};

export const userRoleDisplayNames: RequestHandler = async (req, res) => {
  const user = req.user as User;
  const roleDispalyNames: { role: string; displayName: string }[] = (
    await Models.Role.find({ idString: { $in: user.roles } })
  ).map((i) => ({
    role: i.idString,
    displayName: i.displayName,
  }));

  const result: ServerResponse = {
    msg: Message.SUCCESS,
    data: JSON.stringify(roleDispalyNames),
    code: ResultCode.SUCCESS,
  };

  res.status(200).json(result);
};

export const userConfig: RequestHandler = async (req, res) => {
  const user = req.user as User;

  // can add any specified config for a user
  const config = {
    allianceBeefViewAllowed: user.allianceBeefViewAllowed,
    allianceLambViewAllowed: user.allianceLambViewAllowed,
    termsOfServiceAcceptanceTimestamp: user.termsOfServiceAcceptanceTimestamp,
  };

  const result: ServerResponse = {
    msg: Message.SUCCESS,
    data: JSON.stringify(config),
    code: ResultCode.SUCCESS,
  };

  res.status(200).json(result);
};

export const userRolePermission: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { role } = sanitiseParams(req.body);
  if (role) {
    const roles = user.roles;
    // make sure the user can query the permissions
    if (hasPermission({ roles, requiredRoles: [role] })) {
      const permission = await Models.Permission.findOne({ role })
        .then((o) => o)
        .catch((err) => logExceptionToDB(err));

      result.data = JSON.stringify(permission);
      result.msg = Message.SUCCESS;
      result.code = ResultCode.SUCCESS;
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};
