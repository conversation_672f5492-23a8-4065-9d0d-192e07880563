import { Response } from "express";
import { z } from "zod";

import { ALL_LOCATIONS } from "../../utils/Constants";
import { Message, PkgName } from "../../utils/Types";
import { User } from "../../models/Interfaces";
import { AuthenticatedRequest } from "../Common";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { findDb, getReqQueryIfPermitted } from "./shared";
import {
  querySuppliersForUser,
  querySupplierNumbersForUserRm,
} from "./querySuppliersForUser";
import {
  chClient,
  findDatabaseForPackage,
  isClickHouseEnabled,
} from "../../utils/ClickHouse";

const reqQuerySchema = z.object({
  location: z.string().min(1, "location should not be empty"),
});

const WHERES_MARKER = "###WHERES###";

const sfDb = findDb();
const chDb = findDatabaseForPackage(PkgName.ALLIANCE_DATA_ANALYSIS);

type SupplierFilter = "NONE" | "RM" | "STANDARD";

interface QueryContext {
  sql: string;
  lets?: string;
  wheres?: string;
  queryParams?: Record<string, unknown>;
  processLocationParam: (location: string, context: QueryContext) => void;
  processSupplierNumbersParam: (
    supplierNumbers: number[],
    context: QueryContext,
  ) => void;
  executeQuery: (context: QueryContext) => Promise<QueryResult>;
}

interface QueryResult {
  rows: unknown;
  columns: unknown;
}

const createChContext = (options: { clickHouseSql: string }) => ({
  sql: options.clickHouseSql,
  wheres: "",
  queryParams: {},
  processLocationParam: (location: string, context: QueryContext) => {
    context.wheres += " and lower(location) = {location:String}";
    context.queryParams!.location = location.toLowerCase();
  },
  processSupplierNumbersParam: (
    supplierNumbers: number[],
    context: QueryContext,
  ) => {
    context.wheres += " and has({supplier_nos:Array(Int32)}, supplier_no)";
    context.queryParams!.supplier_nos = supplierNumbers;
  },
  executeQuery: async (context: QueryContext) => {
    const resultSet = await chClient().query({
      query: context.sql.replace(WHERES_MARKER, context.wheres!),
      format: "JSON",
      query_params: context.queryParams,
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    });

    const { meta, data } = await resultSet.json();
    return { rows: data, columns: meta };
  },
});

const createSfContext = (options: { snowflakeSql: string }) => ({
  sql: options.snowflakeSql,
  lets: "",
  wheres: "",
  processLocationParam: (location: string, context: QueryContext) => {
    context.lets += `LET location VARCHAR := '${location.toLowerCase()}'; `;
    context.wheres += " and LOCATION = :location";
  },
  processSupplierNumbersParam: (
    supplierNumbers: number[],
    context: QueryContext,
  ) => {
    context.lets += `LET supplier_numbers := array_construct(${supplierNumbers.join(",")});`;
    context.wheres +=
      " and ARRAY_CONTAINS(SUPPLIER_NO::variant,:supplier_numbers)";
  },
  executeQuery: async (context: QueryContext) => {
    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: `
              DECLARE
                res RESULTSET;
              BEGIN
                ${context.lets}
              res := (
                ${context.sql.replace(WHERES_MARKER, context.wheres!)}
              );
              RETURN TABLE(res);
              END
            `,
      db: sfDb,
    });
    return { rows, columns };
  },
});

const getTraitsOverTimeForQuery = (options: {
  isBeef: boolean;
  shouldFilterSuppliers: SupplierFilter;
  snowflakeSql: string;
  clickHouseSql: string;
}) => {
  if (isClickHouseEnabled()) {
    return getTraitsOverTimeForQueryHandler({
      isBeef: options.isBeef,
      shouldFilterSuppliers: options.shouldFilterSuppliers,
      createContext: () =>
        createChContext({ clickHouseSql: options.clickHouseSql }),
    });
  } else {
    return getTraitsOverTimeForQueryHandler({
      isBeef: options.isBeef,
      shouldFilterSuppliers: options.shouldFilterSuppliers,
      createContext: () =>
        createSfContext({
          snowflakeSql: options.snowflakeSql,
        }),
    });
  }
};

const getTraitsOverTimeForQueryHandler =
  (options: {
    isBeef: boolean;
    shouldFilterSuppliers: SupplierFilter;
    createContext: () => QueryContext;
  }) =>
  async (req: AuthenticatedRequest, res: Response) => {
    const { reqQuery, error } = await getReqQueryIfPermitted(
      req,
      reqQuerySchema,
    );

    if (error) {
      res.status(error.statusCode).send(error.message);
      return;
    }

    const context = options.createContext();

    if (reqQuery?.location && reqQuery?.location !== ALL_LOCATIONS) {
      context.processLocationParam(reqQuery.location, context);
    }
    if (options.shouldFilterSuppliers === "STANDARD") {
      const result = await querySuppliersForUser(
        reqQuery!.location,
        req.user as User,
        options.isBeef,
      );
      if (result.msg !== Message.SUCCESS || !Array.isArray(result.data)) {
        res.status(500).send(result.msg);
        return;
      } else {
        const supplierNumbers = result.data.map(
          (s: { SUPPLIER_NO: number }) => s.SUPPLIER_NO,
        );
        context.processSupplierNumbersParam(supplierNumbers, context);
      }
    } else if (options.shouldFilterSuppliers === "RM") {
      const result = await querySupplierNumbersForUserRm(
        reqQuery!.location,
        req.user as User,
        options.isBeef,
      );
      if (result.msg !== Message.SUCCESS || !Array.isArray(result.data)) {
        res.status(500).send(result.msg);
        return;
      } else {
        context.processSupplierNumbersParam(result.data as number[], context);
      }
    }

    const { rows, columns } = await context.executeQuery(context);

    res.json({
      rows,
      columns,
    });
  };

const sfFieldGroupAvg = (
  sourceFieldSuffix: string,
  resultFieldSuffix: string,
) =>
  `sum(iff(SUM_${sourceFieldSuffix} > 0,SUM_${sourceFieldSuffix}, null)) as GROUP_SUM_${resultFieldSuffix},
  sum(iff(COUNT_${sourceFieldSuffix} > 0, COUNT_${sourceFieldSuffix}, null)) as GROUP_COUNT_${resultFieldSuffix},
  GROUP_SUM_${resultFieldSuffix} / GROUP_COUNT_${resultFieldSuffix} as AVG_${resultFieldSuffix}`;

const chFieldGroupAvg = (
  sourceFieldSuffix: string,
  resultFieldSuffix: string,
) =>
  `sum(if(sum_${sourceFieldSuffix} > 0,sum_${sourceFieldSuffix}, null)) as GROUP_SUM_${resultFieldSuffix},
  sum(if(count_${sourceFieldSuffix} > 0, count_${sourceFieldSuffix}, null)) as GROUP_COUNT_${resultFieldSuffix},
  GROUP_SUM_${resultFieldSuffix} / GROUP_COUNT_${resultFieldSuffix} as AVG_${resultFieldSuffix}`;

export const getTraitsOverTimeLamb = getTraitsOverTimeForQuery({
  clickHouseSql: `
    select
      toString(supplier_no) as SUPPLIER_NO,
      mob_date as MOB_DATE,
      ${chFieldGroupAvg("imf_pct", "IMF_PERCENTAGE_OF")},
      ${chFieldGroupAvg("lmy_pct", "LMY_PERCENTAGE_OF")},
      ${chFieldGroupAvg("hscw", "HOTWEIGHT_KG")},
      arrayStringConcat(arraySort(groupArray(distinct killsheet)), ', ') as KILLSHEETS
    from
      ${chDb}.alliance_insights_lamb_traits_over_time
    where
      1=1
      ${WHERES_MARKER}
    group by mob_date, supplier_no
    order by mob_date
  `,
  snowflakeSql: `
    select
      to_varchar(SUPPLIER_NO) as SUPPLIER_NO,
      MOB_DATE,
      ${sfFieldGroupAvg("IMF_PCT", "IMF_PERCENTAGE_OF")},
      ${sfFieldGroupAvg("LMY_PCT", "LMY_PERCENTAGE_OF")},
      ${sfFieldGroupAvg("HSCW", "HOTWEIGHT_KG")},
      LISTAGG(DISTINCT KILLSHEET, ', ')
        WITHIN GROUP (ORDER BY KILLSHEET)
        as KILLSHEETS
    from
      ${sfDb}.MEQINSIGHTS.ALLIANCE_INSIGHTS_LAMB_TRAITS_OVER_TIME
    where
      1=1
      ${WHERES_MARKER}
    group by MOB_DATE, SUPPLIER_NO
    order by MOB_DATE
  `,
  isBeef: false,
  shouldFilterSuppliers: "STANDARD",
});

export const getTraitsOverTimeBeef = getTraitsOverTimeForQuery({
  clickHouseSql: `
    select
      toString(supplier_no) as SUPPLIER_NO,
      mob_date as MOB_DATE,
      ${chFieldGroupAvg("aus_marbling", "AUS_MARBLING")},
      sum(count_2_plus) as GROUP_SUM_HEADCOUNT_2_PLUS,
      sum(if(count_2_plus > 0, 1, null)) as GROUP_COUNT_HEADCOUNT_2_PLUS,
      GROUP_SUM_HEADCOUNT_2_PLUS / GROUP_COUNT_HEADCOUNT_2_PLUS as AVG_HEADCOUNT_2_PLUS,
      ${chFieldGroupAvg("coldweight", "CCS_WEIGHT_KG")}
    from
      ${chDb}.alliance_insights_beef_traits_over_time
    where
      1=1
      ${WHERES_MARKER}
    group by mob_date, supplier_no
    order by mob_date
  `,
  snowflakeSql: `
    select
      to_varchar(SUPPLIER_NO) as SUPPLIER_NO,
      MOB_DATE,
      ${sfFieldGroupAvg("AUS_MARBLING", "AUS_MARBLING")},
      sum(COUNT_2_PLUS) as GROUP_SUM_HEADCOUNT_2_PLUS,
      sum(iff(COUNT_2_PLUS > 0, 1, null)) as GROUP_COUNT_HEADCOUNT_2_PLUS,
      GROUP_SUM_HEADCOUNT_2_PLUS / GROUP_COUNT_HEADCOUNT_2_PLUS as AVG_HEADCOUNT_2_PLUS,
      ${sfFieldGroupAvg("COLDWEIGHT", "CCS_WEIGHT_KG")}
    from
      ${sfDb}.MEQINSIGHTS.ALLIANCE_INSIGHTS_BEEF_TRAITS_OVER_TIME
    where
      1=1
      ${WHERES_MARKER}
    group by MOB_DATE, SUPPLIER_NO
    order by MOB_DATE
  `,
  isBeef: true,
  shouldFilterSuppliers: "STANDARD",
});

export const getTraitsOverTimeLambBrand = getTraitsOverTimeForQuery({
  clickHouseSql: `
    select
      toString(brand) as BRAND,
      mob_date as MOB_DATE,
      ${chFieldGroupAvg("imf_pct", "IMF_PERCENTAGE_OF")},
      ${chFieldGroupAvg("lmy_pct", "LMY_PERCENTAGE_OF")},
      ${chFieldGroupAvg("hscw", "HOTWEIGHT_KG")}
    from
      ${chDb}.alliance_insights_lamb_traits_over_time
    where
      1=1
      ${WHERES_MARKER}
    group by mob_date, brand
    order by mob_date
  `,
  snowflakeSql: `
    select
      to_varchar(BRAND) as BRAND,
      MOB_DATE,
      ${sfFieldGroupAvg("IMF_PCT", "IMF_PERCENTAGE_OF")},
      ${sfFieldGroupAvg("LMY_PCT", "LMY_PERCENTAGE_OF")},
      ${sfFieldGroupAvg("HSCW", "HOTWEIGHT_KG")}
    from
      ${sfDb}.MEQINSIGHTS.ALLIANCE_INSIGHTS_LAMB_TRAITS_OVER_TIME
    where
      1=1
      ${WHERES_MARKER}
    group by MOB_DATE, BRAND
    order by MOB_DATE
  `,
  isBeef: false,
  shouldFilterSuppliers: "NONE",
});

export const getTraitsOverTimeLambAllSuppliers = (
  shouldFilterSuppliers: boolean,
) =>
  getTraitsOverTimeForQuery({
    clickHouseSql: `
      select
        mob_date as MOB_DATE,
        ${chFieldGroupAvg("imf_pct", "IMF_PERCENTAGE_OF")},
        ${chFieldGroupAvg("lmy_pct", "LMY_PERCENTAGE_OF")},
        ${chFieldGroupAvg("hscw", "HOTWEIGHT_KG")}
      from
        ${chDb}.alliance_insights_lamb_traits_over_time
      where
        1=1
        ${WHERES_MARKER}
      group by mob_date
      order by mob_date
    `,
    snowflakeSql: `
      select
        MOB_DATE,
        ${sfFieldGroupAvg("IMF_PCT", "IMF_PERCENTAGE_OF")},
        ${sfFieldGroupAvg("LMY_PCT", "LMY_PERCENTAGE_OF")},
        ${sfFieldGroupAvg("HSCW", "HOTWEIGHT_KG")}
      from
        ${sfDb}.MEQINSIGHTS.ALLIANCE_INSIGHTS_LAMB_TRAITS_OVER_TIME
      where
        1=1
        ${WHERES_MARKER}
      group by MOB_DATE
      order by MOB_DATE
    `,
    isBeef: false,
    shouldFilterSuppliers: shouldFilterSuppliers ? "RM" : "NONE",
  });

export const getTraitsOverTimeBeefAllSuppliers = (
  shouldFilterSuppliers: boolean,
) =>
  getTraitsOverTimeForQuery({
    clickHouseSql: `
      select
        mob_date as MOB_DATE,
        ${chFieldGroupAvg("aus_marbling", "AUS_MARBLING")},
        sum(count_2_plus) as GROUP_SUM_HEADCOUNT_2_PLUS,
        sum(if(count_2_plus > 0, 1, null)) as GROUP_COUNT_HEADCOUNT_2_PLUS,
        GROUP_SUM_HEADCOUNT_2_PLUS / GROUP_COUNT_HEADCOUNT_2_PLUS as AVG_HEADCOUNT_2_PLUS,
        ${chFieldGroupAvg("coldweight", "CCS_WEIGHT_KG")}
      from
        ${chDb}.alliance_insights_beef_traits_over_time
      where
        1=1
        ${WHERES_MARKER}
      group by mob_date
      order by mob_date
    `,
    snowflakeSql: `
      select
        MOB_DATE,
        ${sfFieldGroupAvg("AUS_MARBLING", "AUS_MARBLING")},
        sum(COUNT_2_PLUS) as GROUP_SUM_HEADCOUNT_2_PLUS,
        sum(iff(COUNT_2_PLUS > 0, 1, null)) as GROUP_COUNT_HEADCOUNT_2_PLUS,
        GROUP_SUM_HEADCOUNT_2_PLUS / GROUP_COUNT_HEADCOUNT_2_PLUS as AVG_HEADCOUNT_2_PLUS,
        ${sfFieldGroupAvg("COLDWEIGHT", "CCS_WEIGHT_KG")}
      from
        ${sfDb}.MEQINSIGHTS.ALLIANCE_INSIGHTS_BEEF_TRAITS_OVER_TIME
      where
        1=1
        ${WHERES_MARKER}
      group by MOB_DATE
      order by MOB_DATE
    `,
    isBeef: true,
    shouldFilterSuppliers: shouldFilterSuppliers ? "RM" : "NONE",
  });
