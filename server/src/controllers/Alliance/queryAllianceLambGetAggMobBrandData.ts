import { Response } from "express";
import { z } from "zod";

import { AuthenticatedRequest } from "../Common";
import { PkgName } from "../../utils/Types";
import {
  getReqBodyIfOrgLocationPermitted,
  orgLocationBodySchema,
} from "../shared";
import { findDb } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { getOptimisedColumns } from "../../utils/Helper";
import {
  chClient,
  findDatabaseForPackage,
  isClickHouseEnabled,
  removeUnusedColumnsInQueryResult,
} from "../../utils/ClickHouse";

const reqBodySchema = orgLocationBodySchema.extend({
  org: z
    .string()
    .toLowerCase()
    .regex(/^alliance$/),
  startDate: z.string().date(),
  endDate: z.string().date(),
});

export async function queryAllianceLambGetAggMobBrandData(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfOrgLocationPermitted(
    req,
    PkgName.ALLIANCE_DATA_ANALYSIS,
    reqBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  if (isClickHouseEnabled()) {
    const db = findDatabaseForPackage(PkgName.ALLIANCE_DATA_ANALYSIS);

    const resultSet = await chClient().query({
      query: `
        select
          mob_date as MOB_DATE,
          mob_no as MOB_NO,
          brand as BRAND,
          num_in_mob as NUM_IN_MOB,
          avg_hscw as AVG_HSCW,
          avg_imf_percentage_of as AVG_IMF_PERCENTAGE_OF,
          avg_viascan_gr as AVG_VIASCAN_GR,
          avg_viascan_yield_percentage_of as AVG_VIASCAN_YIELD_PERCENTAGE_OF
        from ${db}.alliance_lamb_get_agg_mob_brand_data(
          start_date={start_date:Date32},
          end_date={end_date:Date32},
          location={location:String}
        )
      `,
      format: "JSON",
      query_params: {
        start_date: reqBody!.startDate,
        end_date: reqBody!.endDate,
        location: reqBody!.location,
      },
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    });

    const { meta, data } = removeUnusedColumnsInQueryResult(
      await resultSet.json(),
    );
    res.json({ rows: data, columns: meta, numRows: data.length });
  } else {
    const db = findDb();

    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: `
        set start_date = ?;
        set end_date = ?;
        set location = ?;
        select *
        from table(
          ${db}.MEQINSIGHTS.ALLIANCE_LAMB_GET_AGG_MOB_BRAND_DATA(
            $start_date,
            $end_date,
            $location
          )
        )
      `,
      binds: [reqBody!.startDate, reqBody!.endDate, reqBody!.location],
      parameters: {
        MULTI_STATEMENT_COUNT: 4,
      },
      db,
    });

    const optimised = getOptimisedColumns({ columns, rows });

    res.json({
      ...optimised,
      numRows: optimised.rows?.length || 0,
    });
  }
}
