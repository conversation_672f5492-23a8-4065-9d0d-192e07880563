import { Response } from "express";
import { z } from "zod";

import { AuthenticatedRequest } from "../Common";
import { PkgName } from "../../utils/Types";
import {
  getReqBodyIfOrgLocationPermitted,
  orgLocationBodySchema,
} from "../shared";
import { findDb } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { getOptimisedColumns } from "../../utils/Helper";
import {
  chClient,
  findDatabaseForPackage,
  isClickHouseEnabled,
  removeUnusedColumnsInQueryResult,
} from "../../utils/ClickHouse";
import { ALL } from "../../utils/Constants";

const reqBodySchema = orgLocationBodySchema.extend({
  org: z
    .string()
    .toLowerCase()
    .regex(/^alliance$/),
  startDate: z.string().date(),
  endDate: z.string().date(),
  supplier: z.coerce.number().int(),
  killsheet: z.coerce.string().regex(/^(\d+)|(all)$/i),
});

export async function queryAllianceLambGetIndividualDataForFarmerKillsheet(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfOrgLocationPermitted(
    req,
    PkgName.ALLIANCE_DATA_ANALYSIS,
    reqBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  if (isClickHouseEnabled()) {
    const db = findDatabaseForPackage(PkgName.ALLIANCE_DATA_ANALYSIS);

    const resultSet = await chClient().query({
      query: `
        select
          supplier_no AS SUPPLIER_NO,
          location AS LOCATION,
          killsheet AS KILLSHEET,
          tag_id AS TAG_ID,
          kill_date AS KILL_DATE,
          hotweight_kg AS HOTWEIGHT_KG,
          IMF_PERCENTAGE_OF AS IMF_PERCENTAGE_OF,
          LMY_PERCENTAGE_OF AS LMY_PERCENTAGE_OF,
          gr AS GR,
          leg_yield AS LEG_YIELD,
          loin_yield AS LOIN_YIELD,
          shld_yield AS SHLD_YIELD,
          vs_grade AS VS_GRADE
        from ${db}.alliance_lamb_get_individual_data_for_farmer_killsheet(
          start_date={start_date:String},
          end_date={end_date:String},
          location={location:String},
          supplier_no={supplier:Int32},
          killsheet={killsheet:String}
        )
      `,
      format: "JSON",
      query_params: {
        start_date: reqBody!.startDate,
        end_date: reqBody!.endDate,
        location: reqBody!.location,
        supplier: reqBody!.supplier,
        killsheet: reqBody!.killsheet,
      },
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    });

    const { meta, data } = removeUnusedColumnsInQueryResult(
      await resultSet.json(),
    );
    res.json({ rows: data, columns: meta, numRows: data.length });
  } else {
    const db = findDb();

    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: `
        set start_date = ?;
        set end_date = ?;
        set location = ?;
        set supplier = ?::INTEGER;
        set killsheet = ?;
        select *
        from table(
          ${db}.MEQINSIGHTS.ALLIANCE_LAMB_GET_INDIVIDUAL_DATA_FOR_FARMER_KILLSHEET(
            $start_date,
            $end_date,
            $location,
            $supplier,
            $killsheet
          )
        )
      `,
      binds: [
        reqBody!.startDate,
        reqBody!.endDate,
        reqBody!.location === "all" ? ALL : reqBody!.location,
        reqBody!.supplier,
        reqBody!.killsheet,
      ],
      parameters: {
        MULTI_STATEMENT_COUNT: 6,
      },
      db,
    });

    const optimised = getOptimisedColumns({ columns, rows });

    res.json({
      ...optimised,
      numRows: optimised.rows?.length || 0,
    });
  }
}
