import { Response } from "express";
import { z } from "zod";
import _ from "lodash";

import { AuthenticatedRequest } from "../Common";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import {
  findDb,
  getLsrSuppliers,
  getReqQueryIfPermitted,
  getRmSuppliers,
} from "./shared";
import {
  chClient,
  findDatabaseForPackage,
  isClickHouseEnabled,
} from "../../utils/ClickHouse";
import { PkgName } from "../../utils/Types";

const reqQuerySchema = z.object({
  brands: z.string().array().optional(),
});

const RM_REGION_MAP: Record<string, string> = {
  "Mark Anderson": "Northern",
  "Brad Osborne": "North Island",
  "Dan Cairns": "Eastern",
  "Joel Richards": "Southern",
};
const WHERES_MARKER = "###WHERES###";

interface QueryResult {
  rows: unknown;
  columns: unknown;
}

interface QueryContext {
  sql: string;
  lets?: string;
  wheres?: string;
  queryParams?: Record<string, unknown>;
  executeQuery: (
    context: QueryContext,
    supplierRegionPairs: (readonly [string, string])[],
  ) => Promise<QueryResult>;
}

const sfDb = findDb();
const chDb = findDatabaseForPackage(PkgName.ALLIANCE_DATA_ANALYSIS);

const createRegionAnalysisForQueryChContext = (options: {
  clickHouseSql: string;
}) => ({
  sql: options.clickHouseSql,
  lets: "",
  wheres: "",
  queryParams: {},
  executeQuery: async (
    context: QueryContext,
    supplierRegionPairs: (readonly [string, string])[],
  ) => {
    // Create unique supplier -> region mapping (deduplicating suppliers)
    const supplierRegionMap = new Map<string, string>();
    supplierRegionPairs.forEach(([supplier, region]) => {
      supplierRegionMap.set(supplier, region);
    });
    const uniqueMappings = Array.from(supplierRegionMap.entries());

    const resultSet = await chClient().query({
      query: `
        with supplier_grouping as (
            select *
            from values(
              'supplier_no Int32, region Nullable(String)',
              ${uniqueMappings.map(([supplier, region]) => `(${supplier}, '${region}')`).join(",")}
            )
        )
        ${context.sql.replace(WHERES_MARKER, context.wheres || "")}
      `,
      format: "JSON",
      query_params: context.queryParams,
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    });

    const { meta, data } = await resultSet.json();
    return { rows: data, columns: meta };
  },
});

const createRegionAnalysisForQuerySfContext = (options: {
  snowflakeSql: string;
}) => ({
  sql: options.snowflakeSql,
  lets: "",
  wheres: "",
  executeQuery: async (
    context: QueryContext,
    supplierRegionPairs: (readonly [string, string])[],
  ) => {
    // Create unique supplier -> region mapping (deduplicating suppliers)
    const supplierRegionMap = new Map<string, string>();
    supplierRegionPairs.forEach(([supplier, region]) => {
      supplierRegionMap.set(supplier, region);
    });
    const uniqueMappings = Array.from(supplierRegionMap.entries());

    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: `
        DECLARE
          res RESULTSET;
        BEGIN
          ${context.lets}
        res := (
          WITH SUPPLIER_GROUPING AS (
              SELECT *
              FROM (VALUES
                ${uniqueMappings.map(([supplier, region]) => `(${supplier}, '${region}')`).join(",")}
              ) AS v(SUPPLIER_NO, REGION)
          )
          ${context.sql.replace(WHERES_MARKER, context.wheres || "")}
        );
        RETURN TABLE(res);
        END
            `,
      db: sfDb,
    });
    return { rows, columns };
  },
});

const getRegionAnalysisForQuery = (options: {
  isBeef: boolean;
  snowflakeSql: string;
  clickHouseSql: string;
}) => {
  if (isClickHouseEnabled()) {
    return getRegionAnalysisForQueryHandler({
      isBeef: options.isBeef,
      createContext: () =>
        createRegionAnalysisForQueryChContext({
          clickHouseSql: options.clickHouseSql,
        }),
    });
  } else {
    return getRegionAnalysisForQueryHandler({
      isBeef: options.isBeef,
      createContext: () =>
        createRegionAnalysisForQuerySfContext({
          snowflakeSql: options.snowflakeSql,
        }),
    });
  }
};

const getRegionAnalysisForQueryHandler =
  (options: { isBeef: boolean; createContext: () => QueryContext }) =>
  async (req: AuthenticatedRequest, res: Response) => {
    const { error, reqQuery } = await getReqQueryIfPermitted(
      req,
      reqQuerySchema,
    );

    if (error) {
      res.status(error.statusCode).send(error.message);
      return;
    }

    const rmSuppliers = await getRmSuppliers(options.isBeef);
    const supplierRegionPairs =
      rmSuppliers?.flatMap((i) => {
        const region = RM_REGION_MAP[i.rm.displayName] ?? i.rm.displayName;
        return i.allSupplierNumbers.map((j) => [j, region] as const);
      }) ?? [];

    const context = options.createContext();
    if (reqQuery?.brands) {
      context.wheres = ` AND v.brand in ('${reqQuery.brands.join("','")}')`;
    }
    const { rows, columns } = await context.executeQuery(
      context,
      supplierRegionPairs,
    );

    res.json({
      rows,
      columns,
    });
  };

const sfFieldGroupAvg = (
  sourceFieldSuffix: string,
  resultFieldSuffix: string,
) =>
  `sum(iff(SUM_${sourceFieldSuffix} > 0,SUM_${sourceFieldSuffix}, null)) as GROUP_SUM_${resultFieldSuffix},
  sum(iff(COUNT_${sourceFieldSuffix} > 0, COUNT_${sourceFieldSuffix}, null)) as GROUP_COUNT_${resultFieldSuffix},
  GROUP_SUM_${resultFieldSuffix} / GROUP_COUNT_${resultFieldSuffix} as AVG_${resultFieldSuffix}`;

const chFieldGroupAvg = (
  sourceFieldSuffix: string,
  resultFieldSuffix: string,
) =>
  `sum(if(sum_${sourceFieldSuffix} > 0,sum_${sourceFieldSuffix}, null)) as GROUP_SUM_${resultFieldSuffix},
  sum(if(count_${sourceFieldSuffix} > 0, count_${sourceFieldSuffix}, null)) as GROUP_COUNT_${resultFieldSuffix},
  GROUP_SUM_${resultFieldSuffix} / GROUP_COUNT_${resultFieldSuffix} as AVG_${resultFieldSuffix}`;

const sfBeefQuery = `
  select
    COALESCE(sg.REGION, 'Unknown') AS REGION,
    MOB_DATE,
    ${sfFieldGroupAvg("AUS_MARBLING", "AUS_MARBLING")},
    sum(COUNT_2_PLUS) as GROUP_SUM_HEADCOUNT_2_PLUS,
    sum(iff(COUNT_2_PLUS > 0, 1, null)) as GROUP_COUNT_HEADCOUNT_2_PLUS,
    GROUP_SUM_HEADCOUNT_2_PLUS / GROUP_COUNT_HEADCOUNT_2_PLUS as AVG_HEADCOUNT_2_PLUS,
    ${sfFieldGroupAvg("COLDWEIGHT", "CCS_WEIGHT_KG")},
    sum(COUNT_COLDWEIGHT) as GROUP_COUNT
  from
    ${sfDb}.MEQINSIGHTS.ALLIANCE_INSIGHTS_BEEF_TRAITS_OVER_TIME v
    LEFT JOIN SUPPLIER_GROUPING sg ON v.SUPPLIER_NO = sg.SUPPLIER_NO
  where
    1=1
    ${WHERES_MARKER}
  group by MOB_DATE, sg.REGION
  order by MOB_DATE
`;

const chBeefQuery = `
  select
    coalesce(sg.region, 'Unknown') AS REGION,
    mob_date as MOB_DATE,
    ${chFieldGroupAvg("aus_marbling", "AUS_MARBLING")},
    sum(count_2_plus) as GROUP_SUM_HEADCOUNT_2_PLUS,
    sum(if(count_2_plus > 0, 1, null)) as GROUP_COUNT_HEADCOUNT_2_PLUS,
    GROUP_SUM_HEADCOUNT_2_PLUS / GROUP_COUNT_HEADCOUNT_2_PLUS as AVG_HEADCOUNT_2_PLUS,
    ${chFieldGroupAvg("coldweight", "CCS_WEIGHT_KG")},
    sum(count_coldweight) as GROUP_COUNT
  from
    ${chDb}.alliance_insights_beef_traits_over_time v
    left join supplier_grouping sg on v.supplier_no = sg.supplier_no
  where
    1=1
    ${WHERES_MARKER}
  group by mob_date, sg.region
  order by mob_date
`;

export const getRegionAnalysisBeef = getRegionAnalysisForQuery({
  isBeef: true,
  snowflakeSql: sfBeefQuery,
  clickHouseSql: chBeefQuery,
});

const sfLambQuery = `
  select
    COALESCE(sg.REGION, 'Unknown') AS REGION,
    MOB_DATE,
    ${sfFieldGroupAvg("IMF_PCT", "IMF_PERCENTAGE_OF")},
    ${sfFieldGroupAvg("LMY_PCT", "LMY_PERCENTAGE_OF")},
    ${sfFieldGroupAvg("HSCW", "HOTWEIGHT_KG")},
    sum(COUNT_IMF_PCT) as GROUP_COUNT
  from
    ${sfDb}.MEQINSIGHTS.ALLIANCE_INSIGHTS_LAMB_TRAITS_OVER_TIME v
    LEFT JOIN SUPPLIER_GROUPING sg ON v.SUPPLIER_NO = sg.SUPPLIER_NO
  where
    1=1
    ${WHERES_MARKER}
  group by v.MOB_DATE, sg.REGION
  order by v.MOB_DATE
`;

const chLambQuery = `
  select
    coalesce(sg.region, 'Unknown') AS REGION,
    mob_date as MOB_DATE,
    ${chFieldGroupAvg("imf_pct", "IMF_PERCENTAGE_OF")},
    ${chFieldGroupAvg("lmy_pct", "LMY_PERCENTAGE_OF")},
    ${chFieldGroupAvg("hscw", "HOTWEIGHT_KG")},
    sum(count_imf_pct) as GROUP_COUNT
  from
    ${chDb}.alliance_insights_lamb_traits_over_time v
    left join supplier_grouping sg on v.supplier_no = sg.supplier_no
  where
    1=1
    ${WHERES_MARKER}
  group by mob_date, sg.region
  order by mob_date
`;

export const getRegionAnalysisLamb = getRegionAnalysisForQuery({
  isBeef: false,
  snowflakeSql: sfLambQuery,
  clickHouseSql: chLambQuery,
});

const sfLambSweetSpotsQuery = `
  select
    MOB_DATE,
    COALESCE(sg.REGION, 'Unknown') AS REGION,
    count (*) as TOTAL,
    sum(iff(MEQ_IMF_PERC >= 2 AND MEQ_IMF_PERC < 3, 1, null)) as GROUP_COUNT_IMF_2_3,
    sum(iff(MEQ_IMF_PERC >= 3, 1, null)) as GROUP_COUNT_IMF_3_PLUS,
  from
    ${sfDb}.MEQINSIGHTS.ALLIANCE_INSIGHTS_LAMB_DETAILED v
    LEFT JOIN SUPPLIER_GROUPING sg ON v.SUPPLIER_NO = sg.SUPPLIER_NO
  where
    1=1
    ${WHERES_MARKER}
  group by sg.REGION, v.MOB_DATE
  order by sg.REGION, v.MOB_DATE
`;

const chLambSweetSpotsQuery = `
  select
    mob_date as MOB_DATE,
    coalesce(sg.region, 'Unknown') AS REGION,
    count (*) as TOTAL,
    sum(if(meq_imf_perc >= 2 AND meq_imf_perc < 3, 1, null)) as GROUP_COUNT_IMF_2_3,
    sum(if(meq_imf_perc >= 3, 1, null)) as GROUP_COUNT_IMF_3_PLUS
  from
    ${chDb}.alliance_insights_lamb_detailed v
    left join supplier_grouping sg on v.supplier_no = sg.supplier_no
  where
    1=1
    ${WHERES_MARKER}
  group by sg.region, v.mob_date
  order by sg.region, v.mob_date
`;

export const getRegionAnalysisLambSweetSpots = getRegionAnalysisForQuery({
  isBeef: false,
  snowflakeSql: sfLambSweetSpotsQuery,
  clickHouseSql: chLambSweetSpotsQuery,
});

const sfLambSuppliersQuery = `
  select
    v.SUPPLIER_NO,
    COALESCE(sg.REGION, 'Unknown') AS REGION,
    ${sfFieldGroupAvg("IMF_PCT", "IMF_PERCENTAGE_OF")},
    ${sfFieldGroupAvg("LMY_PCT", "LMY_PERCENTAGE_OF")},
    ${sfFieldGroupAvg("HSCW", "HOTWEIGHT_KG")},
    sum(COUNT_IMF_PCT) as GROUP_COUNT
  from
    ${sfDb}.MEQINSIGHTS.ALLIANCE_INSIGHTS_LAMB_TRAITS_OVER_TIME v
    LEFT JOIN SUPPLIER_GROUPING sg ON v.SUPPLIER_NO = sg.SUPPLIER_NO
  where
    1=1
    ${WHERES_MARKER}
  group by v.SUPPLIER_NO, COALESCE(sg.REGION, 'Unknown')
  order by v.SUPPLIER_NO, COALESCE(sg.REGION, 'Unknown')
`;

const chLambSuppliersQuery = `
  select
    v.supplier_no as SUPPLIER_NO,
    coalesce(sg.region, 'Unknown') AS REGION,
    ${chFieldGroupAvg("imf_pct", "IMF_PERCENTAGE_OF")},
    ${chFieldGroupAvg("lmy_pct", "LMY_PERCENTAGE_OF")},
    ${chFieldGroupAvg("hscw", "HOTWEIGHT_KG")},
    sum(count_imf_pct) as GROUP_COUNT
  from
    ${chDb}.alliance_insights_lamb_traits_over_time v
    left join supplier_grouping sg on v.supplier_no = sg.supplier_no
  where
    1=1
    ${WHERES_MARKER}
  group by v.supplier_no, coalesce(sg.region, 'Unknown')
  order by v.supplier_no, coalesce(sg.region, 'Unknown')
`;

export const getRegionAnalysisLambSuppliers = getRegionAnalysisForQuery({
  isBeef: false,
  snowflakeSql: sfLambSuppliersQuery,
  clickHouseSql: chLambSuppliersQuery,
});

const createLsrAnalysisForQueryChContext = (options: {
  clickHouseSql: string;
}) => ({
  sql: options.clickHouseSql,
  lets: "",
  wheres: "",
  queryParams: {},
  executeQuery: async (
    context: QueryContext,
    supplierRegionPairs: (readonly [string, string])[],
  ) => {
    // Create unique supplier -> region mapping (deduplicating suppliers)
    const supplierRegionMap = new Map<string, string>();
    supplierRegionPairs.forEach(([supplier, region]) => {
      supplierRegionMap.set(supplier, region);
    });
    const uniqueMappings = Array.from(supplierRegionMap.entries());

    const resultSet = await chClient().query({
      query: `
        with supplier_grouping as (
          select *
          from values(
            'supplier_no Int32, region Nullable(String)',
            ${uniqueMappings.map(([supplier, region]) => `(${supplier}, '${region}')`).join(",")}
          )
        )
        ${context.sql.replace(WHERES_MARKER, context.wheres || "")}
      `,
      format: "JSON",
      query_params: context.queryParams,
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    });

    const { meta, data } = await resultSet.json();
    return { rows: data, columns: meta };
  },
});

const createLsrAnalysisForQuerySfContext = (options: {
  snowflakeSql: string;
}) => ({
  sql: options.snowflakeSql,
  lets: "",
  wheres: "",
  executeQuery: async (
    context: QueryContext,
    supplierRegionPairs: (readonly [string, string])[],
  ) => {
    // Create unique supplier -> region mapping (deduplicating suppliers)
    const supplierRegionMap = new Map<string, string>();
    supplierRegionPairs.forEach(([supplier, region]) => {
      supplierRegionMap.set(supplier, region);
    });
    const uniqueMappings = Array.from(supplierRegionMap.entries());

    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: `
        DECLARE
          res RESULTSET;
        BEGIN
          ${context.lets}
        res := (
          WITH SUPPLIER_GROUPING AS (
              SELECT *
              FROM (VALUES
                ${uniqueMappings.map(([supplier, region]) => `(${supplier}, '${region}')`).join(",")}
              ) AS v(SUPPLIER_NO, REGION)
          )
          ${context.sql.replace(WHERES_MARKER, context.wheres || "")}
        );
        RETURN TABLE(res);
        END
      `,
      db: sfDb,
    });
    return { rows, columns };
  },
});

const getLsrAnalysisForQuery = (options: {
  isBeef: boolean;
  snowflakeSql: string;
  clickHouseSql: string;
}) => {
  if (isClickHouseEnabled()) {
    return getLsrAnalysisForQueryHandler({
      isBeef: options.isBeef,
      createContext: () =>
        createLsrAnalysisForQueryChContext({
          clickHouseSql: options.clickHouseSql,
        }),
    });
  } else {
    return getLsrAnalysisForQueryHandler({
      isBeef: options.isBeef,
      createContext: () =>
        createLsrAnalysisForQuerySfContext({
          snowflakeSql: options.snowflakeSql,
        }),
    });
  }
};

const getLsrAnalysisForQueryHandler =
  (options: { isBeef: boolean; createContext: () => QueryContext }) =>
  async (req: AuthenticatedRequest, res: Response) => {
    const { error, reqQuery } = await getReqQueryIfPermitted(
      req,
      reqQuerySchema,
    );

    if (error) {
      res.status(error.statusCode).send(error.message);
      return;
    }
    console.log(1111);

    const lsrSuppliers = await getLsrSuppliers(options.isBeef);
    console.log(2222);
    const supplierRegionPairs =
      lsrSuppliers?.flatMap((i) => {
        const region = RM_REGION_MAP[i.lsr.displayName] ?? i.lsr.displayName;
        return i.allSupplierNumbers.map((j) => [j, region] as const);
      }) ?? [];

    console.log(3333);
    const context = options.createContext();
    if (reqQuery?.brands) {
      context.wheres = ` AND v.brand in ('${reqQuery.brands.join("','")}')`;
    }
    console.log(4444);
    const { rows, columns } = await context.executeQuery(
      context,
      supplierRegionPairs,
    );

    console.log(5555);

    res.json({
      rows,
      columns,
    });
  };

export const getLsrAnalysisBeef = getLsrAnalysisForQuery({
  isBeef: true,
  snowflakeSql: sfBeefQuery,
  clickHouseSql: chBeefQuery,
});

export const getLsrAnalysisLamb = getLsrAnalysisForQuery({
  isBeef: false,
  snowflakeSql: sfLambQuery,
  clickHouseSql: chLambQuery,
});

export const getLsrAnalysisLambSweetSpots = getLsrAnalysisForQuery({
  isBeef: false,
  snowflakeSql: sfLambSweetSpotsQuery,
  clickHouseSql: chLambSweetSpotsQuery,
});

const brandsReqQuerySchema = z.object({
  isBeef: z
    .string()
    .transform((val) => JSON.parse(val) as boolean)
    .pipe(z.boolean()),
});
export const getBrands = async (req: AuthenticatedRequest, res: Response) => {
  const { error, reqQuery } = await getReqQueryIfPermitted(
    req,
    brandsReqQuerySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  if (isClickHouseEnabled()) {
    const query = `select distinct brand as BRAND from ${chDb}.alliance_insights_${reqQuery?.isBeef ? "beef" : "lamb"}_traits_over_time`;
    const resultSet = await chClient().query({
      query,
      format: "JSON",
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    });

    const { meta, data } = await resultSet.json();
    res.json({ rows: data, columns: meta });
  } else {
    const sqlText = `select distinct  BRAND from ${sfDb}.MEQINSIGHTS.ALLIANCE_INSIGHTS_${reqQuery?.isBeef ? "BEEF" : "LAMB"}_TRAITS_OVER_TIME`;
    const { rows, columns } = await executeSnowflakeStatement({
      sqlText,
      db: sfDb,
    });
    res.json({
      rows,
      columns,
    });
  }
};
