import { Response } from "express";
import { z } from "zod";

import { AuthenticatedRequest } from "../Common";
import { PkgName } from "../../utils/Types";
import {
  getReqBodyIfOrgLocationPermitted,
  orgLocationBodySchema,
} from "../shared";
import { findDb } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { getOptimisedColumns } from "../../utils/Helper";
import {
  chClient,
  findDatabaseForPackage,
  isClickHouseEnabled,
  removeUnusedColumnsInQueryResult,
} from "../../utils/ClickHouse";

const reqBodySchema = orgLocationBodySchema.extend({
  org: z
    .string()
    .toLowerCase()
    .regex(/^alliance$/),
  mobDate: z.string().date(),
  supplier: z.coerce.number().int(),
  killsheet: z.coerce.number().int(),
});

export async function queryAllianceLambGetGridReportKillsheet(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfOrgLocationPermitted(
    req,
    PkgName.ALLIANCE_DATA_ANALYSIS,
    reqBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  if (isClickHouseEnabled()) {
    const db = findDatabaseForPackage(PkgName.ALLIANCE_DATA_ANALYSIS);

    const resultSet = await chClient().query({
      query: `
        select
          grade_name as GRADE_NAME,
          head as HEAD,
          hotweight as HOTWEIGHT
        from ${db}.alliance_lamb_get_grid_report_killsheet(
          mob_date={mobDate:Date32},
          location={location:String},
          supplier_no={supplier:Int32},
          killsheet={killsheet:Int32}
        )
      `,
      format: "JSON",
      query_params: {
        mobDate: reqBody!.mobDate,
        location: reqBody!.location,
        supplier: reqBody!.supplier,
        killsheet: reqBody!.killsheet,
      },
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    });

    const { meta, data } = removeUnusedColumnsInQueryResult(
      await resultSet.json(),
    );
    res.json({ rows: data, columns: meta, numRows: data.length });
  } else {
    const db = findDb();

    console.log("binds", [
      reqBody!.mobDate,
      reqBody!.location,
      reqBody!.supplier,
      reqBody!.killsheet,
    ]);

    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: `
        set mobDate = ?;
        set location = ?;
        set supplier = ?::INTEGER;
        set killsheet = ?::INTEGER;
        select *
        from table(
          ${db}.MEQINSIGHTS.ALLIANCE_LAMB_GET_GRID_REPORT_KILLSHEET(
            $mobDate,
            $location,
            $supplier,
            $killsheet
          )
        )
      `,
      binds: [
        reqBody!.mobDate,
        reqBody!.location,
        reqBody!.supplier,
        reqBody!.killsheet.toString(),
      ],
      parameters: {
        MULTI_STATEMENT_COUNT: 5,
      },
      db,
    });

    const optimised = getOptimisedColumns({ columns, rows });

    res.json({
      ...optimised,
      numRows: optimised.rows?.length || 0,
    });
  }
}
