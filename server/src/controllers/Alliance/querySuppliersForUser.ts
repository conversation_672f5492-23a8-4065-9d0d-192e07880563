import _ from "lodash";

import * as Models from "../../models";
import { ALL_LOCATIONS, SUPER_ADMIN, SUPER_USER } from "../../utils/Constants";
import {
  ALLIANCE_ROLE,
  LooseObject,
  Message,
  ResultCode,
} from "../../utils/Types";
import { User } from "../../models/Interfaces";
import { hasPermission, isNotEmpty } from "../../utils/Helper";
import { getSuppliers } from "./shared";

const allAllianceRoles = async () =>
  await Models.Role.find({ idString: /^Alliance/ }).then((o) =>
    o.map((i) => i.idString),
  );

export async function querySuppliersForUser(
  location: string,
  user: User,
  isBeef: boolean,
): Promise<{ msg: Message; code: ResultCode; data: any[] | undefined }> {
  const roles = user.roles;

  const result: { msg: Message; code: ResultCode; data: any[] | undefined } = {
    msg: Message.FAILED,
    data: undefined,
    code: ResultCode.FAILED,
  };

  if (location) {
    if (roles) {
      if (hasPermission({ roles, requiredRoles: await allAllianceRoles() })) {
        const dataSource = await getSuppliers(location, isBeef);

        if (isNotEmpty(dataSource.rows)) {
          const allSuppliersFromSnowflake = dataSource.rows?.filter((i) =>
            isNotEmpty(i.SUPPLIER_NO),
          );

          let allSuppliers: any[] | undefined = [];
          if (
            _.intersection(roles, [
              SUPER_ADMIN,
              SUPER_USER,
              ALLIANCE_ROLE.ALL_LOCATIONS_ADMIN,
              ALLIANCE_ROLE.ALL_LOCATIONS_USER,
              ALLIANCE_ROLE.ALL_LOCATIONS_ADMIN.replace(
                ALL_LOCATIONS,
                location,
              ),
              ALLIANCE_ROLE.ALL_LOCATIONS_USER.replace(ALL_LOCATIONS, location),
            ]).length > 0
          ) {
            allSuppliers = allSuppliersFromSnowflake;
          } else {
            if (isBeef) {
              if (roles.includes(ALLIANCE_ROLE.ALL_LOCATIONS_LSR)) {
                if (user.allianceBeefSuppliers) {
                  const allSupplierNumbersFromItsSuppliers: string[] = _.uniq(
                    _.flattenDeep(
                      (
                        await Models.User.find({
                          email: { $in: user.allianceBeefSuppliers },
                        })
                      ).map((u) => u.allianceBeefSupplierNumbers),
                    ),
                  );
                  allSuppliers = allSuppliersFromSnowflake?.filter((i) =>
                    allSupplierNumbersFromItsSuppliers.includes(
                      i.SUPPLIER_NO?.toString(),
                    ),
                  );
                }
              } else if (roles.includes(ALLIANCE_ROLE.ALL_LOCATIONS_RM)) {
                if (user.allianceBeefLSRs) {
                  const allSuppliersFromItsLSRs: string[] = _.uniq(
                    _.flattenDeep(
                      (
                        await Models.User.find({
                          email: { $in: user.allianceBeefLSRs },
                        })
                      ).map((u) => u.allianceBeefSuppliers),
                    ),
                  );
                  const allSupplierNumbersFromItsSuppliers: string[] = _.uniq(
                    _.flattenDeep(
                      (
                        await Models.User.find({
                          email: { $in: allSuppliersFromItsLSRs },
                        })
                      ).map((u) => u.allianceBeefSupplierNumbers),
                    ),
                  );
                  allSuppliers = allSuppliersFromSnowflake?.filter((i) =>
                    allSupplierNumbersFromItsSuppliers.includes(
                      i.SUPPLIER_NO?.toString(),
                    ),
                  );
                }
              } else {
                allSuppliers = allSuppliersFromSnowflake?.filter((i) =>
                  user.allianceBeefSupplierNumbers?.includes(
                    i.SUPPLIER_NO?.toString(),
                  ),
                );
              }
            } else {
              if (roles.includes(ALLIANCE_ROLE.ALL_LOCATIONS_LSR)) {
                if (user.allianceLambSuppliers) {
                  const allSupplierNumbersFromItsSuppliers: string[] = _.uniq(
                    _.flattenDeep(
                      (
                        await Models.User.find({
                          email: { $in: user.allianceLambSuppliers },
                        })
                      ).map((u) => u.allianceLambSupplierNumbers),
                    ),
                  );
                  allSuppliers = allSuppliersFromSnowflake?.filter((i) =>
                    allSupplierNumbersFromItsSuppliers.includes(
                      i.SUPPLIER_NO?.toString(),
                    ),
                  );
                }
              } else if (roles.includes(ALLIANCE_ROLE.ALL_LOCATIONS_RM)) {
                if (user.allianceLambLSRs) {
                  const allSuppliersFromItsLSRs: string[] = _.uniq(
                    _.flattenDeep(
                      (
                        await Models.User.find({
                          email: { $in: user.allianceLambLSRs },
                        })
                      ).map((u) => u.allianceLambSuppliers),
                    ),
                  );
                  const allSupplierNumbersFromItsSuppliers: string[] = _.uniq(
                    _.flattenDeep(
                      (
                        await Models.User.find({
                          email: { $in: allSuppliersFromItsLSRs },
                        })
                      ).map((u) => u.allianceLambSupplierNumbers),
                    ),
                  );
                  allSuppliers = allSuppliersFromSnowflake?.filter((i) =>
                    allSupplierNumbersFromItsSuppliers.includes(
                      i.SUPPLIER_NO?.toString(),
                    ),
                  );
                }
              } else {
                allSuppliers = allSuppliersFromSnowflake?.filter((i) =>
                  user.allianceLambSupplierNumbers?.includes(
                    i.SUPPLIER_NO?.toString(),
                  ),
                );
              }
            }
          }

          // get all supplier numbers with businessName
          const supplierNumbersWithBusinessName: LooseObject = {};
          if (isBeef) {
            const allAllianceUsers = await Models.User.find({
              allianceBeefSupplierNumbers: {
                $ne: null,
                $exists: true,
                $not: { $size: 0 },
              },
            });
            allAllianceUsers.forEach((allianceUser) => {
              allianceUser.allianceBeefSupplierNumbers?.forEach(
                (supplierNumber, i) => {
                  supplierNumbersWithBusinessName[supplierNumber] = _.get(
                    allianceUser.allianceBeefBusinessNames,
                    i,
                  );
                },
              );
            });
          } else {
            const allAllianceUsers = await Models.User.find({
              allianceLambSupplierNumbers: {
                $ne: null,
                $exists: true,
                $not: { $size: 0 },
              },
            });
            allAllianceUsers.forEach((allianceUser) => {
              allianceUser.allianceLambSupplierNumbers?.forEach(
                (supplierNumber, i) => {
                  supplierNumbersWithBusinessName[supplierNumber] = _.get(
                    allianceUser.allianceLambBusinessNames,
                    i,
                  );
                },
              );
            });
          }

          allSuppliers = allSuppliers?.map((i) => ({
            ...i,
            fullName: i.SUPPLIER_NO
              ? supplierNumbersWithBusinessName[i.SUPPLIER_NO] || "Other"
              : "",
          }));

          result.data = allSuppliers;
          result.msg = Message.SUCCESS;
          result.code = ResultCode.SUCCESS;
        } else {
          result.msg = Message.ERROR_DATA_SOURCE_NOT_AVAILABLE;
        }
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  return result;
}

export async function querySupplierNumbersForUserRm(
  location: string,
  user: User,
  isBeef: boolean,
): Promise<{ msg: Message; code: ResultCode; data: any[] | undefined }> {
  const roles = user.roles;

  const result: { msg: Message; code: ResultCode; data: any[] | undefined } = {
    msg: Message.FAILED,
    data: undefined,
    code: ResultCode.FAILED,
  };

  if (location) {
    if (roles) {
      if (hasPermission({ roles, requiredRoles: await allAllianceRoles() })) {
        const dataSource = await getSuppliers(location, isBeef);

        if (isNotEmpty(dataSource?.rows)) {
          const allSupplierNumbersFromSnowflake = dataSource.rows
            ?.filter((i) => isNotEmpty(i.SUPPLIER_NO))
            .map((s) => s.SUPPLIER_NO);

          let allSupplierNumbers: any[] | undefined = [];

          let rm: User | undefined = undefined;
          if (isBeef) {
            if (roles.includes(ALLIANCE_ROLE.ALL_LOCATIONS_RM)) {
              rm = user;
            } else if (roles.includes(ALLIANCE_ROLE.ALL_LOCATIONS_LSR)) {
              rm = (
                await Models.User.find({
                  allianceBeefLSRs: user.email,
                })
              ).at(0);
            } else {
              const lsr = (
                await Models.User.find({
                  allianceBeefSuppliers: user.email,
                })
              ).at(0);
              if (lsr) {
                rm = (
                  await Models.User.find({
                    allianceBeefLSRs: lsr.email,
                  })
                ).at(0);
              }
            }

            if (rm?.allianceBeefLSRs) {
              const allSuppliersFromItsLSRs: string[] = _.uniq(
                _.flattenDeep(
                  (
                    await Models.User.find({
                      email: { $in: rm.allianceBeefLSRs },
                    })
                  ).map((u) => u.allianceBeefSuppliers),
                ),
              );
              const allSupplierNumbersFromItsSuppliers: string[] = _.uniq(
                _.flattenDeep(
                  (
                    await Models.User.find({
                      email: { $in: allSuppliersFromItsLSRs },
                    })
                  ).map((u) => u.allianceBeefSupplierNumbers),
                ),
              );
              allSupplierNumbers = _.intersection(
                allSupplierNumbersFromItsSuppliers,
                allSupplierNumbersFromSnowflake,
              );
            }
          } else {
            //isLamb
            if (roles.includes(ALLIANCE_ROLE.ALL_LOCATIONS_RM)) {
              rm = user;
            } else if (roles.includes(ALLIANCE_ROLE.ALL_LOCATIONS_LSR)) {
              rm = (
                await Models.User.find({
                  allianceLambLSRs: user.email,
                })
              ).at(0);
            } else {
              const lsr = (
                await Models.User.find({
                  allianceLambSuppliers: user.email,
                })
              ).at(0);
              if (lsr) {
                rm = (
                  await Models.User.find({
                    allianceLambLSRs: lsr.email,
                  })
                ).at(0);
              }
            }

            if (rm?.allianceLambLSRs) {
              const allSuppliersFromItsLSRs: string[] = _.uniq(
                _.flattenDeep(
                  (
                    await Models.User.find({
                      email: { $in: rm.allianceLambLSRs },
                    })
                  ).map((u) => u.allianceLambSuppliers),
                ),
              );
              const allSupplierNumbersFromItsSuppliers: string[] = _.uniq(
                _.flattenDeep(
                  (
                    await Models.User.find({
                      email: { $in: allSuppliersFromItsLSRs },
                    })
                  ).map((u) => u.allianceLambSupplierNumbers),
                ),
              );
              allSupplierNumbers = _.intersection(
                allSupplierNumbersFromItsSuppliers,
                allSupplierNumbersFromSnowflake,
              );
            }
          }

          if (allSupplierNumbers) {
            result.data = allSupplierNumbers;
            result.msg = Message.SUCCESS;
            result.code = ResultCode.SUCCESS;
          }
        } else {
          result.msg = Message.ERROR_DATA_SOURCE_NOT_AVAILABLE;
        }
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  return result;
}

export async function queryLSRsForUser(
  location: string,
  user: User,
  isBeef: boolean,
): Promise<{ msg: Message; code: ResultCode; data: any[] | undefined }> {
  const roles = user.roles;

  const result: { msg: Message; code: ResultCode; data: any[] | undefined } = {
    msg: Message.FAILED,
    data: undefined,
    code: ResultCode.FAILED,
  };

  if (location) {
    if (roles) {
      if (hasPermission({ roles, requiredRoles: await allAllianceRoles() })) {
        let lsrs: User[] = [];
        let supplierNumbersByLSR: { [key: string]: string[] } = {};

        if (
          _.intersection(roles, [
            SUPER_ADMIN,
            SUPER_USER,
            ALLIANCE_ROLE.ALL_LOCATIONS_ADMIN,
          ]).length > 0
        ) {
          // Admin case - get all LSRs
          if (isBeef) {
            lsrs = await Models.User.find({
              roles: ALLIANCE_ROLE.ALL_LOCATIONS_LSR,
              allianceBeefSuppliers: { $exists: true, $ne: [] },
            });
          } else {
            lsrs = await Models.User.find({
              roles: ALLIANCE_ROLE.ALL_LOCATIONS_LSR,
              allianceLambSuppliers: { $exists: true, $ne: [] },
            });
          }
        } else if (roles.includes(ALLIANCE_ROLE.ALL_LOCATIONS_RM)) {
          // RM case - get their LSRs
          if (isBeef) {
            lsrs = await Models.User.find({
              email: { $in: user.allianceBeefLSRs },
            });
          } else {
            lsrs = await Models.User.find({
              email: { $in: user.allianceLambLSRs },
            });
          }
        } else {
          result.msg = Message.ERROR_PERMISSION_DENIED;
          return result;
        }

        // For each LSR, get their supplier numbers
        for (const lsr of lsrs) {
          if (isBeef) {
            const suppliers = await Models.User.find({
              email: { $in: lsr.allianceBeefSuppliers },
            });
            supplierNumbersByLSR[lsr.email] = _.uniq(
              _.flattenDeep(
                suppliers.map((u) => u.allianceBeefSupplierNumbers),
              ),
            );
          } else {
            const suppliers = await Models.User.find({
              email: { $in: lsr.allianceLambSuppliers },
            });
            supplierNumbersByLSR[lsr.email] = _.uniq(
              _.flattenDeep(
                suppliers.map((u) => u.allianceLambSupplierNumbers),
              ),
            );
          }
        }

        // Format the response
        result.data = lsrs.map((lsr) => ({
          ..._.pick((lsr as any).toObject(), [
            "displayName",
            "email",
            "firstName",
            "lastName",
          ]),
          supplierNumbers: supplierNumbersByLSR[lsr.email] || [],
        }));

        result.msg = Message.SUCCESS;
        result.code = ResultCode.SUCCESS;
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  return result;
}
