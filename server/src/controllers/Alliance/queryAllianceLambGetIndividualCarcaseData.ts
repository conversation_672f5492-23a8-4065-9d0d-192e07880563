import { Response } from "express";
import { z } from "zod";

import { AuthenticatedRequest } from "../Common";
import { PkgName } from "../../utils/Types";
import {
  getReqBodyIfOrgLocationPermitted,
  orgLocationBodySchema,
} from "../shared";
import { findDb } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { getOptimisedColumns } from "../../utils/Helper";
import {
  chClient,
  findDatabaseForPackage,
  isClickHouseEnabled,
  jsonCompactToJsonResultSet,
  removeUnusedColumnsInQueryResult,
} from "../../utils/ClickHouse";
import { ALL } from "../../utils/Constants";

const reqBodySchema = orgLocationBodySchema.extend({
  org: z
    .string()
    .toLowerCase()
    .regex(/^alliance$/),
  startDate: z.string().date(),
  endDate: z.string().date(),
  brand: z.coerce.string(),
  supplier: z.coerce.string().regex(/^(\d+)|(all)$/i),
});

export async function queryAllianceLambGetIndividualCarcaseData(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfOrgLocationPermitted(
    req,
    PkgName.ALLIANCE_DATA_ANALYSIS,
    reqBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  if (isClickHouseEnabled()) {
    const db = findDatabaseForPackage(PkgName.ALLIANCE_DATA_ANALYSIS);

    const resultSet = await chClient().query({
      query: `
        select
          location as LOCATION,
          chain_no as CHAIN_NO,
          mob_date as MOB_DATE,
          tag_id as TAG_ID,
          brand as BRAND,
          hotweight_kg as HOTWEIGHT_KG,
          imf_percentage_of as IMF_PERCENTAGE_OF,
          viascan_yield_percentage_of as VIASCAN_YIELD_PERCENTAGE_OF,
          viascan_gr as VIASCAN_GR
        from ${db}.alliance_lamb_get_individual_carcase_data(
          start_date={start_date:String},
          end_date={end_date:String},
          location={location:String},
          brand={brand:String},
          supplier_no={supplier:String}
        )
      `,
      format: "JSONCompact",
      query_params: {
        start_date: reqBody!.startDate,
        end_date: reqBody!.endDate,
        location: reqBody!.location,
        brand: reqBody!.brand,
        supplier: reqBody!.supplier,
      },
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    });

    const responseJson = jsonCompactToJsonResultSet(await resultSet.json());
    const { meta, data } = removeUnusedColumnsInQueryResult(responseJson);

    res.json({ rows: data, columns: meta, numRows: data.length });
  } else {
    const db = findDb();

    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: `
        set start_date = ?;
        set end_date = ?;
        set location = ?;
        set brand = ?;
        set supplier = ?;
        select *
        from table(
          ${db}.MEQINSIGHTS.ALLIANCE_LAMB_GET_INDIVIDUAL_CARCASE_DATA(
            $start_date,
            $end_date,
            $location,
            $brand,
            $supplier
          )
        )
      `,
      binds: [
        reqBody!.startDate,
        reqBody!.endDate,
        reqBody!.location === "all" ? ALL : reqBody!.location,
        reqBody!.brand,
        reqBody!.supplier,
      ],
      parameters: {
        MULTI_STATEMENT_COUNT: 6,
      },
      db,
    });

    const optimised = getOptimisedColumns({ columns, rows });
    res.json({
      ...optimised,
      numRows: optimised.rows?.length || 0,
    });
  }
}
