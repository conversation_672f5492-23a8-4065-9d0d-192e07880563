import { Response } from "express";
import { z } from "zod";

import { AuthenticatedRequest } from "../Common";
import { PkgName } from "../../utils/Types";
import {
  getReqBodyIfOrgLocationPermitted,
  orgLocationBodySchema,
} from "../shared";
import { findDb } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { getOptimisedColumns } from "../../utils/Helper";
import {
  chClient,
  findDatabaseForPackage,
  isClickHouseEnabled,
  removeUnusedColumnsInQueryResult,
} from "../../utils/ClickHouse";

const reqBodySchema = orgLocationBodySchema.extend({
  org: z
    .string()
    .toLowerCase()
    .regex(/^alliance$/),
  startDate: z.string().date(),
  endDate: z.string().date(),
  supplier: z.coerce.number().int(),
  killsheet: z.coerce.string().regex(/^\d+$/),
});

export async function queryAllianceBeefAggOnSupplierKillsheet(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfOrgLocationPermitted(
    req,
    PkgName.ALLIANCE_DATA_ANALYSIS,
    reqBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  if (isClickHouseEnabled()) {
    const db = findDatabaseForPackage(PkgName.ALLIANCE_DATA_ANALYSIS);

    const resultSet = await chClient().query({
      query: `
        select
          location as LOCATION,
          mob_date as MOB_DATE,
          supplier_no as SUPPLIER_NO,
          killsheet as KILLSHEET,
          num_in_mob as NUM_IN_MOB,
          avg_hscw as AVG_HSCW,
          avg_marbling as AVG_MARBLING,
          marbling_2_number as MARBLING_2_NUMBER,
          marbling_4_number as MARBLING_4_NUMBER
        from ${db}.alliance_beef_get_agg_on_supplier_killsheet(
          start_date={start_date:String},
          end_date={end_date:String},
          location={location:String},
          supplier_no={supplier:Int32},
          killsheet={killsheet:String}
        )
      `,
      format: "JSON",
      query_params: {
        start_date: reqBody!.startDate,
        end_date: reqBody!.endDate,
        location: reqBody!.location,
        supplier: reqBody!.supplier,
        killsheet: reqBody!.killsheet,
      },
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    });

    const { meta, data } = removeUnusedColumnsInQueryResult(
      await resultSet.json(),
    );
    res.json({ rows: data, columns: meta, numRows: data.length });
  } else {
    const db = findDb();

    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: `
        set start_date = ?;
        set end_date = ?;
        set location = ?;
        set supplier = ?::INTEGER;
        set killsheet = ?;
        select *
        from table(
          ${db}.MEQINSIGHTS.ALLIANCE_BEEF_GET_AGG_ON_SUPPLIER_KILLSHEET(
            $start_date,
            $end_date,
            $location,
            $supplier,
            $killsheet
          )
        )
      `,
      binds: [
        reqBody!.startDate,
        reqBody!.endDate,
        reqBody!.location,
        reqBody!.supplier,
        reqBody!.killsheet,
      ],
      parameters: {
        MULTI_STATEMENT_COUNT: 6,
      },
      db,
    });

    const optimised = getOptimisedColumns({ columns, rows });

    res.json({
      ...optimised,
      numRows: optimised.rows?.length || 0,
    });
  }
}
