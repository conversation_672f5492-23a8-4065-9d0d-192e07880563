import _ from "lodash";
import moment from "moment";
import { z } from "zod";

import * as Models from "../../models";
import { ALL, ALL_ORGS } from "../../utils/Constants";
import { AuthenticatedRequest } from "../Common";
import { StatusCodes } from "http-status-codes";
import {
  ALLIANCE_ROLE,
  DateFormat,
  ZitadelOrgIdString,
  PkgName,
} from "../../utils/Types";
import {
  getOrgFromRole,
  hasPermission,
  isNotEmpty,
  getDbLocationFromOrgLocation,
} from "../../utils/Helper";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { findSnowflakeClientDb } from "../../utils/SnowflakeDatabases";
import {
  chClient,
  findDatabaseForPackage,
  isClickHouseEnabled,
} from "../../utils/ClickHouse";

export const zitadelOrgIdString = ZitadelOrgIdString.ALLIANCE;

const allAllianceRoles = async () =>
  await Models.Role.find({ idString: /^Alliance/ }).then((o) =>
    o.map((i) => i.idString),
  );

export function findDb(): string {
  const db = findSnowflakeClientDb(zitadelOrgIdString);
  if (db === undefined) {
    throw new Error(`Database not found for ${zitadelOrgIdString}`);
  }
  return db;
}

export async function isAllianceUser(
  req: AuthenticatedRequest,
): Promise<boolean> {
  if (req.user?.roles == null) {
    return false;
  }

  return hasPermission({
    roles: req.user.roles as string[],
    requiredRoles: await allAllianceRoles(),
  });
}

async function isLocationPermitted(
  req: AuthenticatedRequest,
): Promise<boolean> {
  if (!_.isArray(req.user?.roles)) {
    throw new Error("Invalid user roles.");
  }
  const targetOrgs = req.query.location
    ? ["Alliance_" + (req.query.location as string)]
    : (await allAllianceRoles()).map((r) => r.split("-")[0]);
  return (req.user.roles as string[])
    .map((role) => getOrgFromRole(role))
    .some(
      (roleOrg) =>
        roleOrg === ALL_ORGS ||
        roleOrg === "Alliance_AllLocations" ||
        targetOrgs.includes(roleOrg),
    );
}

export async function getReqQueryIfPermitted<T extends z.ZodTypeAny>(
  req: AuthenticatedRequest,
  reqQuerySchema: T,
): Promise<{
  reqQuery?: z.infer<T>;
  error?: { statusCode: number; message?: string };
}> {
  if (!(await isAllianceUser(req))) {
    return { error: { statusCode: StatusCodes.FORBIDDEN } };
  }

  const {
    success,
    data: reqQuery,
    error,
  } = reqQuerySchema.safeParse(req.query) as z.SafeParseReturnType<unknown, T>;

  if (!success) {
    return {
      error: {
        statusCode: StatusCodes.BAD_REQUEST,
        message: error.message,
      },
    };
  }

  if (!(await isLocationPermitted(req))) {
    return { error: { statusCode: StatusCodes.FORBIDDEN } };
  }

  return { reqQuery };
}

export const getRmSuppliers = async (isBeef: boolean) => {
  const dataSource = await getSuppliers(ALL, isBeef);

  if (isNotEmpty(dataSource?.rows)) {
    // const allSupplierNumbersFromSnowflake = dataSource.rows
    //   ?.filter((i) => isNotEmpty(i.SUPPLIER_NO))
    //   .map((s) => s.SUPPLIER_NO);

    const rms = await Models.User.find({
      roles: { $eq: ALLIANCE_ROLE.ALL_LOCATIONS_RM },
    });
    const suppliersByRm = await Promise.all(
      rms.map(async (rm) => {
        let allSupplierNumbers: string[] = [];
        if (isBeef) {
          if (rm?.allianceBeefLSRs) {
            const allSuppliersFromItsLSRs: string[] = _.uniq(
              _.flattenDeep(
                (
                  await Models.User.find({
                    email: { $in: rm.allianceBeefLSRs },
                  })
                ).map((u) => u.allianceBeefSuppliers),
              ),
            );
            const allSupplierNumbersFromItsSuppliers: string[] = _.uniq(
              _.flattenDeep(
                (
                  await Models.User.find({
                    email: {
                      $in: allSuppliersFromItsLSRs.concat(
                        rm.allianceBeefSuppliers,
                      ),
                    },
                  })
                ).map((u) => u.allianceBeefSupplierNumbers),
              ),
            );
            // allSupplierNumbers = _.intersection(
            //   allSupplierNumbersFromItsSuppliers,
            //   allSupplierNumbersFromSnowflake,
            // );
            allSupplierNumbers = allSupplierNumbersFromItsSuppliers;
          }
        } else {
          if (rm?.allianceLambLSRs) {
            const allSuppliersFromItsLSRs: string[] = _.uniq(
              _.flattenDeep(
                (
                  await Models.User.find({
                    email: { $in: rm.allianceLambLSRs },
                  })
                ).map((u) => u.allianceLambSuppliers),
              ),
            );
            const allSupplierNumbersFromItsSuppliers: string[] = _.uniq(
              _.flattenDeep(
                (
                  await Models.User.find({
                    email: {
                      $in: allSuppliersFromItsLSRs.concat(
                        rm.allianceLambSuppliers,
                      ),
                    },
                  })
                ).map((u) => u.allianceLambSupplierNumbers),
              ),
            );
            // allSupplierNumbers = _.intersection(
            //   allSupplierNumbersFromItsSuppliers,
            //   allSupplierNumbersFromSnowflake,
            // );
            allSupplierNumbers = allSupplierNumbersFromItsSuppliers;
          }
        }
        return { rm, allSupplierNumbers };
      }),
    );

    return suppliersByRm;
  }
};

export const getLsrSuppliers = async (isBeef: boolean) => {
  const dataSource = await getSuppliers(ALL, isBeef);

  if (isNotEmpty(dataSource?.rows)) {
    const lsrs = await Models.User.find({
      roles: { $eq: ALLIANCE_ROLE.ALL_LOCATIONS_LSR },
    });
    const suppliersByLsr = await Promise.all(
      lsrs.map(async (lsr) => {
        let allSupplierNumbers: string[] = [];
        if (isBeef) {
          if (lsr?.allianceBeefSuppliers) {
            const allSupplierNumbersFromItsSuppliers: string[] = _.uniq(
              _.flattenDeep(
                (
                  await Models.User.find({
                    email: {
                      $in: lsr.allianceBeefSuppliers,
                    },
                  })
                ).map((u) => u.allianceBeefSupplierNumbers),
              ),
            );
            allSupplierNumbers = allSupplierNumbersFromItsSuppliers;
          }
        } else {
          if (lsr?.allianceLambSuppliers) {
            const allSupplierNumbersFromItsSuppliers: string[] = _.uniq(
              _.flattenDeep(
                (
                  await Models.User.find({
                    email: {
                      $in: lsr.allianceLambSuppliers,
                    },
                  })
                ).map((u) => u.allianceLambSupplierNumbers),
              ),
            );
            allSupplierNumbers = allSupplierNumbersFromItsSuppliers;
          }
        }
        return { lsr, allSupplierNumbers };
      }),
    );

    return suppliersByLsr;
  }
};

export const getSuppliers = async (location: string, isBeef: boolean) => {
  const dataSource: { rows: any[] | undefined } = { rows: [] };
  if (isClickHouseEnabled()) {
    const db = findDatabaseForPackage(PkgName.ALLIANCE_DATA_ANALYSIS);
    const resultSet = await chClient().query({
      query: `
        select
          location as LOCATION,
          supplier_no as SUPPLIER_NO
        from ${db}.alliance_${isBeef ? "beef" : "lamb"}_get_supplier_ids(
          start_date={start_date:Date32},
          end_date={end_date:Date32},
          location={location:String}
        )
      `,
      query_params: {
        start_date: "2020-01-01",
        end_date: moment().format(DateFormat.SHORT),
        location: getDbLocationFromOrgLocation(location),
      },
      format: "JSON",
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    });
    const { data } = await resultSet.json();
    dataSource.rows = data;
  } else {
    const db = findSnowflakeClientDb(zitadelOrgIdString);
    const result = await executeSnowflakeStatement({
      sqlText: `SELECT * FROM TABLE(${db}.MEQINSIGHTS.ALLIANCE_${isBeef ? "BEEF" : "LAMB"}_GET_SUPPLIER_IDS('2020-01-01', '${moment().format(DateFormat.SHORT)}', '${getDbLocationFromOrgLocation(location)}'));`,
      db,
    });
    dataSource.rows = result.rows;
  }
  return dataSource;
};
