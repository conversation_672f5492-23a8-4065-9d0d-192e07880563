import { Response } from "express";

import { AuthenticatedRequest } from "../Common";
import { LooseObject } from "../../utils/Types";
import { User } from "../../models/Interfaces";
import { querySuppliersForUser } from "./";
import { sanitiseParams } from "../../utils/Helper";

export async function getSuppliersForUser(
  req: AuthenticatedRequest,
  res: Response,
) {
  const user = req.user as User;
  const { location, isBeef } = sanitiseParams(req.body as LooseObject);
  const result = await querySuppliersForUser(
    location as string,
    user,
    isBeef as boolean,
  );
  res.status(200).json({ ...result, data: JSON.stringify(result.data) });
}
