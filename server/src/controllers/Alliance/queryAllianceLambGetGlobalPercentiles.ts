import { Response } from "express";
import { z } from "zod";

import { AuthenticatedRequest } from "../Common";
import { PkgName } from "../../utils/Types";
import {
  getReqBodyIfOrgLocationPermitted,
  orgLocationBodySchema,
} from "../shared";
import { findDb } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { getOptimisedColumns } from "../../utils/Helper";
import {
  chClient,
  findDatabaseForPackage,
  isClickHouseEnabled,
  removeUnusedColumnsInQueryResult,
} from "../../utils/ClickHouse";

const reqBodySchema = orgLocationBodySchema.extend({
  org: z
    .string()
    .toLowerCase()
    .regex(/^alliance$/),
});

export async function queryAllianceLambGetGlobalPercentiles(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfOrgLocationPermitted(
    req,
    PkgName.ALLIANCE_DATA_ANALYSIS,
    reqBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  if (isClickHouseEnabled()) {
    const db = findDatabaseForPackage(PkgName.ALLIANCE_DATA_ANALYSIS);

    const resultSet = await chClient().query({
      query: `
        select
          percentile as PERCENTILE,
          imf as IMF,
          lmy as LMY
        from ${db}.alliance_lamb_get_global_percentiles(
          location={location:String}
        )
      `,
      format: "JSON",
      query_params: {
        location: reqBody!.location,
      },
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    });

    const { meta, data } = removeUnusedColumnsInQueryResult(
      await resultSet.json(),
    );
    res.json({ rows: data, columns: meta, numRows: data.length });
  } else {
    const db = findDb();

    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: `
        set location = ?;
        select *
        from table(
          ${db}.MEQINSIGHTS.ALLIANCE_LAMB_GET_GLOBAL_PERCENTILES(
            $location
          )
        )
      `,
      binds: [reqBody!.location],
      parameters: {
        MULTI_STATEMENT_COUNT: 2,
      },
      db,
    });

    const optimised = getOptimisedColumns({ columns, rows });

    res.json({
      ...optimised,
      numRows: optimised.rows?.length || 0,
    });
  }
}
