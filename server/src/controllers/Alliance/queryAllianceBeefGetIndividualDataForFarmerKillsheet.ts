import { Response } from "express";
import { z } from "zod";

import { AuthenticatedRequest } from "../Common";
import { PkgName } from "../../utils/Types";
import {
  getReqBodyIfOrgLocationPermitted,
  orgLocationBodySchema,
} from "../shared";
import { findDb } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { getOptimisedColumns } from "../../utils/Helper";
import {
  chClient,
  findDatabaseForPackage,
  isClickHouseEnabled,
  removeUnusedColumnsInQueryResult,
} from "../../utils/ClickHouse";

const reqBodySchema = orgLocationBodySchema.extend({
  org: z
    .string()
    .toLowerCase()
    .regex(/^alliance$/),
  startDate: z.string().date(),
  endDate: z.string().date(),
  supplier: z.coerce.number().int(),
});

export async function queryAllianceBeefGetIndividualDataForFarmerKillsheet(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfOrgLocationPermitted(
    req,
    PkgName.ALLIANCE_DATA_ANALYSIS,
    reqBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  if (isClickHouseEnabled()) {
    const db = findDatabaseForPackage(PkgName.ALLIANCE_DATA_ANALYSIS);

    const resultSet = await chClient().query({
      query: `
        select
          unique_key as UNIQUE_KEY,
          supplier_no as SUPPLIER_NO,
          location as LOCATION,
          killsheet as KILLSHEET,
          tag_id as TAG_ID,
          kill_date as KILL_DATE,
          grade_name as GRADE_NAME,
          hotweight_kg as HOTWEIGHT_KG,
          marbling as MARBLING,
          f_colour as F_COLOUR,
          m_colour as M_COLOUR,
          eye_muscle_area as EYE_MUSCLE_AREA,
          ribfat as RIBFAT,
          ph_pof as PH_POF,
          az_path as AZ_PATH
        from ${db}.alliance_beef_get_individual_data_for_farmer_killsheet(
          start_date={start_date:String},
          end_date={end_date:String},
          location={location:String},
          supplier_no={supplier:Int32},
          killsheet='all'
        )
      `,
      format: "JSON",
      query_params: {
        start_date: reqBody!.startDate,
        end_date: reqBody!.endDate,
        location: reqBody!.location,
        supplier: reqBody!.supplier,
      },
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    });

    const { meta, data } = removeUnusedColumnsInQueryResult(
      await resultSet.json(),
    );
    res.json({ rows: data, columns: meta, numRows: data.length });
  } else {
    const db = findDb();

    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: `
        set start_date = ?;
        set end_date = ?;
        set location = ?;
        set supplier = ?::INTEGER;
        select *
        from table(
          ${db}.MEQINSIGHTS.ALLIANCE_BEEF_GET_INDIVIDUAL_DATA_FOR_FARMER_KILLSHEET(
            $start_date,
            $end_date,
            $location,
            $supplier,
            'All'
          )
        )
      `,
      binds: [
        reqBody!.startDate,
        reqBody!.endDate,
        reqBody!.location,
        reqBody!.supplier,
      ],
      parameters: {
        MULTI_STATEMENT_COUNT: 5,
      },
      db,
    });

    const optimised = getOptimisedColumns({ columns, rows });

    res.json({
      ...optimised,
      numRows: optimised.rows?.length || 0,
    });
  }
}
