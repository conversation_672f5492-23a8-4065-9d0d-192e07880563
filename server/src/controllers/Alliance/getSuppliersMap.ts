import { Response } from "express";
import { z } from "zod";

import * as Models from "../../models";
import { ALLIANCE_ROLE } from "../../utils/Types";
import { AuthenticatedRequest } from "../Common";
import { getReqQueryIfPermitted } from "./shared";

const reqQuerySchema = z.object({});

export const suppliersMap = async () => {
  const result = {
    lambRMs: [] as any[],
    beefRMs: [] as any[],
  };

  const rms = await Models.User.find({
    roles: { $eq: ALLIANCE_ROLE.ALL_LOCATIONS_RM },
  });

  for (const rm of rms) {
    const lambSupplierData = {
      displayName: rm.displayName,
      email: rm.email,
      supplierNumbers: rm.allianceLambSupplierNumbers || [],
      LSRs: [] as any[],
    };

    if (rm?.allianceLambLSRs?.length) {
      const lsrs = await Models.User.find({
        email: { $in: rm.allianceLambLSRs },
      });

      lambSupplierData.LSRs = (
        await Promise.all(
          lsrs.map(async (lsr) => {
            const lsrSuppliers = await Models.User.find({
              email: { $in: lsr.allianceLambSuppliers || [] },
            });

            return {
              displayName: lsr.displayName,
              email: lsr.email,
              supplierNumbers: lsr.allianceLambSupplierNumbers || [],
              suppliers: lsrSuppliers.map((supplier) => ({
                displayName: supplier.displayName,
                email: supplier.email,
                supplierNumbers: supplier.allianceLambSupplierNumbers || [],
              })),
            };
          }),
        )
      )
        .filter((lsr) => !lsr.email.startsWith("ben.shackleton"))
        .filter(
          (lsr) => lsr.supplierNumbers.length > 0 || lsr.suppliers.length > 0,
        );
    }

    if (
      lambSupplierData.supplierNumbers.length > 0 ||
      lambSupplierData.LSRs.length > 0
    ) {
      result.lambRMs.push(lambSupplierData);
    }

    const beefSupplierData = {
      displayName: rm.displayName,
      email: rm.email,
      supplierNumbers: rm.allianceBeefSupplierNumbers || [],
      LSRs: [] as any[],
    };

    if (rm?.allianceBeefLSRs?.length) {
      const lsrs = await Models.User.find({
        email: { $in: rm.allianceBeefLSRs },
      });

      beefSupplierData.LSRs = (
        await Promise.all(
          lsrs.map(async (lsr) => {
            const lsrSuppliers = await Models.User.find({
              email: { $in: lsr.allianceBeefSuppliers || [] },
            });

            return {
              displayName: lsr.displayName,
              email: lsr.email,
              supplierNumbers: lsr.allianceBeefSupplierNumbers || [],
              suppliers: lsrSuppliers.map((supplier) => ({
                displayName: supplier.displayName,
                email: supplier.email,
                supplierNumbers: supplier.allianceBeefSupplierNumbers || [],
              })),
            };
          }),
        )
      )
        .filter((lsr) => !lsr.email.startsWith("ben.shackleton"))
        .filter(
          (lsr) => lsr.supplierNumbers.length > 0 || lsr.suppliers.length > 0,
        );
    }

    if (
      beefSupplierData.supplierNumbers.length > 0 ||
      beefSupplierData.LSRs.length > 0
    ) {
      result.beefRMs.push(beefSupplierData);
    }
  }

  return result;
};

export const getSuppliersMap = async (
  req: AuthenticatedRequest,
  res: Response,
) => {
  const { error } = await getReqQueryIfPermitted(req, reqQuerySchema);

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  const result = await suppliersMap();

  res.json(result);
};
