import { Response } from "express";

import { AuthenticatedRequest } from "../Common";
import { LooseObject } from "../../utils/Types";
import { User } from "../../models/Interfaces";
import { queryLSRsForUser } from "./";
import { sanitiseParams } from "../../utils/Helper";

export async function getLSRsForUser(req: AuthenticatedRequest, res: Response) {
  const user = req.user as User;
  const { location, isBeef } = sanitiseParams(req.query as LooseObject);
  const result = await queryLSRsForUser(
    location as string,
    user,
    isBeef as boolean,
  );
  res.status(200).json(result.data);
}
