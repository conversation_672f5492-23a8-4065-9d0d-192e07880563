import _ from "lodash";
import moment from "moment";
import { z } from "zod";

import * as Models from "../../models";
import { ALL, ALL_ORGS, SUPER_ADMIN, SUPER_USER } from "../../utils/Constants";
import { AuthenticatedRequest } from "../Common";
import { DateFormat, ZitadelOrgIdString } from "../../utils/Types";
import { GMP_ROLE } from "../../utils/Gmp";
import { StatusCodes } from "http-status-codes";
import { User } from "../../models/Interfaces";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { findSnowflakeClientDb } from "../../utils/SnowflakeDatabases";
import { getOrgFromRole, hasPermission } from "../../utils/Helper";

export const zitadelOrgIdString = ZitadelOrgIdString.GMP;

const allGmpRoles = async () =>
  await Models.Role.find({ idString: /^GMP/ }).then((o) =>
    o.map((i) => i.idString),
  );

export function findDb(): string {
  const db = findSnowflakeClientDb(zitadelOrgIdString);
  if (db === undefined) {
    throw new Error(`Database not found for ${zitadelOrgIdString}`);
  }
  return db;
}

export async function isGmpUser(req: AuthenticatedRequest): Promise<boolean> {
  if (req.user?.roles == null) {
    return false;
  }

  return hasPermission({
    roles: req.user.roles as string[],
    requiredRoles: await allGmpRoles(),
  });
}

function isLocationPermitted(req: AuthenticatedRequest): boolean {
  if (!_.isArray(req.user?.roles)) {
    throw new Error("Invalid user roles.");
  }
  return (req.user.roles as string[])
    .map((role) => getOrgFromRole(role))
    .some((roleOrg) => roleOrg === ALL_ORGS || roleOrg === "GMP");
}

export async function getReqQueryIfPermitted<T extends z.ZodTypeAny>(
  req: AuthenticatedRequest,
  reqQuerySchema: T,
): Promise<
  | {
      reqQuery: z.infer<T>;
      error?: never;
    }
  | {
      reqQuery?: never;
      error: { statusCode: number; message?: string };
    }
> {
  if (!(await isGmpUser(req))) {
    return { error: { statusCode: StatusCodes.FORBIDDEN } };
  }

  const {
    success,
    data: reqQuery,
    error,
  } = reqQuerySchema.safeParse(req.query) as z.SafeParseReturnType<unknown, T>;

  if (!success) {
    return {
      error: {
        statusCode: StatusCodes.BAD_REQUEST,
        message: error.message,
      },
    };
  }

  if (!isLocationPermitted(req)) {
    return { error: { statusCode: StatusCodes.FORBIDDEN } };
  }

  return { reqQuery };
}

export const getAllPics = async () => {
  const allPics = await Models.GmpPic.find({})
    .sort({ pic: 1 })
    .then((o) => o);
  return allPics;
};

type PIC = {
  LOT_REFERENCE: string;
  PIC: string;
};

export const picsForUser = async (user: User) => {
  const db = findDb();
  const allPicsWithNames = await getAllPics();

  const dataSource = await executeSnowflakeStatement({
    sqlText: `SELECT DISTINCT PIC, LOT_REFERENCE
              FROM 
              TABLE(${db}.MEQINSIGHTS.GMP_LIST_PICS('2020-01-01', '${moment().format(DateFormat.SHORT)}')) `,
    db,
  });
  const roles = user.roles;
  const allPicsFromSnowflake = (dataSource.rows ?? []) as PIC[];
  let allPics;
  if (
    _.intersection(roles, [SUPER_ADMIN, SUPER_USER, GMP_ROLE.ADMIN]).length > 0
  ) {
    allPics = allPicsFromSnowflake;
  } else {
    allPics = allPicsFromSnowflake?.filter((i) =>
      user.gmpPics?.includes(i.PIC),
    );
    if (roles.includes(GMP_ROLE.AGENT)) {
      allPics = allPicsFromSnowflake?.filter(
        (i) =>
          user.gmpPics?.includes(i.PIC) &&
          (user.gmpPicsWithLots
            ?.find((o) => o.pic === i.PIC)
            ?.lots.includes(ALL) ||
            user.gmpPicsWithLots
              ?.find((o) => o.pic === i.PIC)
              ?.lots.includes(i.LOT_REFERENCE)),
      );
    }
  }
  allPics = _.uniqWith(
    allPics?.map((i) => {
      const pic = allPicsWithNames.find((o) => o.pic === i.PIC);
      return {
        PIC: i.PIC,
        businessName: pic?.businessName ?? "Other",
      };
    }),
    _.isEqual,
  );
  return allPics;
};
