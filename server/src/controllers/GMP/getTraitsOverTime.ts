import { Response } from "express";
import { z } from "zod";

import { User } from "../../models/Interfaces";
import { AuthenticatedRequest } from "../Common";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { findDb, getReqQueryIfPermitted, picsForUser } from "./shared";

const reqQuerySchema = z.object({});

const WHERES_MARKER = "###WHERES###";

const sfDb = findDb();

type SupplierFilter = "NONE" | "STANDARD";

interface QueryContext {
  sql: string;
  lets?: string;
  wheres?: string;
  queryParams?: Record<string, unknown>;
  processPicsParam: (pics: string[], context: QueryContext) => void;
  executeQuery: (context: QueryContext) => Promise<QueryResult>;
}

interface QueryResult {
  rows: unknown;
  columns: unknown;
}

const createSfContext = (options: { snowflakeSql: string }) => ({
  sql: options.snowflakeSql,
  lets: "",
  wheres: "",
  processPicsParam: (pics: string[], context: QueryContext) => {
    context.lets += `LET pics := array_construct('${pics.join("','")}');`;
    context.wheres += " and ARRAY_CONTAINS(PIC::variant,:pics)";
  },
  executeQuery: async (context: QueryContext) => {
    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: `
              DECLARE
                res RESULTSET;
              BEGIN
                ${context.lets}
              res := (
                ${context.sql.replace(WHERES_MARKER, context.wheres!)}
              );
              RETURN TABLE(res);
              END
            `,
      db: sfDb,
    });
    return { rows, columns };
  },
});

const getTraitsOverTimeForQuery = (options: {
  shouldFilterSuppliers: SupplierFilter;
  snowflakeSql: string;
}) => {
  return getTraitsOverTimeForQueryHandler({
    shouldFilterSuppliers: options.shouldFilterSuppliers,
    createContext: () =>
      createSfContext({
        snowflakeSql: options.snowflakeSql,
      }),
  });
};

const getTraitsOverTimeForQueryHandler =
  (options: {
    shouldFilterSuppliers: SupplierFilter;
    createContext: () => QueryContext;
  }) =>
  async (req: AuthenticatedRequest, res: Response) => {
    const { error } = await getReqQueryIfPermitted(req, reqQuerySchema);

    if (error) {
      res.status(error.statusCode).send(error.message);
      return;
    }

    const context = options.createContext();

    if (options.shouldFilterSuppliers === "STANDARD") {
      const userPics = await picsForUser(req.user as User);
      const pics = userPics.map((pic) => pic.PIC);
      context.processPicsParam(pics, context);
    }

    const { rows, columns } = await context.executeQuery(context);

    res.json({
      rows,
      columns,
    });
  };

const sfFieldGroupAvg = (
  sourceFieldSuffix: string,
  resultFieldSuffix: string,
) =>
  `sum(iff(${sourceFieldSuffix} > 0,${sourceFieldSuffix}, null)) as GROUP_SUM_${resultFieldSuffix},
  sum(iff(${sourceFieldSuffix} > 0, 1, null)) as GROUP_COUNT_${resultFieldSuffix},
  GROUP_SUM_${resultFieldSuffix} / GROUP_COUNT_${resultFieldSuffix} as AVG_${resultFieldSuffix},
  avg(AVG_${resultFieldSuffix}) over (order by KILL_DATE rows between 29 preceding and current row) as ROLLING_AVG_${resultFieldSuffix}`;

export const getTraitsOverTimeLamb = getTraitsOverTimeForQuery({
  snowflakeSql: `
    select
      to_varchar(PIC) as PIC,
      KILL_DATE,
      ${sfFieldGroupAvg("GLQ", "GLQ")},
      ${sfFieldGroupAvg("HSCW", "HOTWEIGHT_KG")},
      ${sfFieldGroupAvg("LMY", "LMY_PERCENTAGE_OF")},
      ${sfFieldGroupAvg("IMF", "IMF_PERCENTAGE_OF")},
      LISTAGG(DISTINCT LOT_REFERENCE, ', ')
        WITHIN GROUP (ORDER BY LOT_REFERENCE)
        as LOTS
    from
      ${sfDb}.MEQINSIGHTS.GMP_INSIGHTS_CARCASE
    where
      1=1
      ${WHERES_MARKER}
    group by KILL_DATE, PIC
    order by KILL_DATE
  `,
  shouldFilterSuppliers: "STANDARD",
  // shouldFilterSuppliers: "NONE",
});

export const getTraitsOverTimeLambAllSuppliers = getTraitsOverTimeForQuery({
  snowflakeSql: `
      select
        KILL_DATE,
        ${sfFieldGroupAvg("GLQ", "GLQ")},
        ${sfFieldGroupAvg("HSCW", "HOTWEIGHT_KG")},
        ${sfFieldGroupAvg("LMY", "LMY_PERCENTAGE_OF")},
        ${sfFieldGroupAvg("IMF", "IMF_PERCENTAGE_OF")}
      from
        ${sfDb}.MEQINSIGHTS.GMP_INSIGHTS_CARCASE
      where
        1=1
        ${WHERES_MARKER}
      group by KILL_DATE
      order by KILL_DATE
    `,
  shouldFilterSuppliers: "NONE",
});
