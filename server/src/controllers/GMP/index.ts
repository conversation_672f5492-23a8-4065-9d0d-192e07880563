import type { Request<PERSON><PERSON><PERSON>, Response } from "express";
import type { AuthenticatedRequest } from "../Common";
import { z } from "zod";

import {
  DateFormat,
  GmpBookingStatus,
  Message,
  ResultCode,
  ServerResponse,
  SystemEmail,
} from "../../utils/Types";
import * as Models from "../../models";
import {
  hasPermission,
  isEvenNumber,
  isNotEmpty,
  isValidGmpGridPricingData,
  logExceptionToDB,
  sanitiseParams,
  sendMail,
} from "../../utils/Helper";
import { GmpSupplyAgreement, User } from "../../models/Interfaces";
import { getAllUsersFromDB } from "../Management/Users";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import moment from "moment";
import { findSnowflakeClientDb } from "../../utils/SnowflakeDatabases";
import {
  ALL,
  GMP_ADMIN_EMAIL,
  SUPER_ADMIN,
  SUPER_USER,
} from "../../utils/Constants";
import _ from "lodash";
import fs from "fs/promises";
import { SNOWFLAKE_ROLE } from "../../utils/EnvConfig";
import { GMP_ROLE } from "../../utils/Gmp";
import { getAllPics, picsForUser, getReqQueryIfPermitted } from "./shared";

export * from "./getTraitsOverTime";

const DateFormatBookings = "ddd MMM D, [Week] w, YYYY";

type YearWeek = { year: number; week: number };

const allGMPRoles = async () =>
  await Models.Role.find({ idString: /^GMP-/ }).then((o) =>
    o.map((i) => i.idString),
  );

export async function getPicsForUser(req: AuthenticatedRequest, res: Response) {
  const { error } = await getReqQueryIfPermitted(req, z.object({}));

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  const user = req.user as User;
  const allPics = await picsForUser(user);
  if (_.isEmpty(allPics)) {
    res.status(ResultCode.FAILED).send(Message.ERROR_DATA_SOURCE_NOT_AVAILABLE);
    return;
  }
  res.status(200).json(allPics);
}

export async function loadPics(req: AuthenticatedRequest, res: Response) {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const roles = user.roles;

  if (roles) {
    if (hasPermission({ roles, requiredRoles: await allGMPRoles() })) {
      const allPics = await getAllPics();

      result.data = JSON.stringify(allPics);
      result.msg = Message.SUCCESS;
      result.code = ResultCode.SUCCESS;
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PERMISSION_DENIED;
  }

  res.status(200).json(result);
}

export const addPic: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { pic, businessName } = sanitiseParams(req.body);

  if (pic && businessName) {
    const roles = user.roles;

    if (roles) {
      if (hasPermission({ roles, requiredRoles: await allGMPRoles() })) {
        const existings = await Models.GmpPic.find({ pic }).then((o) => o);

        if (existings.length > 0) {
          result.msg = Message.ERROR_PIC_EXISTS;
        } else {
          await new Models.GmpPic({ pic, businessName }).save().then();

          const allPics = await getAllPics();

          result.data = JSON.stringify(allPics);
          result.msg = Message.SUCCESS;
          result.code = ResultCode.SUCCESS;
        }
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const updatePic: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { id, pic, businessName } = sanitiseParams(req.body);

  if (id && pic && businessName) {
    const roles = user.roles;

    if (roles) {
      if (hasPermission({ roles, requiredRoles: await allGMPRoles() })) {
        const existing = await Models.GmpPic.findById(id).then((o) => o);

        if (existing) {
          await Models.GmpPic.findByIdAndUpdate(id, {
            pic,
            businessName,
          }).then();

          const allPics = await getAllPics();

          result.data = JSON.stringify(allPics);
          result.msg = Message.SUCCESS;
          result.code = ResultCode.SUCCESS;
        } else {
          result.msg = Message.ERROR_ITEM_NOT_FOUND;
        }
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const deletePic: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { id } = sanitiseParams(req.body);

  if (id) {
    const roles = user.roles;

    if (roles) {
      if (hasPermission({ roles, requiredRoles: await allGMPRoles() })) {
        const existing = await Models.GmpPic.findById(id).then((o) => o);

        if (existing) {
          await Models.GmpPic.findByIdAndDelete(id).then();

          const allPics = await getAllPics();

          result.data = JSON.stringify(allPics);
          result.msg = Message.SUCCESS;
          result.code = ResultCode.SUCCESS;
        } else {
          result.msg = Message.ERROR_ITEM_NOT_FOUND;
        }
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const assignPics: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { id, gmpPics, gmpPicsWithLots } = sanitiseParams(req.body);

  if (id && gmpPics) {
    const roles = user.roles;

    if (roles) {
      if (hasPermission({ roles, requiredRoles: await allGMPRoles() })) {
        const existing = await Models.User.findOne({ zitadelId: id }).then(
          (o) => o,
        );

        if (existing) {
          await Models.User.findOneAndUpdate(
            { zitadelId: id },
            { gmpPics, gmpPicsWithLots },
          )
            .then()
            .catch((err) => logExceptionToDB(err));

          const allGmpUsers = await getAllUsersFromDB(
            "GMP",
            roles.includes(SUPER_ADMIN),
          );

          result.data = JSON.stringify(allGmpUsers);
          result.msg = Message.SUCCESS;
          result.code = ResultCode.SUCCESS;
        } else {
          result.msg = Message.ERROR_USER_NOT_FOUND;
        }
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export async function postPicsForUser(
  req: AuthenticatedRequest,
  res: Response,
) {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const roles = user.roles;

  if (roles) {
    if (hasPermission({ roles, requiredRoles: await allGMPRoles() })) {
      const db = findSnowflakeClientDb("GMP");
      const dataSource = await executeSnowflakeStatement({
        sqlText: `SELECT d.KILL_DATE, d.PIC, d.LOT_REFERENCE, t.NUM_GL_LAMBS
                  FROM (
                    SELECT DISTINCT KILL_DATE, PIC, LOT_REFERENCE
                    FROM
                    TABLE(${db}.MEQINSIGHTS.GMP_LIST_PICS('2020-01-01', '${moment().format(DateFormat.SHORT)}'))
                  ) d
                  JOIN
                  TABLE(${db}.MEQINSIGHTS.GMP_LIST_PICS('2020-01-01', '${moment().format(DateFormat.SHORT)}')) t
                  ON
                  d.KILL_DATE = t.KILL_DATE AND d.PIC = t.PIC AND d.LOT_REFERENCE = t.LOT_REFERENCE
                  ORDER BY d.KILL_DATE DESC;`,
        // sqlText: `SELECT DISTINCT KILL_DATE, PIC, LOT_REFERENCE FROM TABLE(GMP_LIST_PICS('2020-01-01', '${moment().format(DateFormat.SHORT)}')) ORDER BY KILL_DATE DESC;`,
        db,
      });

      if (isNotEmpty(dataSource?.rows)) {
        const allPicsFromSnowflake = dataSource.rows;

        let allPics: any[] | undefined = [];
        if (
          _.intersection(roles, [SUPER_ADMIN, SUPER_USER, GMP_ROLE.ADMIN])
            .length > 0
        ) {
          allPics = allPicsFromSnowflake;
        } else {
          allPics = allPicsFromSnowflake?.filter((i) =>
            user.gmpPics?.includes(i.PIC),
          );
          if (roles.includes(GMP_ROLE.AGENT)) {
            allPics = allPicsFromSnowflake?.filter(
              (i) =>
                user.gmpPics?.includes(i.PIC) &&
                (user.gmpPicsWithLots
                  ?.find((o) => o.pic === i.PIC)
                  ?.lots.includes(ALL) ||
                  user.gmpPicsWithLots
                    ?.find((o) => o.pic === i.PIC)
                    ?.lots.includes(i.LOT_REFERENCE)),
            );
          }
        }

        result.data = JSON.stringify(allPics);
        result.msg = Message.SUCCESS;
        result.code = ResultCode.SUCCESS;
      } else {
        result.msg = Message.ERROR_DATA_SOURCE_NOT_AVAILABLE;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PERMISSION_DENIED;
  }

  res.status(200).json(result);
}

export const getSettings: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const roles = user.roles;

  if (roles) {
    if (hasPermission({ roles, requiredRoles: [GMP_ROLE.ADMIN] })) {
      const existing = await Models.GmpSettings.find({}).then((o) => o);

      if (existing.length > 0) {
        result.data = JSON.stringify(existing[0]);
        result.msg = Message.SUCCESS;
        result.code = ResultCode.SUCCESS;
      } else {
        result.msg = Message.ERROR_ITEM_NOT_FOUND;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PERMISSION_DENIED;
  }

  res.status(200).json(result);
};

export const updateSettings: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { weeklyLimit } = sanitiseParams(req.body);

  if (weeklyLimit && _.isInteger(weeklyLimit)) {
    const roles = user.roles;

    if (roles) {
      if (hasPermission({ roles, requiredRoles: [GMP_ROLE.ADMIN] })) {
        const existing = await Models.GmpSettings.find({}).then((o) => o);
        let settings = undefined;
        if (existing.length > 0) {
          settings = await Models.GmpSettings.findByIdAndUpdate(
            existing[0]._id,
            { weeklyLimit },
            { new: true },
          ).then((o) => o);
        } else {
          const newItem = new Models.GmpSettings({ weeklyLimit });
          settings = await newItem.save().then((o) => o);
        }
        if (settings) {
          result.data = JSON.stringify(settings);
          result.msg = Message.SUCCESS;
          result.code = ResultCode.SUCCESS;
        }
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

const getAllWeeklySettings = async (yearWeek?: YearWeek) =>
  await Models.GmpWeeklySettings.find(yearWeek || {})
    .sort({ year: -1, week: -1 })
    .then((o) => o);
export const getWeeklySettings: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const roles = user.roles;

  const { year, week } = sanitiseParams(req.body);

  if (roles) {
    if (hasPermission({ roles, requiredRoles: [GMP_ROLE.ADMIN] })) {
      const all = await getAllWeeklySettings(
        year && week ? { year, week } : undefined,
      );

      result.data = year & week ? JSON.stringify(all[0]) : JSON.stringify(all);
      result.msg = Message.SUCCESS;
      result.code = ResultCode.SUCCESS;
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PERMISSION_DENIED;
  }

  res.status(200).json(result);
};

export const updateWeeklySettings: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };
  const { year, week, weeklyLimit } = sanitiseParams(req.body);

  if (year && week && weeklyLimit && _.isInteger(weeklyLimit)) {
    const roles = user.roles;

    if (roles) {
      if (hasPermission({ roles, requiredRoles: [GMP_ROLE.ADMIN] })) {
        const existing = await getAllWeeklySettings({ year, week });
        let settings = undefined;
        if (existing.length > 0) {
          settings = await Models.GmpWeeklySettings.findByIdAndUpdate(
            existing[0]._id,
            { year, week, weeklyLimit },
            { new: true },
          ).then((o) => o);
        } else {
          const newItem = new Models.GmpWeeklySettings({
            year,
            week,
            weeklyLimit,
          });
          settings = await newItem.save().then((o) => o);
        }
        if (settings) {
          result.data = JSON.stringify(settings);
          result.msg = Message.SUCCESS;
          result.code = ResultCode.SUCCESS;
        }
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const getGrids = async (req: AuthenticatedRequest, res: Response) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const roles = user.roles;

  if (roles) {
    if (hasPermission({ roles, requiredRoles: await allGMPRoles() })) {
      const allGrids = await Models.GmpGrid.find({ status: "ACTIVE" });

      result.data = JSON.stringify(allGrids);
      result.msg = Message.SUCCESS;
      result.code = ResultCode.SUCCESS;
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PERMISSION_DENIED;
  }

  res.status(200).json(result);
};

export const addGrid = async (req: AuthenticatedRequest, res: Response) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { grid } = sanitiseParams(req.body);

  if (grid) {
    const roles = user.roles;

    if (roles) {
      if (hasPermission({ roles, requiredRoles: [GMP_ROLE.ADMIN] })) {
        await new Models.GmpGrid({
          ...grid,
          status: Models.GmpGridStatus.ACTIVE,
        })
          .save()
          .then();

        result.data = JSON.stringify(grid);
        result.msg = Message.SUCCESS;
        result.code = ResultCode.SUCCESS;
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const deleteGrid: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { grid } = sanitiseParams(req.body);
  const updateGrid = { ...grid, status: "DELETED" };

  if (grid) {
    const roles = user.roles;

    if (roles) {
      if (hasPermission({ roles, requiredRoles: [GMP_ROLE.ADMIN] })) {
        await Models.GmpGrid.findByIdAndUpdate(grid._id, {
          status: updateGrid.status,
        }).then();

        result.data = JSON.stringify(updateGrid);
        result.msg = Message.SUCCESS;
        result.code = ResultCode.SUCCESS;
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const updateGrid = async (req: AuthenticatedRequest, res: Response) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { grid } = sanitiseParams(req.body);

  if (grid) {
    const roles = user.roles;

    if (roles) {
      if (hasPermission({ roles, requiredRoles: [GMP_ROLE.ADMIN] })) {
        await Models.GmpGrid.findByIdAndUpdate(grid._id, grid).then();

        result.data = JSON.stringify(grid);
        result.msg = Message.SUCCESS;
        result.code = ResultCode.SUCCESS;
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

const getAllGridPricings = async (yearWeek?: YearWeek, gridId?: string) =>
  await Models.GmpGridPricing.find({ ...(yearWeek || {}), gridId })
    .sort({ year: -1, week: -1 })
    .then((o) => o);

export const getGridPricings: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { gridId, year, week } = sanitiseParams(req.body);

  const roles = user.roles;

  if (roles) {
    if (hasPermission({ roles, requiredRoles: await allGMPRoles() })) {
      const allGridPricing = await getAllGridPricings(
        year && week
          ? { year, week: isEvenNumber(week) ? week - 1 : week }
          : undefined,
        gridId,
      ); // the db only keep odd weeks

      result.data = JSON.stringify(allGridPricing);
      result.msg = Message.SUCCESS;
      result.code = ResultCode.SUCCESS;
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PERMISSION_DENIED;
  }

  res.status(200).json(result);
};

export const addGridPricing: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { gridPricingData } = sanitiseParams(req.body);

  if (gridPricingData && isValidGmpGridPricingData(gridPricingData)) {
    const optimisedGridPricingData = { ...gridPricingData };
    optimisedGridPricingData.week = isEvenNumber(optimisedGridPricingData.week)
      ? optimisedGridPricingData.week - 1
      : optimisedGridPricingData.week; // only save odd weeks

    const roles = user.roles;

    if (roles) {
      if (hasPermission({ roles, requiredRoles: [GMP_ROLE.ADMIN] })) {
        const existings = await Models.GmpGridPricing.find({
          gridId: optimisedGridPricingData.gridId,
          year: optimisedGridPricingData.year,
          week: optimisedGridPricingData.week,
        }).then((o) => o);

        if (existings.length > 0) {
          result.msg = Message.ERROR_GRID_PRICING_EXISTS;
        } else {
          await new Models.GmpGridPricing(optimisedGridPricingData)
            .save()
            .then();

          const allGridPricing = await getAllGridPricings();

          result.data = JSON.stringify(allGridPricing);
          result.msg = Message.SUCCESS;
          result.code = ResultCode.SUCCESS;
        }
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const updateGridPricing: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { gridPricingData } = sanitiseParams(req.body);

  if (gridPricingData && isValidGmpGridPricingData(gridPricingData)) {
    const optimisedGridPricingData = { ...gridPricingData };
    optimisedGridPricingData.week = isEvenNumber(optimisedGridPricingData.week)
      ? optimisedGridPricingData.week - 1
      : optimisedGridPricingData.week; // only save odd weeks

    const roles = user.roles;

    if (roles) {
      if (hasPermission({ roles, requiredRoles: [GMP_ROLE.ADMIN] })) {
        let isConflictWithExistings = false; // check conflict if year or week changed
        const existings = await Models.GmpGridPricing.find({
          gridId: optimisedGridPricingData.gridId,
          year: optimisedGridPricingData.year,
          week: optimisedGridPricingData.week,
        }).then((o) => o);
        if (existings.length > 0) {
          if (existings[0]._id.toString() !== optimisedGridPricingData._id) {
            isConflictWithExistings = true;
          }
        }

        if (!isConflictWithExistings) {
          await Models.GmpGridPricing.findByIdAndUpdate(
            optimisedGridPricingData._id,
            optimisedGridPricingData,
          ).then((o) => o);
          const allGridPricing = await getAllGridPricings();

          result.data = JSON.stringify(allGridPricing);
          result.msg = Message.SUCCESS;
          result.code = ResultCode.SUCCESS;
        } else {
          result.msg = Message.ERROR_GRID_PRICING_EXISTS;
        }
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const getAllBusinessNames: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const roles = user.roles;

  if (roles) {
    if (hasPermission({ roles, requiredRoles: [GMP_ROLE.ADMIN] })) {
      const allBusinessNamesFromGmpPics: string[] = await Models.GmpPic.find(
        {},
      ).then((o) => o.map((i) => i.businessName));
      const allBusinessNamesFromUsers: string[] = await Models.User.find({
        gmpBusinessName: { $exists: true },
      }).then((o) => o.map((i) => i.gmpBusinessName));

      const allBusinessNames: string[] = _.uniq(
        allBusinessNamesFromGmpPics.concat(allBusinessNamesFromUsers),
      );

      result.data = JSON.stringify(allBusinessNames);
      result.msg = Message.SUCCESS;
      result.code = ResultCode.SUCCESS;
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PERMISSION_DENIED;
  }

  res.status(200).json(result);
};

const getAllGmpBookings = async (yearWeek?: YearWeek) =>
  await Models.GmpBooking.find(
    yearWeek ? { ...yearWeek, isValid: true } : { isValid: true },
  )
    .sort({ year: -1, week: -1 })
    .then((o) => o);

export const getBookings = async (req: AuthenticatedRequest, res: Response) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const roles = user.roles;

  const { year, week } = sanitiseParams(req.body);

  if (roles) {
    if (hasPermission({ roles, requiredRoles: [GMP_ROLE.ADMIN] })) {
      const all = await getAllGmpBookings(
        year && week ? { year, week } : undefined,
      );

      result.data = JSON.stringify(all);
      result.msg = Message.SUCCESS;
      result.code = ResultCode.SUCCESS;
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PERMISSION_DENIED;
  }

  res.status(200).json(result);
};

export const addBooking = async (req: AuthenticatedRequest, res: Response) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  let { bookingDate, gridId, quantity, vendorEmail, status } = sanitiseParams(
    req.body,
  );

  const bookingMoment = moment(bookingDate, DateFormat.SHORT);
  const week = bookingMoment.isoWeek();
  const year = bookingMoment.year();

  if (bookingDate && quantity && vendorEmail && status) {
    const roles = user.roles;

    if (roles) {
      vendorEmail = vendorEmail.toLowerCase();
      const vendor = await Models.User.findOne({
        email: vendorEmail,
        isValid: true,
      }).then((o) => o);
      if (vendor) {
        if (hasPermission({ roles, requiredRoles: [GMP_ROLE.ADMIN] })) {
          const existings = await Models.GmpBooking.find({
            year,
            week,
            vendorEmail,
            bookingDate,
            isValid: true,
          }).then((o) => o);

          if (existings.length > 0) {
            result.msg = Message.ERROR_BOOKING_EXISTS;
          } else {
            await new Models.GmpBooking({
              year,
              week,
              bookingDate: bookingMoment.format(DateFormat.SHORT),
              quantity,
              isAgent: vendor.roles?.includes(GMP_ROLE.AGENT),
              vendorEmail: vendor.email,
              vendorFirstName: vendor.firstName,
              vendorLastName: vendor.lastName,
              vendorBusinessName: vendor.gmpBusinessName,
              status,
              bookedByEmail: user.email,
              bookedByFirstName: user.firstName,
              bookedByLastName: user.lastName,
              gridId,
              isValid: true,
            })
              .save()
              .then();

            const all = await getAllGmpBookings();

            result.data = JSON.stringify(all);
            result.msg = Message.SUCCESS;
            result.code = ResultCode.SUCCESS;
          }
        } else {
          result.msg = Message.ERROR_PERMISSION_DENIED;
        }
      } else {
        result.msg = Message.ERROR_VENDOR_NOT_FOUND;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const updateBooking: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { id, bookingDate, gridId, quantity, status } = sanitiseParams(
    req.body,
  );

  const deliveryMoment = moment(bookingDate, DateFormat.SHORT);
  const week = deliveryMoment.isoWeek();
  const year = deliveryMoment.year();

  if (id && bookingDate && quantity && status) {
    const roles = user.roles;

    if (roles) {
      if (hasPermission({ roles, requiredRoles: [GMP_ROLE.ADMIN] })) {
        const updated = await Models.GmpBooking.findByIdAndUpdate(
          id,
          { year, week, bookingDate, gridId, quantity, status },
          { new: true },
        ).then((o) => o);
        if (updated) {
          const all = await getAllGmpBookings();

          result.data = JSON.stringify(all);
          result.msg = Message.SUCCESS;
          result.code = ResultCode.SUCCESS;
        } else {
          result.msg = Message.ERROR_ITEM_NOT_FOUND;
        }
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const deleteBooking: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { id } = sanitiseParams(req.body);

  if (id) {
    const roles = user.roles;

    if (roles) {
      if (hasPermission({ roles, requiredRoles: [GMP_ROLE.ADMIN] })) {
        const updated = await Models.GmpBooking.findByIdAndUpdate(
          id,
          { isValid: false },
          { new: true },
        ).then((o) => o);
        if (updated) {
          const all = await getAllGmpBookings();

          result.data = JSON.stringify(all);
          result.msg = Message.SUCCESS;
          result.code = ResultCode.SUCCESS;
        } else {
          result.msg = Message.ERROR_ITEM_NOT_FOUND;
        }
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

const getAllGmpSupplyAgreements = async ({
  year,
  week,
}: {
  year?: number;
  week?: number;
}) =>
  await Models.GmpSupplyAgreement.find(
    year && week ? { year, week, isValid: true } : { isValid: true },
  )
    .sort({ year: -1, week: -1 })
    .then((o) => o);

export const getSupplyAgreements: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const roles = user.roles;

  const { year, week } = sanitiseParams(req.body);

  if (roles) {
    if (hasPermission({ roles, requiredRoles: [GMP_ROLE.ADMIN] })) {
      const allSupplyAgreements = await getAllGmpSupplyAgreements({
        year,
        week,
      });

      result.data = JSON.stringify(allSupplyAgreements);
      result.msg = Message.SUCCESS;
      result.code = ResultCode.SUCCESS;
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PERMISSION_DENIED;
  }

  res.status(200).json(result);
};

export const sendSupplyAgreements = async (
  req: AuthenticatedRequest,
  res: Response,
) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  let { bookingsToUpdate } = sanitiseParams(req.body);

  if (
    bookingsToUpdate &&
    Array.isArray(bookingsToUpdate) &&
    bookingsToUpdate.length > 0
  ) {
    const roles = user.roles;

    if (roles) {
      if (hasPermission({ roles, requiredRoles: [GMP_ROLE.ADMIN] })) {
        bookingsToUpdate = bookingsToUpdate.filter(
          (i) =>
            i.status !== GmpBookingStatus.Assigned &&
            i.status !== GmpBookingStatus.Completed,
        );

        let isSuccess = true;
        for (let i = 0; i < bookingsToUpdate.length; i++) {
          const booking = bookingsToUpdate[i];
          if (booking.bookingDate) {
            try {
              const emailTemplate = await fs.readFile(
                "src/email_templates/send-supply-agreements.html",
                { encoding: "utf8" },
              );
              const html = emailTemplate
                .replaceAll("[INSERT_NAME]", booking.vendorFirstName)
                .replaceAll(
                  "[INSERT_LINK]",
                  `https://www.meqinsights.com#complete-supply-agreement-${booking._id}`,
                )
                .replaceAll(
                  "[INSERT_QUANTITY_AND_DATE]",
                  `${booking.quantity} Head, delivery ${moment(booking.bookingDate, DateFormat.SHORT).subtract(1, "days").format(DateFormatBookings)} for processing the following day`,
                )
                .replaceAll("[INSERT_YEAR]", moment().year().toString());
              const emailToSent: SystemEmail = {
                to: booking.vendorEmail,
                subject: "Gundagai Lamb Booking",
                html,
              };
              const sentMessageInfo = await sendMail(emailToSent);
              if (sentMessageInfo.isSuccess) {
                const updatedBooking =
                  await Models.GmpBooking.findByIdAndUpdate(
                    booking._id,
                    {
                      bookingDate: booking.bookingDate,
                      status: GmpBookingStatus.Assigned,
                    },
                    { new: true },
                  );
                if (!updatedBooking) {
                  isSuccess = false;
                }
              } else {
                isSuccess = false;
                result.msg = Message.ERROR_SEND_EMAIL;
              }
            } catch (e) {
              isSuccess = false;
              result.msg = Message.ERROR_SEND_EMAIL;
              logExceptionToDB(e);
            }
          }
        }

        if (isSuccess) {
          const all = await getAllGmpBookings();

          result.data = JSON.stringify(all);
          result.msg = Message.SUCCESS;
          result.code = ResultCode.SUCCESS;
        } else {
          result.msg = Message.ERROR_BOOKING_NOT_FOUND;
        }
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const completeSupplyAgreements: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { bookingId, supplyAgreements } = sanitiseParams(req.body);

  // To Do: also need to check if the PICs in supplyAgreements is valid against NLIS
  if (
    bookingId &&
    supplyAgreements &&
    Array.isArray(supplyAgreements) &&
    supplyAgreements.length > 0
  ) {
    const roles = user.roles;

    if (roles) {
      if (
        hasPermission({
          roles,
          requiredRoles: [GMP_ROLE.ADMIN, GMP_ROLE.AGENT, GMP_ROLE.PRODUCER],
        })
      ) {
        const booking = await Models.GmpBooking.findById(bookingId).then(
          (o) => o,
        );
        if (
          booking &&
          booking.isValid &&
          (booking.vendorEmail === user.email ||
            hasPermission({ roles, requiredRoles: [GMP_ROLE.ADMIN] }))
        ) {
          const optimisedSupplyAgreements = supplyAgreements.map((i) => {
            const sanitised = sanitiseParams(i);
            return {
              ...sanitised,
              bookingId,
              pic: sanitised.pic.toUpperCase(),
              isMuelsed:
                !!sanitised.isMuelsed && sanitised.isMuelsed !== "false",
              year: booking.year,
              week: booking.week,
              agreedDeliveryDate: booking.bookingDate,
              isAgent: booking.isAgent,
              producerFirstName: i.producerFirstName || booking.vendorFirstName,
              producerLastName: i.producerLastName || booking.vendorLastName,
              producerEmail: i.producerEmail || booking.vendorEmail,
              producerBusinessName:
                i.producerBusinessName || booking.vendorBusinessName,
              isValid: true,
            } as GmpSupplyAgreement;
          });

          let isSuccess = true;
          const existingSupplyAgreementsForThisBooking =
            await Models.GmpSupplyAgreement.find({
              bookingId,
              isValid: true,
            }).then((o) => o);
          const idsOfExistingSupplyAgreementsForThisBooking =
            existingSupplyAgreementsForThisBooking.map((i) => i._id.toString());

          for (let i = 0; i < optimisedSupplyAgreements.length; i++) {
            const optimisedSupplyAgreement = optimisedSupplyAgreements[i];

            if (
              optimisedSupplyAgreement._id &&
              idsOfExistingSupplyAgreementsForThisBooking.includes(
                optimisedSupplyAgreement._id?.toString(),
              )
            ) {
              const updated = await Models.GmpSupplyAgreement.findByIdAndUpdate(
                optimisedSupplyAgreement._id,
                optimisedSupplyAgreement,
                { new: true },
              ).then((o) => o);
              if (!updated) {
                isSuccess = false;
              }
            } else {
              const supplyAgreementToSave = new Models.GmpSupplyAgreement(
                optimisedSupplyAgreement,
              );
              const saved = await supplyAgreementToSave.save().then((o) => o);
              if (!saved) {
                isSuccess = false;
              }
            }
          }

          if (isSuccess) {
            // remove redundant agreements for this booking
            const idsOfOptimisedSupplyAgreements = optimisedSupplyAgreements
              .filter((i) => i._id)
              .map((i) => i._id);
            const idsOfExistingSupplyAgreementsToRemoveFromThisBooking =
              idsOfExistingSupplyAgreementsForThisBooking.filter(
                (i) => !idsOfOptimisedSupplyAgreements.includes(i),
              );

            for (
              let i = 0;
              i < idsOfExistingSupplyAgreementsToRemoveFromThisBooking.length;
              i++
            ) {
              await Models.GmpSupplyAgreement.findByIdAndDelete(
                idsOfExistingSupplyAgreementsToRemoveFromThisBooking[i],
              ).then();
            }

            // update current booking
            await Models.GmpBooking.findByIdAndUpdate(bookingId, {
              status: GmpBookingStatus.Completed,
            }).then();

            // which ones are missing in GmpPic modal
            const existingPics = await Models.GmpPic.find({
              pic: { $in: optimisedSupplyAgreements.map((i) => i.pic) },
            }).select("pic -_id");
            const existingPicStrings = existingPics.map((doc) => doc.pic);
            const optimisedSupplyAgreementsWithMissingPic =
              optimisedSupplyAgreements.filter(
                (agreement) => !existingPicStrings.includes(agreement.pic),
              );
            if (optimisedSupplyAgreementsWithMissingPic.length > 0) {
              // To Do: can save these new PICs to GmpPic model once they are verified against NLIS
              // save those PICs into GmpPic modal
              // await Models.GmpPic.insertMany(optimisedSupplyAgreementsWithMissingPic.map(i => ({pic: i.pic, businessName: i.producerBusinessName, isAgent: i.isAgent})));

              // only send out notification for PRD server
              if (SNOWFLAKE_ROLE?.includes("PRD")) {
                // notify GMP admin about these new PICs
                try {
                  const emailTemplate = await fs.readFile(
                    "src/email_templates/gmp-notify-new-pics.html",
                    { encoding: "utf8" },
                  );
                  const html = emailTemplate
                    .replaceAll("[INSERT_YEAR]", moment().year().toString())
                    .replaceAll(
                      "[INSERT_NEW_PICS]",
                      optimisedSupplyAgreementsWithMissingPic
                        .map(
                          (agreement) =>
                            `
                        <b>${agreement.isAgent ? "Agent" : "Producer"} Name</b>
                        <br>
                        ${agreement.producerFirstName} ${agreement.producerLastName}
                        <br>
                        ${agreement.producerBusinessName}
                        <br>
                        ${booking.vendorEmail}
                        <br>
                        <b>New PIC</b>
                        <br>${agreement.pic}
                        <br>
                        `,
                        )
                        .join(`<p></p>`),
                    );
                  const emailToSent: SystemEmail = {
                    to: GMP_ADMIN_EMAIL,
                    subject: "New PICs Received",
                    html,
                  };
                  const sentMessageInfo = await sendMail(emailToSent);
                  // don't show message to producers. just print it out for dev purpose.
                  if (sentMessageInfo.isSuccess) {
                    console.log(Message.SUCCESS);
                  } else {
                    console.log(Message.ERROR_SEND_EMAIL);
                  }
                } catch (e) {
                  console.log(Message.ERROR_SEND_EMAIL);
                  logExceptionToDB(e);
                }
              }
            }
          }

          const allBookings = await getAllMyBookings({
            vendorEmail: user.email,
          });

          result.data = JSON.stringify(allBookings);
          result.msg = Message.SUCCESS;
          result.code = ResultCode.SUCCESS;
        } else {
          result.msg = Message.ERROR_ITEM_NOT_FOUND;
        }
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const getAllAgentsAndProducers: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const roles = user.roles;

  if (roles) {
    if (hasPermission({ roles, requiredRoles: [GMP_ROLE.ADMIN] })) {
      const allVendors = await Models.User.find({
        roles: { $in: [GMP_ROLE.AGENT, GMP_ROLE.PRODUCER] },
      }).then((o) => o);

      result.data = JSON.stringify(allVendors);
      result.msg = Message.SUCCESS;
      result.code = ResultCode.SUCCESS;
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PERMISSION_DENIED;
  }

  res.status(200).json(result);
};

const getAllMyBookings = async ({ vendorEmail }: { vendorEmail: string }) =>
  await Models.GmpBooking.find({ vendorEmail, isValid: true })
    .sort({ year: -1, week: -1 })
    .then((o) => o);

export const getMyBookings: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const roles = user.roles;

  if (roles) {
    if (
      hasPermission({
        roles,
        requiredRoles: [GMP_ROLE.AGENT, GMP_ROLE.PRODUCER],
      })
    ) {
      const all = await getAllMyBookings({ vendorEmail: user.email });

      result.data = JSON.stringify(all);
      result.msg = Message.SUCCESS;
      result.code = ResultCode.SUCCESS;
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PERMISSION_DENIED;
  }

  res.status(200).json(result);
};

export const getBookingSupplyAgreements: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { bookingId } = sanitiseParams(req.body);

  if (bookingId) {
    const roles = user.roles;

    if (roles) {
      if (
        hasPermission({
          roles,
          requiredRoles: [GMP_ROLE.ADMIN, GMP_ROLE.AGENT, GMP_ROLE.PRODUCER],
        })
      ) {
        const all = await Models.GmpSupplyAgreement.find({
          bookingId,
          isValid: true,
        }).then((o) => o);

        result.data = JSON.stringify(all);
        result.msg = Message.SUCCESS;
        result.code = ResultCode.SUCCESS;
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const getMySupplyAgreements: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { bookingId } = sanitiseParams(req.body);

  if (bookingId) {
    const roles = user.roles;

    if (roles) {
      if (
        hasPermission({
          roles,
          requiredRoles: [GMP_ROLE.AGENT, GMP_ROLE.PRODUCER],
        })
      ) {
        const booking = await Models.GmpBooking.findOne({
          _id: bookingId,
          vendorEmail: user.email,
          isValid: true,
        });
        if (booking) {
          const all = await Models.GmpSupplyAgreement.find({
            bookingId,
            isValid: true,
          }).then((o) => o);

          result.data = JSON.stringify(all);
          result.msg = Message.SUCCESS;
          result.code = ResultCode.SUCCESS;
        } else {
          result.msg = Message.ERROR_BOOKING_NOT_FOUND;
        }
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};
