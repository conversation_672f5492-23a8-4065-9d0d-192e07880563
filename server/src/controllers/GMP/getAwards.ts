import { Response } from "express";
import { z } from "zod";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { findDb, getReqQueryIfPermitted } from "./shared";
import { AuthenticatedRequest } from "../Common";
import {
  ProducerOfTheYearScoringConfig,
  GridComplianceScoringConfig,
  GLQScoreScoringConfig,
} from "../../models/GMPScoringConfigs";

const sfDb = findDb();

const getDefaultStartYear = () => {
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth() + 1; // getMonth() returns 0-11

  // If we're in August or later, use current year
  // If we're before August, use previous year
  return currentMonth >= 8 ? currentYear : currentYear - 1;
};

interface ProducerOfTheYearScoringConfig {
  consignment: { target: number; weight: number };
  glqAvg: { target: number; weight: number };
  glqStddev: { target: number; weight: number };
  hscwAvg: { target: number; weight: number };
  hscwStddev: { target: number; weight: number };
  minConsignmentCount: number;
}

interface GridComplianceScoringConfig {
  minHeadPerConsignment: number;
  minConsignmentCount: number;
  hscw: {
    min: number;
    max: number;
    weight: number;
  };
  lmy: {
    min: number;
    max: number;
    weight: number;
  };
}

interface GLQScoreScoringConfig {
  minHeadPerConsignment: number;
  minConsignmentCount: number;
}

export const DEFAULT_PRODUCER_OF_THE_YEAR_CONFIG: ProducerOfTheYearScoringConfig = {
  consignment: { target: 50, weight: 0.2 },
  glqAvg: { target: 6, weight: 0.4 },
  glqStddev: { target: 1, weight: 0.1 },
  hscwAvg: { target: 28, weight: 0.2 },
  hscwStddev: { target: 3, weight: 0.1 },
  minConsignmentCount: 2,
};

export const DEFAULT_GRID_COMPLIANCE_CONFIG: GridComplianceScoringConfig = {
  minHeadPerConsignment: 150,
  minConsignmentCount: 2,
  hscw: {
    min: 20,
    max: 32,
    weight: 50,
  },
  lmy: {
    min: 55,
    max: 62,
    weight: 50,
  },
};

export const DEFAULT_GLQ_SCORE_CONFIG: GLQScoreScoringConfig = {
  minHeadPerConsignment: 150,
  minConsignmentCount: 2,
};

const gridComplianceQuerySchema = z.object({
  startYear: z.coerce.number().default(() => getDefaultStartYear()),
  config: z
    .object({
      minHeadPerConsignment: z.coerce.number().optional(),
      minConsignmentCount: z.coerce.number().optional(),
      hscw: z
        .object({
          min: z.coerce.number(),
          max: z.coerce.number(),
          weight: z.coerce.number(),
        })
        .optional(),
      lmy: z
        .object({
          min: z.coerce.number(),
          max: z.coerce.number(),
          weight: z.coerce.number(),
        })
        .optional(),
    })
    .optional(),
});

const glqScoreQuerySchema = z.object({
  startYear: z.coerce.number().default(() => getDefaultStartYear()),
  config: z
    .object({
      minHeadPerConsignment: z.coerce.number().optional(),
      minConsignmentCount: z.coerce.number().optional(),
    })
    .optional(),
});

const producerOfTheYearQuerySchema = z.object({
  startYear: z.coerce.number().default(() => getDefaultStartYear()),
  config: z
    .object({
      consignment: z
        .object({ target: z.coerce.number(), weight: z.coerce.number() })
        .optional(),
      glqAvg: z.object({ target: z.coerce.number(), weight: z.coerce.number() }).optional(),
      glqStddev: z
        .object({ target: z.coerce.number(), weight: z.coerce.number() })
        .optional(),
      hscwAvg: z.object({ target: z.coerce.number(), weight: z.coerce.number() }).optional(),
      hscwStddev: z
        .object({ target: z.coerce.number(), weight: z.coerce.number() })
        .optional(),
      minConsignmentCount: z.coerce.number().optional(),
    })
    .optional(),
});

const PARAMETERS = `
WITH PARAMETERS AS (
    SELECT
        :1 as START_YEAR
)
`;

const buildConsignmentCountsQuery = (minHeadPerConsignment: number) => `
CONSIGNMENT_COUNTS AS (
    SELECT
        KILL_DATE,
        PIC,
        COUNT(*) as ANIMAL_COUNT
    FROM ###DB###.MEQINSIGHTS.GMP_INSIGHTS_CARCASE
    WHERE KILL_DATE >= (SELECT DATE_FROM_PARTS(START_YEAR, 8, 1) FROM PARAMETERS)
        AND KILL_DATE < (SELECT DATE_FROM_PARTS(START_YEAR + 1, 8, 1) FROM PARAMETERS)
    GROUP BY KILL_DATE, PIC
    HAVING COUNT(*) >= ${minHeadPerConsignment}
)
`;
const buildPicConsignmentCountsQuery = (minConsignmentCount: number) => `
PIC_CONSIGNMENT_COUNTS AS (
    SELECT
        PIC,
        COUNT(*) as CONSIGNMENT_COUNT
    FROM CONSIGNMENT_COUNTS
    GROUP BY PIC
    HAVING COUNT(*) >= ${minConsignmentCount}
)
`;

const buildGridComplianceAwardQuery = (config: GridComplianceScoringConfig) => `
PIC_SCORES AS (
    SELECT
        C.PIC,
        COUNT(*) as TOTAL_ANIMALS,
        SUM(CASE WHEN C.HSCW BETWEEN ${config.hscw.min} AND ${config.hscw.max} THEN 1 ELSE 0 END) as HSCW_IN_RANGE,
        SUM(CASE WHEN C.LMY BETWEEN ${config.lmy.min} AND ${config.lmy.max} THEN 1 ELSE 0 END) as LMY_IN_RANGE
    FROM ###DB###.MEQINSIGHTS.GMP_INSIGHTS_CARCASE C
    INNER JOIN PIC_CONSIGNMENT_COUNTS PCC ON C.PIC = PCC.PIC
    INNER JOIN CONSIGNMENT_COUNTS CC ON C.KILL_DATE = CC.KILL_DATE AND C.PIC = CC.PIC
    WHERE C.KILL_DATE >= (SELECT DATE_FROM_PARTS(START_YEAR, 8, 1) FROM PARAMETERS)
        AND C.KILL_DATE < (SELECT DATE_FROM_PARTS(START_YEAR + 1, 8, 1) FROM PARAMETERS)
    GROUP BY C.PIC
)

SELECT
    PS.PIC,
    PCC.CONSIGNMENT_COUNT,
    PS.TOTAL_ANIMALS,
    ROUND((PS.HSCW_IN_RANGE::FLOAT / PS.TOTAL_ANIMALS) * ${config.hscw.weight} + (PS.LMY_IN_RANGE::FLOAT / PS.TOTAL_ANIMALS) * ${config.lmy.weight}, 2) as TOTAL_SCORE_PERCENTAGE,
    ROUND((PS.HSCW_IN_RANGE::FLOAT / PS.TOTAL_ANIMALS) * 100, 2) as HSCW_SCORE_PERCENTAGE,
    ROUND((PS.LMY_IN_RANGE::FLOAT / PS.TOTAL_ANIMALS) * 100, 2) as LMY_SCORE_PERCENTAGE
FROM PIC_SCORES PS
INNER JOIN PIC_CONSIGNMENT_COUNTS PCC ON PS.PIC = PCC.PIC
ORDER BY TOTAL_SCORE_PERCENTAGE DESC;
`;

export const getGridComplianceAward = async (
  req: AuthenticatedRequest,
  res: Response,
) => {
  const { reqQuery, error } = await getReqQueryIfPermitted(
    req,
    gridComplianceQuerySchema,
  );
  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  try {
    const { startYear, config: gridComplianceConfig } = reqQuery;

    // Fetch config from database or use provided override
    const savedConfig = await GridComplianceScoringConfig.findOne({
      org: "GMP",
    });

    const config: GridComplianceScoringConfig = {
      minHeadPerConsignment:
        gridComplianceConfig?.minHeadPerConsignment ||
        savedConfig?.minHeadPerConsignment ||
        DEFAULT_GRID_COMPLIANCE_CONFIG.minHeadPerConsignment,
      minConsignmentCount:
        gridComplianceConfig?.minConsignmentCount ||
        savedConfig?.minConsignmentCount ||
        DEFAULT_GRID_COMPLIANCE_CONFIG.minConsignmentCount,
      hscw:
        gridComplianceConfig?.hscw ||
        savedConfig?.hscw ||
        DEFAULT_GRID_COMPLIANCE_CONFIG.hscw,
      lmy:
        gridComplianceConfig?.lmy ||
        savedConfig?.lmy ||
        DEFAULT_GRID_COMPLIANCE_CONFIG.lmy,
    };

    const query = `
      ${PARAMETERS},
      ${buildConsignmentCountsQuery(config.minHeadPerConsignment)},
      ${buildPicConsignmentCountsQuery(config.minConsignmentCount)},
      ${buildGridComplianceAwardQuery(config)}
    `.replaceAll(/###DB###/g, sfDb);

    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: query,
      binds: [startYear.toString()],
      db: sfDb,
    });

    return res.json({
      columns,
      rows,
    });
  } catch (error) {
    console.error("Error fetching GMP awards:", error);
    return res.status(500).json({
      success: false,
      error: "Failed to fetch GMP awards data",
    });
  }
};

const GLQ_SCORE_AWARD = `
CONSIGNMENT_GLQ AS (
    SELECT
        C.KILL_DATE,
        C.PIC,
        CC.ANIMAL_COUNT,
        AVG(C.GLQ) as AVG_GLQ
    FROM ###DB###.MEQINSIGHTS.GMP_INSIGHTS_CARCASE C
    INNER JOIN CONSIGNMENT_COUNTS CC
        ON C.KILL_DATE = CC.KILL_DATE
        AND C.PIC = CC.PIC
    WHERE C.KILL_DATE >= (SELECT DATE_FROM_PARTS(START_YEAR, 8, 1) FROM PARAMETERS)
        AND C.KILL_DATE < (SELECT DATE_FROM_PARTS(START_YEAR + 1, 8, 1) FROM PARAMETERS)
        AND C.GLQ IS NOT NULL
    GROUP BY C.KILL_DATE, C.PIC, CC.ANIMAL_COUNT
)

SELECT
    PIC,
    KILL_DATE,
    ANIMAL_COUNT,
    ROUND(AVG_GLQ, 2) as AVG_GLQ
FROM CONSIGNMENT_GLQ
ORDER BY AVG_GLQ DESC;
`;
export const getGLQScoreAward = async (
  req: AuthenticatedRequest,
  res: Response,
) => {
  const { reqQuery, error } = await getReqQueryIfPermitted(
    req,
    glqScoreQuerySchema,
  );
  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  try {
    const { startYear, config: glqScoreConfig } = reqQuery;

    // Fetch config from database or use provided override
    const savedConfig = await GLQScoreScoringConfig.findOne({ org: "GMP" });

    const minHeadPerConsignment =
      glqScoreConfig?.minHeadPerConsignment ||
      savedConfig?.minHeadPerConsignment ||
      DEFAULT_GLQ_SCORE_CONFIG.minHeadPerConsignment;

    const minConsignmentCount =
      glqScoreConfig?.minConsignmentCount ||
      savedConfig?.minConsignmentCount ||
      DEFAULT_GLQ_SCORE_CONFIG.minConsignmentCount;

    const query = `
      ${PARAMETERS},
      ${buildConsignmentCountsQuery(minHeadPerConsignment)},
      ${buildPicConsignmentCountsQuery(minConsignmentCount)},
      ${GLQ_SCORE_AWARD}
    `.replaceAll(/###DB###/g, sfDb);

    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: query,
      binds: [startYear.toString()],
      db: sfDb,
    });

    return res.json({
      columns,
      rows,
    });
  } catch (error) {
    console.error("Error fetching GMP awards:", error);
    return res.status(500).json({
      success: false,
      error: "Failed to fetch GMP awards data",
    });
  }
};

const buildProducerOfTheYearQuery = (
  config: ProducerOfTheYearScoringConfig,
) => `
PRODUCER_METRICS AS (
    SELECT
        C.PIC,
        COUNT(DISTINCT C.KILL_DATE) as CONSIGNMENT_COUNT,
        AVG(C.GLQ) as AVG_GLQ,
        STDDEV(C.GLQ) as GLQ_STDDEV,
        AVG(C.HSCW) as AVG_HSCW,
        STDDEV(C.HSCW) as HSCW_STDDEV
    FROM ###DB###.MEQINSIGHTS.GMP_INSIGHTS_CARCASE C
    INNER JOIN PIC_CONSIGNMENT_COUNTS PCC ON C.PIC = PCC.PIC
    WHERE C.KILL_DATE >= (SELECT DATE_FROM_PARTS(START_YEAR, 8, 1) FROM PARAMETERS)
        AND C.KILL_DATE < (SELECT DATE_FROM_PARTS(START_YEAR + 1, 8, 1) FROM PARAMETERS)
        AND C.GLQ IS NOT NULL
        AND C.HSCW IS NOT NULL
    GROUP BY C.PIC
)

SELECT
    PM.PIC,
    PM.CONSIGNMENT_COUNT,
    ROUND((PM.CONSIGNMENT_COUNT / ${config.consignment.target} * 100) * ${config.consignment.weight}, 2) as CONSIGNMENT_SCORE,
    ROUND(PM.AVG_GLQ, 2) as AVG_GLQ,
    ROUND((PM.AVG_GLQ / ${config.glqAvg.target} * 100) * ${config.glqAvg.weight}, 2) as GLQ_AVG_SCORE,
    ROUND(PM.GLQ_STDDEV, 2) as GLQ_STDDEV,
    ROUND((PM.GLQ_STDDEV / ${config.glqStddev.target} * 100) * ${config.glqStddev.weight}, 2) as GLQ_STDDEV_SCORE,
    ROUND(PM.AVG_HSCW, 2) as AVG_HSCW,
    ROUND((PM.AVG_HSCW / ${config.hscwAvg.target} * 100) * ${config.hscwAvg.weight}, 2) as HSCW_AVG_SCORE,
    ROUND(PM.HSCW_STDDEV, 2) as HSCW_STDDEV,
    ROUND((PM.HSCW_STDDEV / ${config.hscwStddev.target} * 100) * ${config.hscwStddev.weight}, 2) as HSCW_STDDEV_SCORE,
    -- Total score
    ROUND(
        (PM.AVG_GLQ / ${config.glqAvg.target} * 100) * ${config.glqAvg.weight} +
        (PM.GLQ_STDDEV / ${config.glqStddev.target} * 100) * ${config.glqStddev.weight} +
        (PM.AVG_HSCW / ${config.hscwAvg.target} * 100) * ${config.hscwAvg.weight} +
        (PM.HSCW_STDDEV / ${config.hscwStddev.target} * 100) * ${config.hscwStddev.weight} +
        (PM.CONSIGNMENT_COUNT / ${config.consignment.target} * 100) * ${config.consignment.weight},
        2
    ) as TOTAL_SCORE
FROM PRODUCER_METRICS PM
ORDER BY TOTAL_SCORE DESC;
`;

export const getProducerOfTheYear = async (
  req: AuthenticatedRequest,
  res: Response,
) => {
  const { reqQuery, error } = await getReqQueryIfPermitted(
    req,
    producerOfTheYearQuerySchema,
  );
  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  try {
    const { startYear, config: producerOfTheYearConfig } = reqQuery;

    // Fetch config from database or use provided overrides
    const savedConfig = await ProducerOfTheYearScoringConfig.findOne({
      org: "GMP",
    });

    const config: ProducerOfTheYearScoringConfig = {
      consignment:
        producerOfTheYearConfig?.consignment ||
        savedConfig?.consignment ||
        DEFAULT_PRODUCER_OF_THE_YEAR_CONFIG.consignment,
      glqAvg:
        producerOfTheYearConfig?.glqAvg ||
        savedConfig?.glqAvg ||
        DEFAULT_PRODUCER_OF_THE_YEAR_CONFIG.glqAvg,
      glqStddev:
        producerOfTheYearConfig?.glqStddev ||
        savedConfig?.glqStddev ||
        DEFAULT_PRODUCER_OF_THE_YEAR_CONFIG.glqStddev,
      hscwAvg:
        producerOfTheYearConfig?.hscwAvg ||
        savedConfig?.hscwAvg ||
        DEFAULT_PRODUCER_OF_THE_YEAR_CONFIG.hscwAvg,
      hscwStddev:
        producerOfTheYearConfig?.hscwStddev ||
        savedConfig?.hscwStddev ||
        DEFAULT_PRODUCER_OF_THE_YEAR_CONFIG.hscwStddev,
      minConsignmentCount:
        producerOfTheYearConfig?.minConsignmentCount ||
        savedConfig?.minConsignmentCount ||
        DEFAULT_PRODUCER_OF_THE_YEAR_CONFIG.minConsignmentCount,
    };

    const query = `
      ${PARAMETERS},
      ${buildConsignmentCountsQuery(1)},
      ${buildPicConsignmentCountsQuery(config.minConsignmentCount)},
      ${buildProducerOfTheYearQuery(config)}
    `.replaceAll(/###DB###/g, sfDb);

    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: query,
      binds: [startYear.toString()],
      db: sfDb,
    });

    return res.json({
      columns,
      rows,
    });
  } catch (error) {
    console.error("Error fetching Producer of the Year data:", error);
    return res.status(500).json({
      success: false,
      error: "Failed to fetch Producer of the Year data",
    });
  }
};

// Scoring Config Management Endpoints

// Validation schemas for config endpoints
const producerOfTheYearConfigSchema = z.object({
  consignment: z.object({
    target: z.number().positive(),
    weight: z.number().min(0).max(1),
  }),
  glqAvg: z.object({
    target: z.number().positive(),
    weight: z.number().min(0).max(1),
  }),
  glqStddev: z.object({
    target: z.number().positive(),
    weight: z.number().min(0).max(1),
  }),
  hscwAvg: z.object({
    target: z.number().positive(),
    weight: z.number().min(0).max(1),
  }),
  hscwStddev: z.object({
    target: z.number().positive(),
    weight: z.number().min(0).max(1),
  }),
  minConsignmentCount: z.number().int().positive(),
});

const gridComplianceConfigSchema = z.object({
  minHeadPerConsignment: z.number().int().positive(),
  minConsignmentCount: z.number().int().positive(),
  hscw: z.object({
    min: z.number().positive(),
    max: z.number().positive(),
    weight: z.number().positive(),
  }),
  lmy: z.object({
    min: z.number().positive(),
    max: z.number().positive(),
    weight: z.number().positive(),
  }),
});

const glqScoreConfigSchema = z.object({
  minHeadPerConsignment: z.number().int().positive(),
  minConsignmentCount: z.number().int().positive(),
});

// Producer of the Year config endpoints
export const saveProducerOfTheYearConfig = async (
  req: AuthenticatedRequest,
  res: Response,
) => {
  try {
    const validationResult = producerOfTheYearConfigSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        error: "Invalid configuration data",
        details: validationResult.error.errors,
      });
    }

    const config = validationResult.data;
    const org = "GMP";

    const savedConfig = await ProducerOfTheYearScoringConfig.findOneAndUpdate(
      { org },
      { ...config, org },
      { upsert: true, new: true },
    );

    return res.json({
      consignment: savedConfig.consignment,
      glqAvg: savedConfig.glqAvg,
      glqStddev: savedConfig.glqStddev,
      hscwAvg: savedConfig.hscwAvg,
      hscwStddev: savedConfig.hscwStddev,
      minConsignmentCount: savedConfig.minConsignmentCount,
    });
  } catch (error) {
    console.error("Error saving Producer of the Year config:", error);
    return res.status(500).json({
      success: false,
      error: "Failed to save configuration",
    });
  }
};

export const getProducerOfTheYearConfig = async (
  req: AuthenticatedRequest,
  res: Response,
) => {
  try {
    const org = "GMP";

    const config = await ProducerOfTheYearScoringConfig.findOne({ org });

    if (!config) {
      return res.status(404).json({
        success: false,
        error: "Configuration not found",
      });
    }

    return res.json({
      consignment: config.consignment,
      glqAvg: config.glqAvg,
      glqStddev: config.glqStddev,
      hscwAvg: config.hscwAvg,
      hscwStddev: config.hscwStddev,
      minConsignmentCount: config.minConsignmentCount,
    });
  } catch (error) {
    console.error("Error fetching Producer of the Year config:", error);
    return res.status(500).json({
      success: false,
      error: "Failed to fetch configuration",
    });
  }
};

// Grid Compliance config endpoints
export const saveGridComplianceConfig = async (
  req: AuthenticatedRequest,
  res: Response,
) => {
  try {
    const validationResult = gridComplianceConfigSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        error: "Invalid configuration data",
        details: validationResult.error.errors,
      });
    }

    const config = validationResult.data;
    const org = "GMP";

    const savedConfig = await GridComplianceScoringConfig.findOneAndUpdate(
      { org },
      { ...config, org },
      { upsert: true, new: true },
    );

    return res.json({
      minHeadPerConsignment: savedConfig.minHeadPerConsignment,
      minConsignmentCount: savedConfig.minConsignmentCount,
      hscw: savedConfig.hscw,
      lmy: savedConfig.lmy,
    });
  } catch (error) {
    console.error("Error saving Grid Compliance config:", error);
    return res.status(500).json({
      success: false,
      error: "Failed to save configuration",
    });
  }
};

export const getGridComplianceConfig = async (
  req: AuthenticatedRequest,
  res: Response,
) => {
  try {
    const org = "GMP";

    const config = await GridComplianceScoringConfig.findOne({ org });

    if (!config) {
      return res.status(404).json({
        success: false,
        error: "Configuration not found",
      });
    }

    return res.json({
      minHeadPerConsignment: config.minHeadPerConsignment,
      minConsignmentCount: config.minConsignmentCount,
      hscw: config.hscw,
      lmy: config.lmy,
    });
  } catch (error) {
    console.error("Error fetching Grid Compliance config:", error);
    return res.status(500).json({
      success: false,
      error: "Failed to fetch configuration",
    });
  }
};

// GLQ Score config endpoints
export const saveGLQScoreConfig = async (
  req: AuthenticatedRequest,
  res: Response,
) => {
  try {
    const validationResult = glqScoreConfigSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        error: "Invalid configuration data",
        details: validationResult.error.errors,
      });
    }

    const config = validationResult.data;
    const org = "GMP";

    const savedConfig = await GLQScoreScoringConfig.findOneAndUpdate(
      { org },
      { ...config, org },
      { upsert: true, new: true },
    );

    return res.json({
      minHeadPerConsignment: savedConfig.minHeadPerConsignment,
      minConsignmentCount: savedConfig.minConsignmentCount,
    });
  } catch (error) {
    console.error("Error saving GLQ Score config:", error);
    return res.status(500).json({
      success: false,
      error: "Failed to save configuration",
    });
  }
};

export const getGLQScoreConfig = async (
  req: AuthenticatedRequest,
  res: Response,
) => {
  try {
    const org = "GMP";

    const config = await GLQScoreScoringConfig.findOne({ org });

    if (!config) {
      return res.status(404).json({
        success: false,
        error: "Configuration not found",
      });
    }

    return res.json({
      minHeadPerConsignment: config.minHeadPerConsignment,
      minConsignmentCount: config.minConsignmentCount,
    });
  } catch (error) {
    console.error("Error fetching GLQ Score config:", error);
    return res.status(500).json({
      success: false,
      error: "Failed to fetch configuration",
    });
  }
};
