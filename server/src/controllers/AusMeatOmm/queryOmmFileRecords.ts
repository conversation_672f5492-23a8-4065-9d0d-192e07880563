import { Response } from "express";
import { z } from "zod";

import { AuthenticatedRequest } from "../Common";
import { authBodySchema, getReqBodyIfPermitted, MAX_PAGE_SIZE } from "./shared";
import { OmmFileRecord } from "../../models/AusMeatOmm";
import { UserSettings } from "../../models/UserSettings";
import { StatusCodes } from "http-status-codes";

const bodySchema = authBodySchema.extend({
  maxFileDate: z.string().date(),
  pageSize: z.number().min(1).max(MAX_PAGE_SIZE),
});

export async function queryOmmFileRecords(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfPermitted(req, bodySchema);

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  const fileRecords = await OmmFileRecord.find({
    org: reqBody!.org,
    location: reqBody!.location,
    fileDate: { $lte: reqBody!.maxFileDate },
  })
    .sort({ fileDate: -1 })
    .limit(reqBody!.pageSize);

  const userSettings = await UserSettings.findOne({
    org: reqBody!.org,
    location: reqBody!.location,
    ausMeatOmm: { $ne: null },
  });

  if (!userSettings) {
    res.status(StatusCodes.NOT_FOUND).send("Go live date not found.");
    return;
  }

  res.json({
    ommFileRecords: fileRecords,
    goLiveDate: userSettings.ausMeatOmm!.goLiveDate,
  });
}
