import { Response } from "express";

import { AuthenticatedRequest } from "../Common";
import {
  findOmmFile,
  getReqBodyIfPermitted,
  findOmmFileBodySchema,
} from "./shared";

export async function queryOmmFile(req: AuthenticatedRequest, res: Response) {
  const { reqBody, error } = await getReqBodyIfPermitted(
    req,
    findOmmFileBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  const { ommFile, error: findOmmFileError } = await findOmmFile(reqBody!);

  if (findOmmFileError) {
    res.status(findOmmFileError.statusCode).send();
    return;
  }

  res.json(ommFile);
}
