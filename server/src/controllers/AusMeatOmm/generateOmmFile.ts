import { Response } from "express";
import { z } from "zod";
import _ from "lodash";
import moment from "moment-timezone";

import { AuthenticatedRequest } from "../Common";
import { authBodySchema, getReqBodyIfPermitted } from "./shared";
import {
  IOmmMessage,
  IOmmMessagePreset,
  MEASUREMENT_TYPES,
  OmmFile,
  OmmFileRecord,
  IOmmFile,
  IMeasurementsDatum,
  IDeviceDatum,
  ICarcaseDatum,
  OmmMessagePreset,
  NO_VALUE_RECORD,
} from "../../models/AusMeatOmm";
import { UserSettings } from "../../models/UserSettings";
import { IAusMeatOmmSettings } from "../../models/UserSettings";
import { findSnowflakeClientDb } from "../../utils/SnowflakeDatabases";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { randomUUID } from "crypto";
import { StatusCodes } from "http-status-codes";

const bodySchema = authBodySchema.extend({
  fileDate: z.string().date(),
});

type BodySchema = z.infer<typeof bodySchema>;

const userSettingsSchema = z.object({
  apiKey: z.string().min(1, "API Key must not be empty."),
  establishmentNumber: z.string().min(1, "Establish Number must not be empty."),
  chainNumber: z.number().int("Chain Number must be an integer."),
});

export async function generateOmmFile(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfPermitted(req, bodySchema);

  if (error?.statusCode === StatusCodes.FORBIDDEN) {
    res.status(error.statusCode).send();
    return;
  }

  if (error?.statusCode === StatusCodes.BAD_REQUEST) {
    res.status(error.statusCode).json({
      message: error.message,
    });
    return;
  }

  const userSettings = await UserSettings.findOne({
    org: reqBody!.org,
    location: reqBody!.location,
    ausMeatOmm: { $ne: null },
  });

  if (!userSettings?.ausMeatOmm) {
    throw new Error("AUS-MEAT OMM settings not found.");
  }

  if (!userSettingsSchema.safeParse(userSettings.ausMeatOmm).success) {
    res.status(StatusCodes.BAD_REQUEST).json({
      message: "Invalid AUS-MEAT OMM settings.",
    });
    return;
  }

  if (userSettings.ausMeatOmm.goLiveDate > reqBody!.fileDate) {
    res.status(StatusCodes.BAD_REQUEST).json({
      message: "File date must be greater than go-live date.",
    });
    return;
  }

  const fileRecordDoc = await OmmFileRecord.findOne({
    org: reqBody!.org,
    location: reqBody!.location,
    fileDate: reqBody!.fileDate,
  });

  if (fileRecordDoc?.ommFileIdSent) {
    res
      .status(StatusCodes.BAD_REQUEST)
      .json({ message: "OMM file has already been sent for this record." });
    return;
  }

  const ommRows = await queryOmmRows(reqBody!);
  if (ommRows.length === 0) {
    res.status(StatusCodes.NOT_FOUND).json({ message: "OMM data not found." });
    return;
  }

  const ommMessagePreset = await OmmMessagePreset.findOne();
  if (!ommMessagePreset) {
    throw new Error("OMM message preset not found.");
  }

  const ommFile = convertRowsToOmmFile(
    ommRows,
    ommMessagePreset,
    userSettings.ausMeatOmm,
  );
  const savedFileDoc = await new OmmFile(ommFile).save();

  if (fileRecordDoc == null) {
    const savedFileRecordDoc = await new OmmFileRecord({
      org: reqBody!.org,
      location: reqBody!.location,
      fileDate: reqBody!.fileDate,
      ommFileIdSent: undefined,
      ommFilesGenerated: [
        {
          generatedAt: new Date(),
          ommFileId: savedFileDoc._id,
        },
      ],
    }).save();
    res.json({
      success: true,
      ommFile: savedFileDoc.toObject(),
      ommFileRecord: savedFileRecordDoc.toObject(),
    });
  } else {
    fileRecordDoc.ommFilesGenerated.push({
      generatedAt: new Date(),
      ommFileId: savedFileDoc._id.toHexString(),
    });
    const savedFileRecordDoc = await fileRecordDoc.save();

    res.json({
      success: true,
      ommFile: savedFileDoc.toObject(),
      ommFileRecord: savedFileRecordDoc.toObject(),
    });
  }
}

interface OmmSnowflakeRow {
  COMPANY_LOCATION: string;
  KILLDATE: string;
  KILLLOT: string;
  BODYNUMBER: number;
  STUNNINGDATETIME: string;
  EQUIPMENTSERIALNUMBER: string;
  MEASUREMENTDATETIME: string;
  MEASUREMENTDEVICEMSA: string;
  MEASUREMENTDEVICEAUSMB: string;
  MEASUREMENTDEVICEFC: string;
  MEASUREMENTDEVICEMC: string;
  MEASUREMENTDEVICEEMA: string;
  MEASUREMENTHUMANMSA: number;
  MEASUREMENTHUMANAUSMB: number;
  MEASUREMENTHUMANFC: number;
  MEASUREMENTHUMANMC: string;
  MEASUREMENTASSIGNEDMSA: number;
  MEASUREMENTASSIGNEDAUSMB: number;
  MEASUREMENTASSIGNEDFC: number;
  MEASUREMENTASSIGNEDMC: string;
  MEASUREMENTASSIGNEDEMA: number;
  MEASUREMENTSERIALNUMBER: string;
  MEASUREMENTOPERATOR: string;
}

async function queryOmmRows(reqBody: BodySchema): Promise<OmmSnowflakeRow[]> {
  const db = findSnowflakeClientDb(reqBody.org, reqBody.location);

  const { rows } = await executeSnowflakeStatement({
    sqlText: `
      set kill_date = ?;
      set company_location = ?;
      select *, to_number(BODYNUMBER) as BODYNUMBER
      from ${db}.MEQINSIGHTS.JBSAUS_AUSMEAT_OMM
      where KILLDATE = $kill_date and COMPANY_LOCATION = $company_location
        and (
          measurementdevicemsa is not null
          or measurementdeviceausmb is not null
          or measurementdevicefc is not null
          or measurementdevicemc is not null
          or measurementdeviceema is not null
        )
    `,
    binds: [
      reqBody.fileDate.replaceAll("-", ""),
      `${reqBody.org}-${reqBody.location}`,
    ],
    parameters: {
      MULTI_STATEMENT_COUNT: 3,
    },
    db,
  });

  return rows as OmmSnowflakeRow[];
}

function convertRowsToOmmFile(
  rows: OmmSnowflakeRow[],
  preset: IOmmMessagePreset,
  userConfig: IAusMeatOmmSettings,
): IOmmFile {
  const timeZoneOffset = getTimeZoneOffset(userConfig);
  const messages: IOmmMessage[] = _.values(
    _.groupBy(rows, (r) => r.KILLLOT),
  ).map((messageRows) => {
    const carcaseData: ICarcaseDatum[] = _.values(
      _.groupBy(messageRows, (r) => r.BODYNUMBER),
    ).map((carcaseRows) => {
      const deviceData: IDeviceDatum[] = _.values(
        _.groupBy(carcaseRows, (r) => r.EQUIPMENTSERIALNUMBER),
      ).map((deviceRows) => {
        const measurementsData: IMeasurementsDatum[] = deviceRows.flatMap((r) =>
          MEASUREMENT_TYPES.filter(
            (t) => _.get(r, "MEASUREMENTDEVICE" + t.columnSuffix) != null,
          ).map((t) => {
            return {
              rmsccMeasurementSerialNumber: r.MEASUREMENTSERIALNUMBER,
              rmsccMeasurementOperator: r.MEASUREMENTOPERATOR,
              rmsccMeasurementOperatorType:
                preset.measurements.rmsccMeasurementOperatorType,
              rmsccMeasurementDateTime: r.MEASUREMENTDATETIME + timeZoneOffset,
              rmsccMeasurementType: t.code,
              rmsccMeasurementAssigned: String(
                _.get(r, "MEASUREMENTASSIGNED" + t.columnSuffix) == null
                  ? NO_VALUE_RECORD
                  : _.get(r, "MEASUREMENTASSIGNED" + t.columnSuffix),
              ),
              rmsccMeasurementDevice: String(
                _.get(r, "MEASUREMENTDEVICE" + t.columnSuffix) == null
                  ? NO_VALUE_RECORD
                  : _.get(r, "MEASUREMENTDEVICE" + t.columnSuffix),
              ),
              rmsccMeasurementHuman: String(
                _.get(r, "MEASUREMENTHUMAN" + t.columnSuffix) == null
                  ? NO_VALUE_RECORD
                  : _.get(r, "MEASUREMENTHUMAN" + t.columnSuffix),
              ),
              rmsccActionReason:
                _.get(r, "MEASUREMENTDEVICE" + t.columnSuffix) != null &&
                _.get(r, "MEASUREMENTHUMAN" + t.columnSuffix) != null
                  ? "Override"
                  : null,
              rmsccSoftwareVersion: preset.measurements.rmsccSoftwareVersion,
            };
          }),
        );
        return {
          rmsccEquipmentBrand: preset.device.rmsccEquipmentBrand,
          rmsccEquipmentModel: preset.device.rmsccEquipmentModel,
          rmsccEquipmentSerialNumber: deviceRows[0].EQUIPMENTSERIALNUMBER,
          rmsccHardwareVersion: preset.device.rmsccHardwareVersion,
          rmsccFirmwareVersion: preset.device.rmsccFirmwareVersion,
          rmsccEquipmentSerialIncrement:
            preset.device.rmsccEquipmentSerialIncrement,
          rmsccMeasurementsData: measurementsData,
        };
      });
      return {
        ausmeatBodyNumber: carcaseRows[0].BODYNUMBER,
        rmsccDeviceData: deviceData,
      };
    });
    return {
      rmsccMessageType: {
        rmsccPayloadName: preset.rmsccMessageType.rmsccPayloadName,
        rmsccVersionNumber: preset.rmsccMessageType.rmsccVersionNumber,
        rmsccAuthority: preset.rmsccMessageType.rmsccAuthority,
        rmsccIndustryDataStandard:
          preset.rmsccMessageType.rmsccIndustryDataStandard,
      },
      rmsccBeginningOfMessage: {
        rmsccMessageGUID: randomUUID(),
        rmsccMessageVersionIndicator:
          preset.rmsccBeginningOfMessage.rmsccMessageVersionIndicator,
        rmsccMessageFunction:
          preset.rmsccBeginningOfMessage.rmsccMessageFunction,
        rmsccKillDate: messageRows[0].KILLDATE,
        ausmeatEstablishmentNumber: userConfig.establishmentNumber,
        iscChainNumber: userConfig.chainNumber,
        ausmeatSpecies: preset.rmsccBeginningOfMessage.ausmeatSpecies,
        ausmeatKillLot: messageRows[0].KILLLOT,
        ausmeatLotCount: carcaseData.length,
      },
      rmsccCarcaseData: carcaseData,
    };
  });
  return { messages };
}

function getTimeZoneOffset(userConfig: IAusMeatOmmSettings): string {
  if (userConfig.timeZoneName === undefined) {
    return "";
  }
  return moment().tz(userConfig.timeZoneName).format("Z");
}
