import { Response } from "express";
import { z } from "zod";

import { AuthenticatedRequest } from "../Common";
import { getReqBodyIfSuperAdmin } from "./shared";
import { OmmMessagePreset } from "../../models/AusMeatOmm";

const bodySchema = z.object({
  rmsccMessageType: z.object({
    rmsccPayloadName: z.string().min(1),
    rmsccVersionNumber: z.string().min(1),
    rmsccAuthority: z.string().min(1),
    rmsccIndustryDataStandard: z.string().min(1),
  }),
  rmsccBeginningOfMessage: z.object({
    rmsccMessageFunction: z.string().min(1),
    ausmeatSpecies: z.string().min(1),
  }),
  device: z.object({
    rmsccEquipmentBrand: z.string().min(1),
    rmsccEquipmentModel: z.string().min(1),
    rmsccHardwareVersion: z.string().min(1),
    rmsccFirmwareVersion: z.string().min(1),
  }),
  measurements: z.object({
    rmsccSoftwareVersion: z.string().min(1),
  }),
});

export async function setOmmFilePreset(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = getReqBodyIfSuperAdmin(req, bodySchema);
  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  const result = await OmmMessagePreset.updateOne(undefined, reqBody, {
    upsert: true,
  });

  res.json({
    success:
      result.upsertedCount === 1 ||
      result.modifiedCount === 1 ||
      result.matchedCount === 1,
  });
}
