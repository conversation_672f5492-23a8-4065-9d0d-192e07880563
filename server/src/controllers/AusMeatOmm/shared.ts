import { z } from "zod";
import _ from "lodash";
import { StatusCodes } from "http-status-codes";

import { ALL_LOCATIONS, ALL_ORGS, SUPER_ADMIN } from "../../utils/Constants";
import { getRoleComponents } from "../../utils/Helper";
import { PkgName } from "../../utils/Types";
import { AuthenticatedRequest } from "../Common";
import {
  IOmmFile,
  IOmmFileRecord,
  OmmFile,
  OmmFileRecord,
} from "../../models/AusMeatOmm";
import { isPackageUser, getReqBodyIfOrgLocationPermitted } from "../shared";

export async function isAusMeatOmmUser(
  req: AuthenticatedRequest,
): Promise<boolean> {
  return isPackageUser(req, PkgName.AUS_MEAT_OMM);
}

function isSuperAdmin(req: AuthenticatedRequest) {
  if (req.user?.roles == null) {
    return false;
  }
  const roles = req.user.roles as string[];
  return roles.some((role) => role === SUPER_ADMIN);
}

export function isAuthBodyPermitted(
  authBody: z.infer<typeof authBodySchema>,
  req: AuthenticatedRequest,
): boolean {
  if (!_.isArray(req.user?.roles)) {
    throw new Error("Invalid user roles.");
  }

  const roles = req.user.roles as string[];
  if (roles.some((role) => role === SUPER_ADMIN)) {
    return true;
  }

  return roles
    .map((role) => getRoleComponents(role))
    .some((roleComponents) => {
      if (!roleComponents) {
        return false;
      }

      if (
        roleComponents.org !== ALL_ORGS &&
        roleComponents.org.toLowerCase() !== authBody.org
      ) {
        return false;
      }

      return (
        roleComponents.location === ALL_LOCATIONS ||
        roleComponents.location?.toLowerCase() === authBody.location
      );
    });
}

export const authBodySchema = z.object({
  org: z.string().min(1, "org should not be empty").toLowerCase(),
  location: z.string().min(1, "location should not be empty").toLowerCase(),
});

export function getReqBodyIfSuperAdmin<T extends z.AnyZodObject>(
  req: AuthenticatedRequest,
  bodySchema: T,
): {
  reqBody?: z.infer<typeof bodySchema>;
  error?: { statusCode: number; message?: string };
} {
  if (!isSuperAdmin(req)) {
    return { error: { statusCode: StatusCodes.FORBIDDEN } };
  }

  const { success, data: reqBody, error } = bodySchema.safeParse(req.body);

  if (!success) {
    return {
      error: {
        statusCode: StatusCodes.BAD_REQUEST,
        message: error.message,
      },
    };
  }

  return { reqBody };
}

export async function getReqBodyIfPermitted<T extends typeof authBodySchema>(
  req: AuthenticatedRequest,
  bodySchema: T,
): Promise<{
  reqBody?: z.infer<typeof bodySchema>;
  error?: { statusCode: number; message?: string };
}> {
  return getReqBodyIfOrgLocationPermitted(
    req,
    PkgName.AUS_MEAT_OMM,
    bodySchema,
  );
}

export const MAX_PAGE_SIZE = 100;

export const findOmmFileBodySchema = authBodySchema.extend({
  id: z.string().min(1),
});

export async function findOmmFile<
  T extends z.infer<typeof findOmmFileBodySchema>,
>(
  reqBody: T,
): Promise<{
  ommFile?: IOmmFile;
  ommFileRecord?: IOmmFileRecord;
  error?: { statusCode: number };
}> {
  const ommFileRecord = await OmmFileRecord.findOne({
    "ommFilesGenerated.ommFileId": reqBody.id,
  });

  if (!ommFileRecord) {
    return {
      error: {
        statusCode: StatusCodes.NOT_FOUND,
      },
    };
  }

  if (
    ommFileRecord.org !== reqBody.org ||
    ommFileRecord.location !== reqBody.location
  ) {
    return {
      error: {
        statusCode: StatusCodes.FORBIDDEN,
      },
    };
  }

  const ommFile = await OmmFile.findOne({
    _id: reqBody.id,
  });

  if (!ommFile) {
    return {
      error: {
        statusCode: StatusCodes.NOT_FOUND,
      },
    };
  }

  return { ommFile, ommFileRecord };
}
