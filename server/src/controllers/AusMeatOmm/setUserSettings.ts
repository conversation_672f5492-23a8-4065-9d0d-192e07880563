import { Response } from "express";
import { z } from "zod";

import { AuthenticatedRequest } from "../Common";
import { authBodySchema, getReqBodyIfPermitted } from "./shared";
import { UserSettings } from "../../models/UserSettings";

const bodySchema = authBodySchema.extend({
  apiKey: z.string().min(1, "API Key must not be empty."),
  establishmentNumber: z.string().min(1, "Establish Number must not be empty."),
  chainNumber: z.number().int("Chain Number must be an integer."),
});

export async function setUserSettings(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfPermitted(req, bodySchema);

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  const result = await UserSettings.updateOne(
    {
      org: reqBody!.org,
      location: reqBody!.location,
    },
    {
      $set: {
        "ausMeatOmm.apiKey": reqBody!.apiKey,
        "ausMeatOmm.establishmentNumber": reqBody!.establishmentNumber,
        "ausMeatOmm.chainNumber": reqBody!.chainNumber,
      },
    },
  );

  res.json({
    success: result.modifiedCount === 1 || result.matchedCount === 1,
  });
}
