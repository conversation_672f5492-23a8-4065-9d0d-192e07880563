import { Response } from "express";
import axios, { AxiosError } from "axios";

import { AuthenticatedRequest } from "../Common";
import {
  findOmmFile,
  getReqBodyIfPermitted,
  findOmmFileBodySchema,
} from "./shared";
import { AUS_MEAT_OMM_API_URL } from "../../utils/EnvConfig";
import { UserSettings } from "../../models/UserSettings";
import { OmmApiLogItem, OmmFileRecord } from "../../models/AusMeatOmm";

export async function sendOmmFile(req: AuthenticatedRequest, res: Response) {
  const { reqBody, error } = await getReqBodyIfPermitted(
    req,
    findOmmFileBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  const {
    ommFile,
    ommFileRecord,
    error: findOmmFileError,
  } = await findOmmFile(reqBody!);

  if (findOmmFileError) {
    res.status(findOmmFileError.statusCode).send();
    return;
  }

  const userSettings = await UserSettings.findOne({
    org: reqBody!.org,
    location: reqBody!.location,
    ausMeatOmm: { $ne: null },
  });

  if (!userSettings) {
    throw new Error(
      `AUS-MEAT OMM settings not found for ${reqBody!.org}-${reqBody!.location}.`,
    );
  }

  try {
    const responses = await Promise.all(
      ommFile!.messages.map(async (message, i) => {
        try {
          const r = await axios.post(
            `${AUS_MEAT_OMM_API_URL}/v1/members/upload`,
            message,
            {
              headers: {
                Authorization: userSettings.ausMeatOmm?.apiKey,
              },
            },
          );

          return r;
        } catch (err) {
          const errResponse = (err as AxiosError).response;
          if (errResponse) {
            await new OmmApiLogItem({
              timestamp: new Date(),
              org: reqBody!.org,
              location: reqBody!.location,
              fileDate: ommFileRecord!.fileDate,
              ommFileId: reqBody!.id,
              ommMessageIndex: i,
              response: errResponse,
            }).save();
          }
          throw err;
        }
      }),
    );

    if (responses.every((r) => r.status === 200)) {
      await OmmFileRecord.updateOne(
        { "ommFilesGenerated.ommFileId": reqBody!.id },
        {
          $set: {
            ommFileIdSent: reqBody!.id,
          },
        },
      );
    }

    res.json({
      success: true,
    });
  } catch (err) {
    console.error(err);
    res.json({
      success: false,
    });
  }
}
