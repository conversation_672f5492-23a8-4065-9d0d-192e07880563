import { Response } from "express";

import { AuthenticatedRequest } from "../Common";
import { authBodySchema, getReqBodyIfPermitted } from "./shared";
import { UserSettings } from "../../models/UserSettings";
import { StatusCodes } from "http-status-codes";

export async function queryOmmUserSettings(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfPermitted(req, authBodySchema);

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  const userSettings = await UserSettings.findOne({
    org: reqBody!.org,
    location: reqBody!.location,
    ausMeatOmm: { $ne: null },
  });

  if (!userSettings) {
    res.status(StatusCodes.NOT_FOUND).send();
    return;
  }

  res.json(userSettings.toObject({ versionKey: false }).ausMeatOmm);
}
