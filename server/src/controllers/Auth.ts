import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { Message, ResultCode, ServerResponse } from "../utils/Types";
import * as Models from "../models";
import { ORG_JSON_PROPERTIES } from "../utils/Constants";
import { sanitiseParams } from "../utils/Helper";

export const getZitadelOrg: RequestHandler = async (req, res) => {
  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  let { idString } = sanitiseParams(req.body);

  if (idString) {
    const zitadelOrg = await Models.ZitadelOrg.findOne({ idString });

    if (zitadelOrg) {
      result.data = JSON.stringify(zitadelOrg, ORG_JSON_PROPERTIES);
      result.msg = Message.SUCCESS;
      result.code = ResultCode.SUCCESS;
    } else {
      result.msg = (Message.ERROR_ORG_NOT_FOUND +
        ". Please make sure your web url is correct.") as Message;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};
