import { StatusCodes } from "http-status-codes";
import { AuthenticatedRequest } from "../Common";
import { z } from "zod";
import { findDb, isMeqLiveUser } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { Response } from "express";

const reqQuerySchema = z.object({
  orgIdString: z.string(),
});

const locationRowSchema = z.object({
  LOCATION: z.string(),
});

export async function queryOrgDeviceLocations(
  req: AuthenticatedRequest,
  res: Response,
) {
  if (!isMeqLiveUser(req)) {
    res.status(StatusCodes.FORBIDDEN).send();
    return;
  }
  // TODO check org is valid for user

  const { success, data: filters, error } = reqQuerySchema.safeParse(req.query);

  if (!success) {
    res.status(StatusCodes.BAD_REQUEST).send(error.message);
    return;
  }

  const db = findDb();
  let { orgIdString } = filters;
  if (orgIdString === "MEQ") {
    orgIdString = "test";
  }

  const { rows } = await executeSnowflakeStatement({
    sqlText: `
      SELECT
        DISTINCT LOCATION
      FROM ${db}.meqinsights.SCAN_REVIEW_PORTAL
      WHERE CUSTOMER_ID ILIKE :1
      ORDER BY LOCATION
    `,
    binds: [orgIdString],
    db,
  });

  const validatedRows = z.array(locationRowSchema).safeParse(rows);
  if (!validatedRows.success) {
    res
      .status(StatusCodes.INTERNAL_SERVER_ERROR)
      .send("Invalid data format received from database");
    return;
  }

  res.json(validatedRows.data.map((row) => row.LOCATION));
}
