import { StatusCodes } from "http-status-codes";
import { AuthenticatedRequest } from "../Common";
import { z } from "zod";
import { findDb, isMeqLiveUser } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { Response } from "express";

const reqQuerySchema = z.object({
  location: z.string(),
  startDate: z.string().date(),
  endDate: z.string().date(),
  deviceId: z.string().default("All"),
});

export async function querySummaryOfScans(
  req: AuthenticatedRequest,
  res: Response,
) {
  if (!isMeqLiveUser(req)) {
    res.status(StatusCodes.FORBIDDEN).send();
    return;
  }

  const { success, data: filters, error } = reqQuerySchema.safeParse(req.query);

  if (!success) {
    res.status(StatusCodes.BAD_REQUEST).send(error.message);
    return;
  }

  const db = findDb();

  const { rows, columns } = await executeSnowflakeStatement({
    sqlText: `
      WITH ranked_records AS (
        SELECT
          SCAN_DATE,
          OIL_APPLIED,
          AVG_OIL_TEMP,
          ROW_NUMBER() OVER (PARTITION BY ANIMAL_ID, SCAN_DATE ORDER BY s.SCAN_DATETIME ASC) AS record_rank,
          CASE
            WHEN TIMESTAMPDIFF(SECOND,
              LAG(s.SCAN_DATETIME) OVER (PARTITION BY s.LOCATION, SCAN_DATE ORDER BY s.SCAN_DATETIME),
              s.SCAN_DATETIME
            ) > 300 THEN null
            ELSE TIMESTAMPDIFF(SECOND,
              LAG(s.SCAN_DATETIME) OVER (PARTITION BY s.LOCATION, SCAN_DATE ORDER BY s.SCAN_DATETIME),
              s.SCAN_DATETIME
            )
          END AS time_diff_seconds,
        FROM ${db}.meqinsights.SCAN_REVIEW_PORTAL s
        WHERE s.LOCATION = :1 AND SCAN_DATE BETWEEN :2 AND :3
          AND iff(:4 = 'All', true, DEVICE_ID = :4)
      )

      SELECT
        SCAN_DATE,
        COUNT(*) AS total_scans,
        SUM(CASE WHEN record_rank > 1 THEN 1 ELSE 0 END) AS total_rescans,
        MIN(CASE WHEN OIL_APPLIED > 0 THEN OIL_APPLIED ELSE NULL END) AS min_oil_applied,
        MAX(OIL_APPLIED) AS max_oil_applied,
        AVG(OIL_APPLIED) AS avg_oil_applied,
        AVG(AVG_OIL_TEMP) AS avg_oil_temp,
        AVG(time_diff_seconds) AS avg_time_between_records,
      FROM ranked_records
      GROUP BY SCAN_DATE
      ORDER BY SCAN_DATE DESC;
    `,
    binds: [
      filters.location,
      filters.startDate,
      filters.endDate,
      filters.deviceId,
    ],
    db,
  });

  res.json({ rows, columns });
}
