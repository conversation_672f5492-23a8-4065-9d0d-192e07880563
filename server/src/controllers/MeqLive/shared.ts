import _ from "lodash";
import { AuthenticatedRequest } from "../Common";
import { getOrgFromRole } from "../../utils/Helper";
import { ALL_ORGS, MEQ_ORG, SUPER_ADMIN } from "../../utils/Constants";
import { PackageDatabases } from "../../utils/SnowflakeDatabases";
import { PkgName } from "../../utils/Types";

export function isMeqLiveUser(req: AuthenticatedRequest): boolean {
  const roles = _.get(req, ["user", "roles"], []) as string[];
  return roles.some(
    (role: string) =>
      [MEQ_ORG, ALL_ORGS].includes(getOrgFromRole(role)) ||
      role === SUPER_ADMIN,
  );
}

export function isDunbiaUser(req: AuthenticatedRequest): boolean {
  const roles = _.get(req, ["user", "roles"], []) as string[];
  return roles.some(
    (role: string) =>
      getOrgFromRole(role) === "Dunbia" ||
      [ALL_ORGS].includes(getOrgFromRole(role)) ||
      role === SUPER_ADMIN,
  );
}

export function findDb(): string {
  const db = PackageDatabases.find(
    (d) => d.id === PkgName.MEQ_LIVE.toString(),
  )?.database;
  if (db === undefined) {
    throw new Error(`Database not found for ${PkgName.MEQ_LIVE}`);
  }
  return db;
}
