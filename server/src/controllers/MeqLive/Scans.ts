import { StatusCodes } from "http-status-codes";
import { AuthenticatedRequest } from "../Common";
import { z } from "zod";
import { findDb, isMeqLiveUser, isDunbiaUser } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { findSnowflakeClientDb } from "../../utils/SnowflakeDatabases";
import { Response } from "express";

const reqQuerySchema = z.object({
  location: z.string().min(1, "location should not be empty"),
  date: z.string().date("date should be in the format YYYY-MM-DD"),
  deviceId: z.string().default("All"),
});

export async function queryScans(req: AuthenticatedRequest, res: Response) {
  if (!isMeqLiveUser(req)) {
    res.status(StatusCodes.FORBIDDEN).send();
    return;
  }

  const { success, data: filters, error } = reqQuerySchema.safeParse(req.query);

  if (!success) {
    res.status(StatusCodes.BAD_REQUEST).send(error.message);
    return;
  }

  const db = findDb();
  const shouldIncludeDunbiaData =
    isDunbiaUser(req) && filters.location.toLowerCase() === "cardington";
  const dunbiaDb = shouldIncludeDunbiaData
    ? findSnowflakeClientDb("Dunbia")
    : "";

  const { rows, columns } = await executeSnowflakeStatement({
    sqlText: `
      SELECT s.*,
        m.EMA_DISPLAYED AS EMA,
        m.AUS_MB_DISPLAYED AS AUS_MARBLING,
        m.MSA_MB_DISPLAYED AS MSA_MARBLING,
        m.USDA_MB_DISPLAYED AS USDA_MARBLING,
        ${shouldIncludeDunbiaData ? "d.EAR_TAG, d.PATHWAY," : ""}
      FROM ${db}.meqinsights.scan_review_portal s
      LEFT OUTER JOIN ${db}.MEQINSIGHTS.MEQLIVE_INSIGHTS_DEVICE_PREDICTIONS m
        ON s.LOCATION = m.LOCATION AND s.ANIMAL_ID = m.RFID
        AND TO_VARCHAR(m.SCAN_DATETIME, 'YYYY-MM-DD HH24:MI:SS') = TO_VARCHAR(s.SCAN_DATETIME, 'YYYY-MM-DD HH24:MI:SS')
      ${
        shouldIncludeDunbiaData
          ? `LEFT OUTER JOIN ${dunbiaDb}.MEQINSIGHTS.DUNBIA_INSIGHTS_PLANT_SIDE_CARCASE_DATA d ON s.ANIMAL_ID = RIGHT(d.EAR_TAG, 6) AND s.SCAN_DATE = DATE(d.KILL_DATE)`
          : ""
      }
      WHERE s.LOCATION = :1 AND SCAN_DATE = :2
        AND iff(:3 = 'All', true, DEVICE_ID = :3)
    `,
    binds: [filters.location, filters.date, filters.deviceId],
    db,
  });

  res.json({ rows, columns });
}
