import { StatusCodes } from "http-status-codes";
import { AuthenticatedRequest } from "../Common";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { findDb, isMeqLiveUser } from "./shared";
import { Response } from "express";

export async function getScanFilters(req: AuthenticatedRequest, res: Response) {
  if (!isMeqLiveUser(req)) {
    res.status(StatusCodes.FORBIDDEN).send();
    return;
  }

  const db = findDb();

  const { rows, columns } = await executeSnowflakeStatement({
    sqlText: `SELECT DISTINCT location, scan_date FROM ${db}.meqinsights.scan_review_portal`,
    db,
  });

  res.json({ rows, columns });
}
