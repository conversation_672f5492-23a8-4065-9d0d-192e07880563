import { StatusCodes } from "http-status-codes";
import { AuthenticatedRequest } from "../Common";
import { z } from "zod";
import { findDb, isMeqLiveUser } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { Response } from "express";

const reqQuerySchema = z.object({
  location: z.string(),
  scanDate: z.string().date(),
});

export async function queryUserReport(
  req: AuthenticatedRequest,
  res: Response,
) {
  if (!isMeqLiveUser(req)) {
    res.status(StatusCodes.FORBIDDEN).send();
    return;
  }

  const { success, data: filters, error } = reqQuerySchema.safeParse(req.query);

  if (!success) {
    res.status(StatusCodes.BAD_REQUEST).send(error.message);
  }

  const db = findDb();

  const { rows, columns } = await executeSnowflakeStatement({
    sqlText: `
      WITH ranked_records AS (
        SELECT
          ANIMAL_ID,
          OIL_APPLIED,
          AVG_OIL_TEMP,
          "USER",
          ROW_NUMBER() OVER (PARTITION BY ANIMAL_ID ORDER BY SCAN_DATETIME ASC) AS record_rank,
          CASE
              WHEN TIMESTAMPDIFF(SECOND,
                LAG(SCAN_DATETIME) OVER (PARTITION BY location, SCAN_DATE ORDER BY SCAN_DATETIME),
                SCAN_DATETIME
              ) > 300 THEN null
              ELSE TIMESTAMPDIFF(SECOND,
                LAG(SCAN_DATETIME) OVER (PARTITION BY location, SCAN_DATE ORDER BY SCAN_DATETIME),
                SCAN_DATETIME
              )
            END AS time_diff_seconds
        FROM ${db}.meqinsights.SCAN_REVIEW_PORTAL
        WHERE location = :1
        AND SCAN_DATE = :2
      ),

      all_time_scans AS (
        SELECT
          "USER",
          COUNT(*) AS user_all_time_scans
        FROM ${db}.meqinsights.SCAN_REVIEW_PORTAL
        WHERE LOCATION = :1
        GROUP BY "USER"
      )

      SELECT
        r."USER",
        COUNT(*) AS total_scans,
        SUM(CASE WHEN record_rank > 1 THEN 1 ELSE 0 END) AS total_rescans,
        MIN(CASE WHEN OIL_APPLIED > 0 THEN OIL_APPLIED ELSE NULL END) AS min_oil_applied,
        MAX(OIL_APPLIED) AS max_oil_applied,
        AVG(OIL_APPLIED) AS avg_oil_applied,
        AVG(time_diff_seconds) AS avg_time_between_records,
        AVG(AVG_OIL_TEMP) AS avg_oil_temp,
        a.user_all_time_scans AS total_scans_all_time
      FROM ranked_records r
      JOIN all_time_scans a ON r."USER" = a."USER"
      GROUP BY r."USER", a.user_all_time_scans
      ORDER BY total_scans DESC;
    `,
    binds: [filters!.location, filters!.scanDate],
    db,
  });

  res.json({ rows, columns });
}
