import { StatusCodes } from "http-status-codes";
import { AuthenticatedRequest } from "../Common";
import { z } from "zod";
import { findDb, isMeqLiveUser } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { Response } from "express";
import { ALL_LOCATIONS, getOrgIdStringComponents } from "../../utils/Helper";

const reqQuerySchema = z.object({
  orgIdString: z.string(),
});

const locationDeviceRowSchema = z.object({
  DEVICE_ID: z.string(),
  LOCATION: z.string(),
});

export async function queryOrgLocationDevices(
  req: AuthenticatedRequest,
  res: Response,
) {
  if (!isMeqLiveUser(req)) {
    res.status(StatusCodes.FORBIDDEN).send();
    return;
  }

  const { success, data: filters, error } = reqQuerySchema.safeParse(req.query);

  if (!success) {
    res.status(StatusCodes.BAD_REQUEST).send(error.message);
    return;
  }

  // TODO check org is valid for user
  // hasAccessToOrg(req.user, filters.orgIdString);

  const db = findDb();
  const { orgIdString } = filters;
  let { org, location } = getOrgIdStringComponents(orgIdString);
  if (orgIdString === "MEQ") {
    location = "factory";
    org = "test";
  }

  const { rows } = await executeSnowflakeStatement({
    sqlText: `
      SELECT LOCATION, DEVICE_ID
      FROM ${db}.meqinsights.SCAN_REVIEW_PORTAL
      WHERE CUSTOMER_ID ILIKE :1 AND IFF(:2 = '${ALL_LOCATIONS}', true, LOCATION ILIKE :2)
      GROUP BY LOCATION, DEVICE_ID
      ORDER BY LOCATION, DEVICE_ID
    `,
    binds: [org, location],
    db,
  });

  const validatedRows = z.array(locationDeviceRowSchema).safeParse(rows);
  if (!validatedRows.success) {
    res
      .status(StatusCodes.INTERNAL_SERVER_ERROR)
      .send("Invalid data format received from database");
    return;
  }

  res.json(validatedRows.data);
}
