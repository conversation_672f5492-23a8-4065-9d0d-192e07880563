import { StatusCodes } from "http-status-codes";
import { AuthenticatedRequest } from "../Common";
import { z } from "zod";
import { findDb, isMeqLiveUser } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { Response } from "express";

const reqBodySchema = z.object({
  startDate: z.string().date(),
});

export async function queryDeviceUsage(
  req: AuthenticatedRequest,
  res: Response,
) {
  if (!isMeqLiveUser(req)) {
    res.status(StatusCodes.FORBIDDEN).send();
    return;
  }

  const { success, data: filters, error } = reqBodySchema.safeParse(req.body);

  if (!success) {
    res.status(StatusCodes.BAD_REQUEST).send(error.message);
    return;
  }

  const db = findDb();

  const { rows, columns } = await executeSnowflakeStatement({
    sqlText: `
      WITH data AS (
        SELECT
          device_id,
          TRIM(location) as location,
          SCAN_DATE,
          CASE
            WHEN TIMESTAMPDIFF(SECOND,
              LAG(SCAN_DATETIME) OVER (PARTITION BY location, SCAN_DATE ORDER BY SCAN_DATETIME),
              SCAN_DATETIME
            ) > 300 THEN null
            ELSE TIMESTAMPDIFF(SECOND,
              LAG(SCAN_DATETIME) OVER (PARTITION BY location, SCAN_DATE ORDER BY SCAN_DATETIME),
              SCAN_DATETIME
            )
          END AS time_diff_seconds
        FROM ${db}.meqinsights.SCAN_REVIEW_PORTAL
        WHERE SCAN_DATE >= :1
      )
      SELECT device_id, location, SCAN_DATE,
        COUNT(SCAN_DATE) as SCAN_COUNT,
        AVG(SCAN_COUNT)
          OVER(PARTITION BY location ORDER BY SCAN_DATE RANGE BETWEEN INTERVAL '3 months' PRECEDING AND CURRENT ROW)
            AS "MOVING_AVERAGE",
        AVG(time_diff_seconds) AS AVG_SECONDS_DIFF
      FROM data
      GROUP BY device_id, location, SCAN_DATE
      ORDER BY location, device_id, SCAN_DATE
    `,
    binds: [filters.startDate],
    db,
  });

  res.json({ rows, columns });
}
