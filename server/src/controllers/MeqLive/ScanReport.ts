import { StatusCodes } from "http-status-codes";
import { AuthenticatedRequest } from "../Common";
import { z } from "zod";
import { findDb, isMeqLiveUser } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { Response } from "express";

const reqQuerySchema = z.object({
  location: z.string(),
  scanDate: z.string().date(),
});

export async function queryScanReport(
  req: AuthenticatedRequest,
  res: Response,
) {
  if (!isMeqLiveUser(req)) {
    res.status(StatusCodes.FORBIDDEN).send();
    return;
  }

  const { success, data: filters, error } = reqQuerySchema.safeParse(req.query);

  if (!success) {
    res.status(StatusCodes.BAD_REQUEST).send(error.message);
  }

  const db = findDb();

  const { rows, columns } = await executeSnowflakeStatement({
    sqlText: `
      WITH ranked_records AS (
        SELECT
        scan_date,
        location,
          ANIMAL_ID,
          OIL_APPLIED,
          AVG_OIL_TEMP,
          ROW_NUMBER() OVER (PARTITION BY ANIMAL_ID ORDER BY SCAN_DATETIME ASC) AS record_rank,
          CASE
          WHEN TIMESTAMPDIFF(SECOND,
            LAG(SCAN_DATETIME) OVER (PARTITION BY location, SCAN_DATE ORDER BY SCAN_DATETIME),
            SCAN_DATETIME
          ) > 300 THEN null
          ELSE TIMESTAMPDIFF(SECOND,
            LAG(SCAN_DATETIME) OVER (PARTITION BY location, SCAN_DATE ORDER BY SCAN_DATETIME),
            SCAN_DATETIME
          )
        END AS time_diff_seconds
        FROM ${db}.meqinsights.SCAN_REVIEW_PORTAL
      ),

      daily_scans AS (
        SELECT
          DATE_TRUNC('DAY', SCAN_DATETIME) AS scan_date,
          COUNT(*) AS scans_in_day
        FROM ${db}.meqinsights.SCAN_REVIEW_PORTAL
        WHERE location = :2
        GROUP BY DATE_TRUNC('DAY', SCAN_DATETIME)
      ),

      three_month_window AS (
        SELECT
          :1 AS today,
          DATEADD('MONTH', -3, :1) AS three_months_ago
      ),

      moving_avg AS (
        SELECT
          AVG(scans_in_day) AS three_month_moving_avg
        FROM daily_scans, three_month_window
        WHERE scan_date >= three_months_ago
          AND scan_date <= today
      )

      SELECT
        COUNT(*) AS total_scans,
        SUM(CASE WHEN record_rank > 1 THEN 1 ELSE 0 END) AS total_rescans,
        COUNT(DISTINCT ANIMAL_ID) AS unique_animals,
        MIN(CASE WHEN OIL_APPLIED > 0 THEN OIL_APPLIED ELSE NULL END) AS min_oil_applied,
        MAX(OIL_APPLIED) AS max_oil_applied,
        AVG(CASE WHEN OIL_APPLIED > 0 THEN OIL_APPLIED ELSE NULL END) AS avg_oil_applied,
        AVG(CASE WHEN AVG_OIL_TEMP > 0 THEN AVG_OIL_TEMP ELSE NULL END) AS avg_oil_temp,
        AVG(time_diff_seconds) AS avg_time_between_records,
        (SELECT three_month_moving_avg FROM moving_avg) AS three_month_moving_avg
      FROM ranked_records
      WHERE scan_date = :1 AND location = :2
      GROUP BY location, scan_date
    `,
    binds: [filters!.scanDate, filters!.location],
    db,
  });

  res.json({ rows, columns });
}
