import { Response } from "express";
import { z } from "zod";

import { AuthenticatedRequest } from "../Common";
import { PkgName } from "../../utils/Types";
import {
  getReqBodyIfOrgLocationPermitted,
  orgLocationBodySchema,
} from "../shared";
import { DatasetTaggingEventModel } from "../../models/DatasetTagging";

const reqBodySchema = orgLocationBodySchema.extend({
  query: z.object({
    dataset: z.literal("camera"),
    origins: z.array(
      z.object({
        companyLocation: z.string().optional(),
        meatType: z.string().optional(),
        barcode: z.string().optional(),
        photoDateTime: z.string().optional(),
      }),
    ),
  }),
});

export async function queryTags(req: AuthenticatedRequest, res: Response) {
  const { reqBody, error } = await getReqBodyIfOrgLocationPermitted(
    req,
    PkgName.DATASET_TAGGING,
    reqBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  const results = await Promise.all(
    reqBody!.query.origins.map(async (origin) => {
      const events = await DatasetTaggingEventModel.find({
        "originId.companyLocation": origin.companyLocation,
        "originId.meatType": origin.meatType,
        "originId.barcode": origin.barcode,
        "originId.photoDateTime": origin.photoDateTime,
      }).sort({ eventTime: "asc" });

      const tags: Record<string, unknown> = {};

      events.forEach((event) => {
        if (event.eventAction === "create") {
          tags[event.tagKey] = event.tagValue;
        } else if (event.eventAction === "delete") {
          delete tags[event.tagKey];
        }
      });

      return tags;
    }),
  );

  res.json(results);
}
