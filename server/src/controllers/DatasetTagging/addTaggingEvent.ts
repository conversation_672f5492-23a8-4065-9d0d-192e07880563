import { Response } from "express";
import { z } from "zod";

import { AuthenticatedRequest } from "../Common";
import { PkgName } from "../../utils/Types";
import {
  getReqBodyIfOrgLocationPermitted,
  orgLocationBodySchema,
  parseReqBodyBySchema,
} from "../shared";
import { DatasetTaggingEventModel } from "../../models/DatasetTagging";
import { StatusCodes } from "http-status-codes";

const reqBodySchema = orgLocationBodySchema.extend({
  event: z.object({
    dataset: z.literal("camera"),
    originId: z.object({
      companyLocation: z.string().optional(),
      meatType: z.string().optional(),
      barcode: z.string().optional(),
      photoDateTime: z.string().optional(),
    }),
    datasetSchemaVersion: z.literal(1),
    eventSchemaVersion: z.literal(1),
    eventAction: z.enum(["create", "delete"]),
  }),
});

const createSchema = z.object({
  event: z.object({
    tagKey: z.string(),
    tagValue: z.union([z.string(), z.number(), z.boolean()]),
  }),
});

const deleteSchema = z.object({
  event: z.object({
    tagKey: z.string(),
  }),
});

export async function addTaggingEvent(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfOrgLocationPermitted(
    req,
    PkgName.DATASET_TAGGING,
    reqBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  let eventAction: ReturnType<
    typeof parseReqBodyBySchema<typeof createSchema | typeof deleteSchema>
  >;

  switch (reqBody!.event.eventAction) {
    case "create":
      eventAction = parseReqBodyBySchema(req, createSchema);
      break;
    case "delete":
      eventAction = parseReqBodyBySchema(req, deleteSchema);
      break;
  }

  if (eventAction.error) {
    res.status(eventAction.error.statusCode).send(eventAction.error.message);
    return;
  }

  try {
    await DatasetTaggingEventModel.create({
      ...reqBody!.event,
      ...eventAction.data?.event,
      eventTime: new Date(),
      eventAuthor: {
        id: req.user?.email,
        role: req.user?.roles?.length > 0 ? req.user!.roles[0] : undefined,
      },
    });
    res.json({ success: true });
  } catch (err) {
    console.error(err);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ success: false });
  }
}
