import { Response } from "express";

import { AuthenticatedRequest } from "../Common";
import { PkgName } from "../../utils/Types";
import {
  getReqBodyIfOrgLocationPermitted,
  orgLocationBodySchema,
} from "../shared";
import { UserSettings } from "../../models/UserSettings";

export async function queryUserSettings(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfOrgLocationPermitted(
    req,
    PkgName.DATASET_TAGGING,
    orgLocationBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  const userSettings = await UserSettings.findOne({
    org: reqBody!.org,
    location: reqBody!.location,
  });

  res.json(userSettings?.datasetTagging || {});
}
