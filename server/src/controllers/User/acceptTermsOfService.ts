import { Response } from "express";

import { AuthenticatedRequest } from "../Common";
import { User } from "../../models";
import { refreshZitadelUserCacheByUserId } from "../../utils/Cache";

export async function acceptTermsOfService(
  req: AuthenticatedRequest,
  res: Response,
) {
  const timestamp = new Date().toISOString();
  const result = await User.updateOne(
    {
      id: req.user?.id,
      email: req.user?.email,
      termsOfServiceAcceptanceTimestamp: { $exists: false },
    },
    {
      $set: {
        termsOfServiceAcceptanceTimestamp: timestamp,
      },
    },
  );

  const success = result.modifiedCount === 1;

  if (success) {
    await refreshZitadelUserCacheByUserId(req.user?.zitadelId);
  }

  res.json({
    success,
    ...(success ? { termsOfServiceAcceptanceTimestamp: timestamp } : {}),
  });
}
