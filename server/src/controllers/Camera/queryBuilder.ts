import { z } from "zod";
import { IUserSettings } from "../../models/UserSettings";
import { findDatabaseForPackage } from "../../utils/ClickHouse";
import { PkgName } from "../../utils/Types";
import { orgLocationBodySchema } from "../shared";
import { dateRangeBodySchema } from "./shared";
import { findPackageDb } from "../../utils/SnowflakeDatabases";

class ImageQuery {
  sql: string;
  params: Record<string, unknown>;

  constructor(sql: string, params: Record<string, unknown>) {
    this.sql = sql;
    this.params = params;
  }

  getVarDefsAndBindsSf() {
    const paramEntries = Object.entries(this.params);
    const varDefs = paramEntries.map(([name]) => `set ${name} = ?;`).join("\n");
    const binds = paramEntries.map(([, value]) => value);
    return { varDefs, binds };
  }
}

const imageQueryColsCh = [
  "kill_date",
  "body_number",
  "photo_datetime",
  "barcode",
  "msa_marbling",
  "override_msa_marbling",
  "aus_marbling",
  "override_aus_marbling",
  "usda_marbling",
  "imf",
  "imf_visual",
  "fineness",
  "fat_color",
  "override_fat_color",
  "meat_color",
  "override_meat_color",
  "eye_muscle_area",
  "eye_muscle_area_units",
  "override_eye_muscle_area",
  "rib_fat",
  "rib_fat_units",
  "override_rib_fat",
  "user",
  "az_url",
  "note",
  "meat_type",
  "company_location",
] as const;

export function hasImageSummaryExtSettings(
  userSettings?: IUserSettings["camera"],
) {
  return userSettings?.summary?.ext != null;
}

export function hasCrossOrgImageShareSettings(
  userSettings?: IUserSettings["camera"],
) {
  return (
    Array.isArray(userSettings?.crossOrgShare) &&
    userSettings?.crossOrgShare.length > 0
  );
}

export function buildIndividualImagesQueryCh(args: {
  reqBody: z.infer<typeof orgLocationBodySchema>;
  userSettings?: IUserSettings["camera"];
  isCrossOrgQueryEnabled: boolean;
  isImageSummaryExtQueryEnabled: boolean;
}): ImageQuery {
  const db = findDatabaseForPackage(PkgName.MEQ_CAMERA)!;

  let imageQuery = buildImageQueryCh({
    db,
    projectionCols: imageQueryColsCh,
    org: args.reqBody.org,
    location: args.reqBody.location,
  });

  if (args.isCrossOrgQueryEnabled) {
    imageQuery = buildCrossOrgImageQueryCh({
      userSettings: args.userSettings!.crossOrgShare,
      mainQuery: imageQuery,
    });
  }

  if (args.isImageSummaryExtQueryEnabled) {
    imageQuery = buildImageSummaryExtQueryCh({
      reqBody: args.reqBody,
      userSettings: args.userSettings!.summary.ext,
      imageQuery,
    });
  }

  return imageQuery;
}

function buildImageSummaryExtQueryCh(args: {
  reqBody: z.infer<typeof orgLocationBodySchema>;
  userSettings: NonNullable<IUserSettings["camera"]>["summary"]["ext"];
  imageQuery: {
    sql: string;
    params: Record<string, unknown>;
  };
}): ImageQuery {
  const db = findDatabaseForPackage(PkgName.MEQ_CAMERA_IMAGE_SUMMARY_EXT)!;

  const extCols = Array.from(
    new Set(
      args.userSettings.projectionCols.concat(args.userSettings.joiningCols),
    ),
  );

  const sql = `
    with images as (
      ${args.imageQuery.sql}
    ),
    ext as (
      select
        ${extCols.map((col) => `${col} as ${col.toUpperCase()}`).join(",")},
        '${args.reqBody.org}' || '-' || location as COMPANY_LOCATION
      from ${db}.${args.reqBody.org}_cam
    )
    select
      ${imageQueryColsCh.map((col) => col.toUpperCase()).join(",")},
      ${args.userSettings.projectionCols.map((col) => col.toUpperCase()).join(",")}
    from images
      left outer join ext
      using (COMPANY_LOCATION, ${args.userSettings.joiningCols.map((col) => col.toUpperCase()).join(",")})
  `;

  const params = Object.assign(
    {
      ext_location: args.reqBody.location,
    },
    args.imageQuery.params,
  );

  return new ImageQuery(sql, params);
}

function buildCrossOrgImageQueryCh(args: {
  userSettings: NonNullable<IUserSettings["camera"]>["crossOrgShare"];
  mainQuery: ImageQuery;
}): ImageQuery {
  const db = findDatabaseForPackage(PkgName.MEQ_CAMERA)!;

  const shareQueries: ImageQuery[] = args.userSettings.map((share, i) => {
    return buildImageQueryCh({
      db,
      projectionCols: imageQueryColsCh,
      org: share.org,
      location: share.location,
      note: share.note,
      paramSuffix: `_share_${i}`,
    });
  });

  const allQueries = [args.mainQuery, ...shareQueries];

  const combinedSql = allQueries
    .map((q) => q.sql)
    .join(
      `
        union all
      `,
    );

  const combinedParams = {};
  allQueries.forEach((q) => {
    Object.assign(combinedParams, q.params);
  });

  return new ImageQuery(combinedSql, combinedParams);
}

function buildImageQueryCh(args: {
  db: string;
  projectionCols: readonly string[];
  org: string;
  location: string;
  note?: string;
  paramSuffix?: string;
}): ImageQuery {
  const companyParamName = `company_${args.paramSuffix ?? ""}`;
  const locationParamName = `location_${args.paramSuffix ?? ""}`;
  const noteParamName =
    args.note && args.paramSuffix ? `note${args.paramSuffix}` : undefined;

  const params = {
    [companyParamName]: args.org,
    [locationParamName]: args.location,
    ...(noteParamName ? { [noteParamName]: args.note } : {}),
  };

  const sql = `
    select
      ${args.projectionCols.map((col) => `${col} as ${col.toUpperCase()}`).join(",")}
    from ${args.db}.individual_images_v2(
      start_date={start_date:String},
      end_date={end_date:String},
      company={${companyParamName}:String},
      location={${locationParamName}:String}
    )
    ${noteParamName ? `where trim(note) = trim({${noteParamName}:String})` : ""}
  `;

  return new ImageQuery(sql, params);
}

export function buildIndividualImagesQuerySf(args: {
  reqBody: z.infer<typeof reqBodySchema>;
  userSettings?: IUserSettings["camera"];
  isCrossOrgQueryEnabled: boolean;
  isImageSummaryExtQueryEnabled: boolean;
}): ImageQuery {
  const db = findPackageDb(PkgName.MEQ_CAMERA);

  let imageQuery = buildImageQuerySf({
    db,
    org: args.reqBody.org,
    location: args.reqBody.location,
  });

  if (args.isCrossOrgQueryEnabled) {
    imageQuery = buildCrossOrgImageQuerySf({
      userSettings: args.userSettings!.crossOrgShare,
      mainQuery: imageQuery,
    });
  }

  if (args.isImageSummaryExtQueryEnabled) {
    imageQuery = buildImageSummaryExtQuerySf({
      reqBody: args.reqBody,
      userSettings: args.userSettings!.summary.ext,
      imageQuery,
    });
  }

  const params = Object.assign(
    {
      start_date: args.reqBody.startDate,
      end_date: args.reqBody.endDate,
      meat_type: args.reqBody.meatType,
    },
    imageQuery.params,
  );

  return new ImageQuery(imageQuery.sql, params);
}

function buildImageSummaryExtQuerySf(args: {
  reqBody: z.infer<typeof orgLocationBodySchema>;
  userSettings: NonNullable<IUserSettings["camera"]>["summary"]["ext"];
  imageQuery: {
    sql: string;
    params: Record<string, unknown>;
  };
}): ImageQuery {
  const db = findPackageDb(PkgName.MEQ_CAMERA_IMAGE_SUMMARY_EXT);

  const sql = `
    with images as (
      ${args.imageQuery.sql}
    ),
    ext as (
      select *, '${args.reqBody.org}' || '-' || location as company_location
      from ${db}.camera_image_summary_ext.${args.reqBody.org}_cam
    )
    select
      i.*,
      ${args.userSettings.projectionCols.join(",")}
    from images as i
      left outer join ext as e
      using (company_location, ${args.userSettings.joiningCols.join(",")})
  `;

  const params = Object.assign(
    {
      ext_location: args.reqBody.location,
    },
    args.imageQuery.params,
  );

  return new ImageQuery(sql, params);
}

function buildCrossOrgImageQuerySf(args: {
  userSettings: NonNullable<IUserSettings["camera"]>["crossOrgShare"];
  mainQuery: ImageQuery;
}): ImageQuery {
  const db = findPackageDb(PkgName.MEQ_CAMERA);

  const shareQueries: ImageQuery[] = args.userSettings.map((share, i) => {
    return buildImageQuerySf({
      db,
      org: share.org,
      location: share.location,
      note: share.note,
      paramSuffix: `_share_${i}`,
    });
  });

  const allQueries = [args.mainQuery, ...shareQueries];

  const combinedSql = allQueries
    .map((q) => q.sql)
    .join(
      `
        union all
      `,
    );

  const combinedParams = {};
  allQueries.forEach((q) => {
    Object.assign(combinedParams, q.params);
  });

  return new ImageQuery(combinedSql, combinedParams);
}

function buildImageQuerySf(args: {
  db: string;
  org: string;
  location: string;
  note?: string;
  paramSuffix?: string;
}): ImageQuery {
  const companyParamName = `company_${args.paramSuffix ?? ""}`;
  const locationParamName = `location_${args.paramSuffix ?? ""}`;
  const noteParamName =
    args.note && args.paramSuffix ? `note${args.paramSuffix}` : undefined;

  const params = {
    [companyParamName]: args.org,
    [locationParamName]: args.location,
    ...(noteParamName ? { [noteParamName]: args.note } : {}),
  };

  const sql = `
    select *
    from table(
      ${args.db}.MEQINSIGHTS.MEQCAMERA_INDIVIDUAL_IMAGES(
        $start_date,
        $end_date,
        $${companyParamName},
        $${locationParamName},
        $meat_type
      )
    )
    ${noteParamName ? `where trim(note) = trim($${noteParamName})` : ""}
  `;

  return new ImageQuery(sql, params);
}
