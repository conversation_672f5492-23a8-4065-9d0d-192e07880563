import { Response } from "express";
import { z } from "zod";

import { AuthenticatedRequest } from "../Common";
import { PkgName } from "../../utils/Types";
import {
  COLUMNS_TO_RENAME_BY_ORG_COUNTRY,
  getReqBodyIfOrgLocationPermitted,
  isPackageUser,
} from "../shared";
import { dateRangeBodySchema, findOrg, findUserSettings } from "./shared";
import { findPackageDb } from "../../utils/SnowflakeDatabases";
import {
  buildIndividualImagesQueryCh,
  buildIndividualImagesQuerySf,
  hasCrossOrgImageShareSettings,
  hasImageSummaryExtSettings,
} from "./queryBuilder";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { getOptimisedColumns } from "../../utils/Helper";
import {
  chClient,
  isClickHouseEnabled,
  removeUnusedColumnsInQueryResult,
} from "../../utils/ClickHouse";
import { IUserSettings } from "../../models/UserSettings";

async function queryClickHouse(args: {
  reqBody: z.infer<typeof reqBodySchema>;
  userSettings?: IUserSettings["camera"];
  isCrossOrgQueryEnabled: boolean;
  isImageSummaryExtQueryEnabled: boolean;
}) {
  const { sql, params } = buildIndividualImagesQueryCh(args);

  return (
    await chClient().query({
      query: sql,
      format: "JSON",
      query_params: {
        start_date: args.reqBody.startDate,
        end_date: args.reqBody.endDate,
        ...params,
      },
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    })
  ).json();
}

async function querySnowflake(args: {
  reqBody: z.infer<typeof reqBodySchema>;
  userSettings?: IUserSettings["camera"];
  isCrossOrgQueryEnabled: boolean;
  isImageSummaryExtQueryEnabled: boolean;
}) {
  const db = findPackageDb(PkgName.MEQ_CAMERA);

  const imageQuery = buildIndividualImagesQuerySf(args);

  const { varDefs, binds } = imageQuery.getVarDefsAndBindsSf();

  const { rows, columns } = await executeSnowflakeStatement({
    sqlText: `
      ${varDefs}
      ${imageQuery.sql}
    `,
    binds,
    parameters: {
      MULTI_STATEMENT_COUNT: binds.length + 1,
    },
    db,
  });

  return { rows, columns };
}

const reqBodySchema = dateRangeBodySchema.extend({
  meatType: z.enum(["beef", "lamb", "*"]),
});

export async function queryIndividualImages(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfOrgLocationPermitted(
    req,
    PkgName.MEQ_CAMERA,
    reqBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  const org = await findOrg(reqBody!.org, reqBody!.location);
  const columnsToRename = org?.country
    ? COLUMNS_TO_RENAME_BY_ORG_COUNTRY[org.country]
    : undefined;

  const settings = await findUserSettings(reqBody!.org, reqBody!.location);

  const isCrossOrgQueryEnabled = hasCrossOrgImageShareSettings(
    settings?.camera,
  );

  const isImageSummaryExtQueryEnabled =
    (await isPackageUser(req, PkgName.MEQ_CAMERA_IMAGE_SUMMARY_EXT)) &&
    hasImageSummaryExtSettings(settings?.camera);

  const columnsToRemove = ["COMPANY_LOCATION"].concat(
    settings?.camera?.summary?.colsBlackList || [],
  );

  if (isClickHouseEnabled()) {
    const resultSet = await queryClickHouse({
      reqBody: reqBody!,
      userSettings: settings?.camera,
      isCrossOrgQueryEnabled,
      isImageSummaryExtQueryEnabled,
    });
    const { meta, data } = removeUnusedColumnsInQueryResult(
      resultSet,
      columnsToRemove,
      columnsToRename,
    );

    res.json({ rows: data, columns: meta, numRows: data.length });
  } else {
    const { rows, columns } = await querySnowflake({
      reqBody: reqBody!,
      userSettings: settings?.camera,
      isCrossOrgQueryEnabled,
      isImageSummaryExtQueryEnabled,
    });

    const optimised = getOptimisedColumns({
      columns,
      rows,
      columnsToRemove,
      columnsToRename,
    });

    res.json({
      ...optimised,
      numRows: optimised.rows?.length || 0,
    });
  }
}
