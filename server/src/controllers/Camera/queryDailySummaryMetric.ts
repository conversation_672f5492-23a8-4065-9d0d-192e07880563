import { Response } from "express";
import { z } from "zod";

import { AuthenticatedRequest } from "../Common";
import { PkgName } from "../../utils/Types";
import {
  COLUMNS_TO_RENAME_BY_ORG_COUNTRY,
  getReqBodyIfOrgLocationPermitted,
} from "../shared";
import { dateRangeBodySchema, findOrg, findUserSettings } from "./shared";
import { findPackageDb } from "../../utils/SnowflakeDatabases";
import {
  buildIndividualImagesQueryCh,
  buildIndividualImagesQuerySf,
  hasCrossOrgImageShareSettings,
} from "./queryBuilder";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import { getOptimisedColumns } from "../../utils/Helper";
import {
  chClient,
  isClickHouseEnabled,
  removeUnusedColumnsInQueryResult,
} from "../../utils/ClickHouse";
import { IUserSettings } from "../../models/UserSettings";

async function queryClickHouse(args: {
  reqBody: z.infer<typeof dateRangeBodySchema>;
  userSettings?: IUserSettings["camera"];
  isCrossOrgQueryEnabled: boolean;
}) {
  const { sql, params } = buildIndividualImagesQueryCh({
    reqBody: args.reqBody,
    userSettings: args.userSettings,
    isCrossOrgQueryEnabled: args.isCrossOrgQueryEnabled,
    isImageSummaryExtQueryEnabled: false,
  });

  const summaryQuery = `
    with images as (${sql})
    select
      toDate32(PHOTO_DATETIME) as PHOTO_DATE,
      count(*) as NUMBER_OF_IMAGES,
      round(avg(MSA_MARBLING), 1) as AVG_MSA_MARBLING,
      round(avg(AUS_MARBLING), 1) as AVG_AUS_MARBLING,
      round(avg(USDA_MARBLING), 1) as AVG_USDA_MARBLING,
      round(avg(IMF), 1) as AVG_IMF,
      round(avg(EYE_MUSCLE_AREA), 1) as AVG_EYE_MUSCLE_AREA,
      round(avg(RIB_FAT), 1) as AVG_RIB_FAT
    from images
    group by PHOTO_DATE
    order by PHOTO_DATE desc
  `;

  return (
    await chClient().query({
      query: summaryQuery,
      format: "JSON",
      query_params: {
        start_date: args.reqBody.startDate,
        end_date: args.reqBody.endDate,
        ...params,
      },
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    })
  ).json();
}

async function querySnowflake(args: {
  reqBody: z.infer<typeof dateRangeBodySchema>;
  userSettings?: IUserSettings["camera"];
  isCrossOrgQueryEnabled: boolean;
}) {
  const db = findPackageDb(PkgName.MEQ_CAMERA);

  const imageQuery = buildIndividualImagesQuerySf({
    reqBody: args.reqBody,
    userSettings: args.userSettings,
    isCrossOrgQueryEnabled: args.isCrossOrgQueryEnabled,
    isImageSummaryExtQueryEnabled: false,
  });

  const { varDefs, binds } = imageQuery.getVarDefsAndBindsSf();

  const summaryQuery = `
    ${varDefs}
    with images as (${imageQuery.sql})
    SELECT
      to_date(photo_datetime) as photo_date,
      count(*) as number_of_images,
      round(avg(msa_marbling), 1) as avg_msa_marbling,
      round(avg(aus_marbling), 1) as avg_aus_marbling,
      round(avg(usda_marbling), 1) as avg_usda_marbling,
      round(avg(imf), 1) as avg_imf,
      round(avg(eye_muscle_area), 1) as avg_eye_muscle_area,
      round(avg(rib_fat), 1) as avg_rib_fat
    FROM
      images
    GROUP BY
      photo_date
  `;

  const { rows, columns } = await executeSnowflakeStatement({
    sqlText: summaryQuery,
    binds,
    parameters: {
      MULTI_STATEMENT_COUNT: binds.length + 1,
    },
    db,
  });

  return { rows, columns };
}

export async function queryDailySummaryMetric(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfOrgLocationPermitted(
    req,
    PkgName.MEQ_CAMERA,
    dateRangeBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  const org = await findOrg(reqBody!.org, reqBody!.location);
  const columnsToRename = org?.country
    ? COLUMNS_TO_RENAME_BY_ORG_COUNTRY[org.country]
    : undefined;

  const settings = await findUserSettings(reqBody!.org, reqBody!.location);

  const isCrossOrgQueryEnabled = hasCrossOrgImageShareSettings(
    settings?.camera,
  );

  if (isClickHouseEnabled()) {
    const resultSet = await queryClickHouse({
      reqBody: reqBody!,
      userSettings: settings?.camera,
      isCrossOrgQueryEnabled,
    });

    const { meta, data } = removeUnusedColumnsInQueryResult(
      resultSet,
      settings?.camera?.summary?.colsBlackList,
      columnsToRename,
    );
    res.json({ rows: data, columns: meta, numRows: data.length });
  } else {
    const { rows, columns } = await querySnowflake({
      reqBody: reqBody!,
      userSettings: settings?.camera,
      isCrossOrgQueryEnabled,
    });

    const optimised = getOptimisedColumns({
      columns,
      rows,
      columnsToRemove: settings?.camera?.summary?.colsBlackList,
      columnsToRename,
    });

    res.json({
      ...optimised,
      numRows: optimised.rows?.length || 0,
    });
  }
}
