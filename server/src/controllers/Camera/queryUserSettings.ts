import { Response } from "express";

import { AuthenticatedRequest } from "../Common";
import { PkgName } from "../../utils/Types";
import {
  getReqBodyIfOrgLocationPermitted,
  orgLocationBodySchema,
} from "../shared";
import { findUserSettings } from "./shared";

export async function queryUserSettings(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfOrgLocationPermitted(
    req,
    PkgName.MEQ_CAMERA,
    orgLocationBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  const userSettings = await findUserSettings(reqBody!.org, reqBody!.location);

  res.json(userSettings?.camera || {});
}
