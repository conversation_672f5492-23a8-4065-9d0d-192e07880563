import { z } from "zod";

import { UserSettings } from "../../models/UserSettings";
import { orgLocationBodySchema } from "../shared";
import { Org } from "../../models";

export const dateRangeBodySchema = orgLocationBodySchema.extend({
  startDate: z.string().date(),
  endDate: z.string().date(),
});

export function findUserSettings(org: string, location: string) {
  return UserSettings.findOne({ org, location });
}

export function findOrg(org: string, location: string) {
  return Org.findOne({
    idString: { $regex: `${org}_${location}`, $options: "i" },
  });
}
