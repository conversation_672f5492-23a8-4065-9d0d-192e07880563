import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { Message, ResultCode, ServerResponse } from "../../utils/Types";
import { ADMIN, SUPER_ADMIN } from "../../utils/Constants";
import {
  getOrgFromRole,
  hasPermission,
  isRoleValidInOrg,
  logExceptionToDB,
  sanitiseParams,
} from "../../utils/Helper";
import {
  getZitadelManagementClient,
  getZitadelUserClient,
} from "../../utils/Zitadel";
import { refreshZitadelUserCacheByUserId } from "../../utils/Cache";
import validator from "validator";
import { User } from "../../models/Interfaces";
import * as Models from "../../models";

// The functions in this file are only available to admins

export const getAllUsersFromDB = async (
  orgIdString: string,
  isSuperAdmin: boolean,
) => {
  const users: User[] = [];
  const allUsers = (
    await Models.User.find({})
      .sort({ _id: -1 })
      .then((o) => o)
  ).filter((i) =>
    isSuperAdmin
      ? true
      : orgIdString === "MEQ"
        ? true
        : !i.email.endsWith("meqprobe.com"),
  );
  allUsers.forEach((i) => {
    let isInThisOrg = false;
    i.roles.forEach((role) => {
      if (getOrgFromRole(role) === orgIdString) {
        isInThisOrg = true;
      }
    });
    if (isInThisOrg) {
      users.push(i);
    }
  });

  return users;
};

export const getAllUsersOfOrg: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  let { orgIdString } = sanitiseParams(req.body);

  if (orgIdString) {
    const orgAdmin = orgIdString + "-" + ADMIN;
    const roles = user.roles;

    if (hasPermission({ roles, requiredRoles: [orgAdmin] })) {
      const users = await getAllUsersFromDB(
        orgIdString,
        roles.includes(SUPER_ADMIN),
      );

      result.data = JSON.stringify(users);
      result.msg = Message.SUCCESS;
      result.code = ResultCode.SUCCESS;
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const addUser: RequestHandler = async (req, res) => {
  const user = req.user as User;
  // const zitadelOrgIdString = req.zitadelOrgIdString;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  let {
    orgIdString,
    email,
    firstName,
    lastName,
    roles: newRoles,
    gmpBusinessName,
    gmpPics,
    gmpPicsWithLots,
  } = sanitiseParams(req.body);

  if (
    orgIdString &&
    email &&
    validator.isEmail(email) &&
    firstName &&
    lastName &&
    newRoles
  ) {
    email = email.toLowerCase();

    const org = await Models.Org.findOne({
      idString: orgIdString,
      isValid: true,
    }).then((o) => o);
    if (org) {
      const orgAdmin = orgIdString + "-" + ADMIN;
      const roles = user.roles;

      if (hasPermission({ roles, requiredRoles: [orgAdmin] })) {
        let isValidRoles = true;
        for (let i = 0; i < newRoles.length; i++) {
          const role = await Models.Role.findOne({ idString: newRoles[i] });
          if (!role) {
            isValidRoles = false;
          }
        }

        if (isValidRoles) {
          const existing = await Models.User.findOne({ email }).then((o) => o);
          if (existing) {
            // update role only
            const rolesToUpdate = existing.roles
              .filter((i) => !i.startsWith(orgIdString))
              .concat(newRoles); // non-this-org-roles plus this-org-roles
            await Models.User.findByIdAndUpdate(existing._id, {
              roles: rolesToUpdate,
              gmpBusinessName,
              gmpPics,
              gmpPicsWithLots,
            });
            await refreshZitadelUserCacheByUserId(existing.zitadelId);

            const users = await getAllUsersFromDB(
              orgIdString,
              roles.includes(SUPER_ADMIN),
            );

            result.data = JSON.stringify(users);
            result.msg = Message.SUCCESS;
            result.code = ResultCode.SUCCESS;
          } else {
            try {
              // create new account
              const target = await (
                await getZitadelManagementClient(org.zitadelOrgIdString)
              )?.addHumanUser({
                userName: email,
                profile: { firstName, lastName, preferredLanguage: "en" },
                email: { email },
              });
              if (target && target.userId) {
                await new Models.User({
                  zitadelOrgIdString: org.zitadelOrgIdString,
                  zitadelId: target.userId,
                  email,
                  zitadelUsername: email,
                  firstName,
                  lastName,
                  displayName: `${firstName} ${lastName}`,
                  roles: newRoles,
                  gmpBusinessName,
                  gmpPics,
                  gmpPicsWithLots,
                  isValid: true,
                })
                  .save()
                  .then()
                  .catch((err) => logExceptionToDB(err));
                await refreshZitadelUserCacheByUserId(target.userId);

                const users = await getAllUsersFromDB(
                  orgIdString,
                  roles.includes(SUPER_ADMIN),
                );

                result.data = JSON.stringify(users);
                result.msg = Message.SUCCESS;
                result.code = ResultCode.SUCCESS;
              }
            } catch (error) {
              if ((error as any).message?.includes("User already exists")) {
                const existingZitadelUserResponse = await (
                  await getZitadelManagementClient(org.zitadelOrgIdString)
                )?.listUsers({
                  queries: [{ emailQuery: { emailAddress: email } }],
                });

                if (
                  existingZitadelUserResponse &&
                  existingZitadelUserResponse.result &&
                  existingZitadelUserResponse.result.length > 0
                ) {
                  await new Models.User({
                    zitadelOrgIdString: org.zitadelOrgIdString,
                    zitadelId: existingZitadelUserResponse.result[0].id,
                    email,
                    zitadelUsername: email,
                    firstName,
                    lastName,
                    displayName: `${firstName} ${lastName}`,
                    roles: newRoles,
                    gmpBusinessName,
                    gmpPics,
                    gmpPicsWithLots,
                    isValid: true,
                  })
                    .save()
                    .then();

                  await refreshZitadelUserCacheByUserId(
                    existingZitadelUserResponse.result[0].id,
                  );

                  const users = await getAllUsersFromDB(
                    orgIdString,
                    roles.includes(SUPER_ADMIN),
                  );

                  result.data = JSON.stringify(users);
                  result.msg = Message.SUCCESS;
                  result.code = ResultCode.SUCCESS;
                }
              }
            }
          }
        } else {
          result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
        }
      } else {
        result.msg = Message.ERROR_PERMISSION_DENIED;
      }
    } else {
      result.msg = Message.ERROR_ORG_NOT_FOUND;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const updateUser: RequestHandler = async (req, res) => {
  const user = req.user as User;
  const zitadelOrgIdString = req.zitadelOrgIdString;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { orgIdString, id, firstName, lastName } = sanitiseParams(req.body);

  if (zitadelOrgIdString && orgIdString && id && firstName && lastName) {
    const orgAdmin = orgIdString + "-" + ADMIN;
    const roles = user.roles;

    if (hasPermission({ roles, requiredRoles: [orgAdmin] })) {
      const target = await (
        await getZitadelUserClient(zitadelOrgIdString)
      )?.updateHumanUser({
        userId: id,
        profile: {
          givenName: firstName,
          familyName: lastName,
          displayName: `${firstName} ${lastName}`,
        },
      });

      if (target) {
        await Models.User.findOneAndUpdate(
          { zitadelId: id },
          { firstName, lastName, displayName: `${firstName} ${lastName}` },
        );
        await refreshZitadelUserCacheByUserId(id);
        const users = await getAllUsersFromDB(
          orgIdString,
          roles.includes(SUPER_ADMIN),
        );

        result.data = JSON.stringify(users);
        result.msg = Message.SUCCESS;
        result.code = ResultCode.SUCCESS;
      } else {
        result.msg = Message.ERROR_USER_NOT_FOUND;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const getAllRoles: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  let { orgIdString } = sanitiseParams(req.body);

  if (orgIdString) {
    const orgAdmin = orgIdString + "-" + ADMIN;
    const roles = user.roles;

    if (hasPermission({ roles, requiredRoles: [orgAdmin] })) {
      const projectRoles = await Models.Role.find({
        idString: { $regex: new RegExp(`^${orgIdString}`, "i") },
      });

      result.data = JSON.stringify(
        projectRoles.map((i) => ({
          label: i.displayName,
          value: i.idString,
          description: i.description,
        })),
      );
      result.msg = Message.SUCCESS;
      result.code = ResultCode.SUCCESS;
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const updateRoles: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const {
    orgIdString,
    id,
    roles: newRoles,
    gmpBusinessName,
  } = sanitiseParams(req.body);

  if (orgIdString && id && newRoles) {
    const orgAdmin = orgIdString + "-" + ADMIN;
    const roles = user.roles;

    if (
      hasPermission({ roles, requiredRoles: [orgAdmin] }) &&
      newRoles.every((r: string) => isRoleValidInOrg(r, orgIdString))
    ) {
      let isValidRoles = true;
      for (let i = 0; i < newRoles.length; i++) {
        const role = await Models.Role.findOne({ idString: newRoles[i] });
        if (!role) {
          isValidRoles = false;
        }
      }

      if (isValidRoles) {
        const targetUser = await Models.User.findOne({ zitadelId: id });
        const rolesToUpdate = (
          targetUser?.roles.filter((i) => !i.startsWith(orgIdString)) || []
        ).concat(newRoles); // non-this-org-roles plus this-org-roles
        await Models.User.findOneAndUpdate(
          { zitadelId: id },
          { roles: rolesToUpdate, gmpBusinessName },
        );
        await refreshZitadelUserCacheByUserId(id);

        const users = await getAllUsersFromDB(
          orgIdString,
          roles.includes(SUPER_ADMIN),
        );

        result.data = JSON.stringify(users);
        result.msg = Message.SUCCESS;
        result.code = ResultCode.SUCCESS;
      } else {
        result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};

export const deleteUser: RequestHandler = async (req, res) => {
  const user = req.user as User;

  const result: ServerResponse = {
    msg: Message.FAILED,
    data: null,
    code: ResultCode.FAILED,
  };

  const { orgIdString, id } = sanitiseParams(req.body);

  if (orgIdString && id) {
    const orgAdmin = orgIdString + "-" + ADMIN;
    const roles = user.roles;

    if (hasPermission({ roles, requiredRoles: [orgAdmin] })) {
      const targetUser = await Models.User.findOne({ zitadelId: id });
      if (targetUser) {
        let isDeleted = false;
        const rolesToUpdate =
          targetUser.roles.filter((i) => !i.startsWith(orgIdString)) || []; // non-this-org-roles
        // this user belongs to this org only
        if (rolesToUpdate.length === 0) {
          // delete user in db and zitadel
          const isDeletedFromZitadel = await (
            await getZitadelManagementClient(targetUser.zitadelOrgIdString)
          )?.removeUser({ id }); // only delete user from its original zitadel server
          if (isDeletedFromZitadel) {
            isDeleted = !!(await Models.User.findOneAndDelete({
              zitadelId: id,
            }));
            await refreshZitadelUserCacheByUserId(id);
          }
        } else {
          isDeleted = !!(await Models.User.findOneAndUpdate(
            { zitadelId: id },
            { roles: rolesToUpdate },
          ));
          await refreshZitadelUserCacheByUserId(id);
        }

        if (isDeleted) {
          const users = await getAllUsersFromDB(
            orgIdString,
            roles.includes(SUPER_ADMIN),
          );

          result.data = JSON.stringify(users);
          result.msg = Message.SUCCESS;
          result.code = ResultCode.SUCCESS;
        }
      } else {
        result.msg = Message.ERROR_USER_NOT_FOUND;
      }
    } else {
      result.msg = Message.ERROR_PERMISSION_DENIED;
    }
  } else {
    result.msg = Message.ERROR_PARAMETERS_NOT_VALID;
  }

  res.status(200).json(result);
};
