import { z } from "zod";

import { AuthenticatedRequest } from "../Common";
import { PkgName } from "../../utils/Types";
import { PackageDatabases } from "../../utils/SnowflakeDatabases";
import { isPackageUser, getReqBodyIfOrgLocationPermitted } from "../shared";

export async function isProbeUser(req: AuthenticatedRequest): Promise<boolean> {
  return isPackageUser(req, PkgName.MEQ_PROBE);
}

export function findDb(): string {
  const db = PackageDatabases.find(
    (d) => d.id === PkgName.MEQ_PROBE.toString(),
  )?.database;
  if (db === undefined) {
    throw new Error(`Database not found for ${PkgName.MEQ_PROBE}`);
  }
  return db;
}

export const authBodySchema = z.object({
  org: z.string().min(1, "org should not be empty").toLowerCase(),
  location: z.string().min(1, "location should not be empty").toLowerCase(),
});

export async function getReqBodyIfPermitted<T extends typeof authBodySchema>(
  req: AuthenticatedRequest,
  bodySchema: T,
): Promise<{
  reqBody?: z.infer<typeof bodySchema>;
  error?: { statusCode: number; message?: string };
}> {
  return getReqBodyIfOrgLocationPermitted(req, PkgName.MEQ_PROBE, bodySchema);
}

export const DATE_RANGE_MAX_IN_MS = 365 * 24 * 60 * 60 * 1000;

export const dateRangeSchema = z
  .tuple([z.string().date(), z.string().date()])
  .refine(
    ([startDate, endDate]) => {
      const startEpoch = Date.parse(startDate);
      const endEpoch = Date.parse(endDate);
      return (
        startEpoch <= endEpoch && endEpoch - startEpoch <= DATE_RANGE_MAX_IN_MS
      );
    },
    {
      message: "Date range length must be between 1 day and 1 year.",
    },
  );

export const dateRangeBodySchema = authBodySchema.extend({
  dateRange: dateRangeSchema,
});
