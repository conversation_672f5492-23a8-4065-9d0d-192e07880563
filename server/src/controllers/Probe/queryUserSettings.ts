import { Response } from "express";

import { AuthenticatedRequest } from "../Common";
import { UserSettings } from "../../models/UserSettings";
import { getReqBodyIfPermitted, authBodySchema } from "./shared";

export async function queryUserSettings(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfPermitted(req, authBodySchema);

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  const userSettings = await UserSettings.findOne({
    org: reqBody!.org,
    location: reqBody!.location,
  });

  res.json(userSettings?.probe || {});
}
