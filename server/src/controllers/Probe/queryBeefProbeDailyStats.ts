import { Response } from "express";

import { AuthenticatedRequest } from "../Common";
import { findDb, dateRangeBodySchema, getReqBodyIfPermitted } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import {
  chClient,
  findDatabaseForPackage,
  isClickHouseEnabled,
} from "../../utils/ClickHouse";
import { PkgName } from "../../utils/Types";

export async function queryBeefProbeDailyStats(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfPermitted(
    req,
    dateRangeBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  if (isClickHouseEnabled()) {
    const db = findDatabaseForPackage(PkgName.MEQ_PROBE);

    const resultSet = await chClient().query({
      query: `
        select
            scan_date as SCAN_DATE,
            avg_msa as AVG_MSA,
            avg_aus as AVG_AUS,
            avg_usda as AVG_USDA,
            num_scans as NUM_SCANS
        from
            ${db}.meqprobe_beef_preds_daily_stats
        where
            company_location = {company_location:String}
            and scan_date between {start_date:Date} and {end_date:Date}
        order by
            scan_date desc
      `,
      format: "JSON",
      query_params: {
        company_location: `${reqBody!.org}-${reqBody!.location}`,
        start_date: reqBody!.dateRange[0],
        end_date: reqBody!.dateRange[1],
      },
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    });

    const { meta, data } = await resultSet.json();
    res.json({ rows: data, columns: meta });
  } else {
    const db = findDb();
    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: `
        set company_location = ?;
        set start_date = ?;
        set end_date = ?;
        select
            scan_date,
            avg_msa,
            avg_aus,
            avg_usda,
            num_scans
        from ${db}.meqinsights.meqprobe_beef_preds_daily_stats
        where scan_date between $start_date and $end_date
          and company_location = $company_location
        order by scan_date desc
      `,
      binds: [
        `${reqBody!.org}-${reqBody!.location}`,
        reqBody!.dateRange[0],
        reqBody!.dateRange[1],
      ],
      parameters: {
        MULTI_STATEMENT_COUNT: 4,
      },
      db,
    });

    res.json({ rows, columns });
  }
}
