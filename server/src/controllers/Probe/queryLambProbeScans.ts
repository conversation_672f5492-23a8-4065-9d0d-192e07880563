import { Response } from "express";
import { z } from "zod";

import { AuthenticatedRequest } from "../Common";
import { authBodySchema, findDb, getReqBodyIfPermitted } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import {
  chClient,
  findDatabaseForPackage,
  isClickHouseEnabled,
} from "../../utils/ClickHouse";
import { PkgName } from "../../utils/Types";

const filtersBodySchema = authBodySchema.extend({
  scanDate: z.string().date(),
});

export async function queryLambProbeScans(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfPermitted(
    req,
    filtersBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  if (isClickHouseEnabled()) {
    const db = findDatabaseForPackage(PkgName.MEQ_PROBE);

    const resultSet = await chClient().query({
      query: `
        SELECT
          probe_id AS PROBE_ID,
          scan_id AS SCAN_ID,
          scan_date AS SCAN_DATE,
          scan_datetime AS SCAN_DATETIME,
          imf AS IMF,
          scan_score AS SCAN_SCORE,
          chain_number AS CHAIN_NUMBER
        FROM ${db}.meqprobe_lamb_preds_summary
        WHERE scan_date = {scan_date: Date}
        AND company_location = {company_location:String}
      `,
      format: "JSON",
      query_params: {
        scan_date: reqBody!.scanDate,
        company_location: `${reqBody!.org}-${reqBody!.location}`,
      },
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    });

    const { meta, data } = await resultSet.json();
    res.json({ rows: data, columns: meta });
  } else {
    const db = findDb();
    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: `
        set company_location = ?;
        set scan_date = ?;
        select
          probe_id,
          scan_id,
          scan_date,
          scan_datetime,
          imf,
          scan_score,
          chain_number
        from ${db}.meqinsights.meqprobe_lamb_preds_summary
        where
          scan_date = $scan_date
          and company_location = $company_location
      `,
      binds: [`${reqBody!.org}-${reqBody!.location}`, reqBody!.scanDate],
      parameters: {
        MULTI_STATEMENT_COUNT: 3,
      },
      db,
    });

    res.json({ rows, columns });
  }
}
