import { RequestHand<PERSON>, Request as ExpressRequest } from "express";
import {
  LooseObject,
  Message,
  ResultCode,
  ServerResponse,
  ZitaDelIntrospectionUser,
} from "../utils/Types";
import { logActionToDB, sanitiseParams } from "../utils/Helper";
import { ActionLog, User as IUser } from "../models/Interfaces";
import AppDataSource from "../v1/AppDataSource";
import { User as UserV1 } from "../v1/models";
import {
  getUserByZitadelUserId,
  getZitadeIntrospectTokenInfo,
  getZitadelOrg,
} from "../utils/Cache";

declare global {
  namespace Express {
    interface Request {
      user?: LooseObject;
      zitadelOrgIdString?: string;
    }
  }
}

export interface AuthenticatedRequest extends ExpressRequest {
  user?: LooseObject;
  zitadelOrgIdString?: string;
}

export const helloWorld: RequestHandler = (req, res, next) => {
  const result: ServerResponse = {
    msg: Message.SUCCESS,
    code: ResultCode.SUCCESS,
    data: "Hello World",
  };
  res.status(200).json(result);
};

export const unauthorized: RequestHandler = (req, res, next) => {
  const result: ServerResponse = {
    msg: Message.UNAUTHORIZED,
    code: ResultCode.FAILED,
    data: null,
  };
  res.status(401).json(result);
};

export const validateZitadelAccessToken: RequestHandler = async (
  req,
  res,
  next,
) => {
  const result: ServerResponse = {
    msg: Message.ERROR_USER_NOT_FOUND,
    code: ResultCode.FAILED,
    data: null,
  };

  const authHeader = req.headers["authorization"];
  const zitadelOrgIdString = req.headers["zitadelorgidstring"];

  if (authHeader?.startsWith("Bearer ") && zitadelOrgIdString) {
    const token = authHeader.split(" ")[1];
    if (token) {
      try {
        const zitadelOrg = await getZitadelOrg(zitadelOrgIdString as string);
        if (zitadelOrg) {
          const tokenData = await getZitadeIntrospectTokenInfo({
            zitadelOrg,
            token,
          });
          if (!tokenData.active) {
            res.status(401).json(result);
          } else {
            req.user = tokenData;
            req.zitadelOrgIdString = zitadelOrgIdString as string;
            next();
          }
        } else {
          res.status(401).json(result);
        }
      } catch (error) {
        res.status(401).json(result);
      }
    } else {
      res.status(401).json(result);
    }
  } else {
    res.status(401).json(result);
  }
};

export const loadUserFromDB: RequestHandler = async (req, res, next) => {
  const result: ServerResponse = {
    msg: Message.ERROR_USER_NOT_FOUND,
    code: ResultCode.FAILED,
    data: null,
  };

  const user = req.user as ZitaDelIntrospectionUser;

  if (user) {
    const userFromDb = await getUserByZitadelUserId(user.sub);
    if (userFromDb) {
      req.user = userFromDb;
      next();
    } else {
      // check if user exists in v1
      const userRepository = AppDataSource.getRepository(UserV1);
      let userFromDbV1 = await userRepository.findOne({
        where: { email: user.email, isDeleted: false },
      });

      if (userFromDbV1) {
        req.user = userFromDbV1;
        next();
      } else {
        res.status(401).json(result);
      }
    }
  } else {
    res.status(401).json(result);
  }
};

export const logAccess: RequestHandler = (req, res, next) => {
  const user = req.user as IUser;
  const route = req.url;
  const params = sanitiseParams(req.body);

  const actionLogToSave = {
    username: user.zitadelUsername,
    roles: user.roles,
    route,
    params,
  };
  logActionToDB(actionLogToSave as ActionLog);

  next();
};
