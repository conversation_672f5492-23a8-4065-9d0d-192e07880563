import { Response } from "express";
import { z } from "zod";

import { AuthenticatedRequest } from "../Common";
import { filtersBodySchema, findDb, getReqBodyIfPermitted } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import {
  chClient,
  findDatabaseForPackage,
  isClickHouseEnabled,
} from "../../utils/ClickHouse";
import { PkgName } from "../../utils/Types";

const reqBodySchema = filtersBodySchema.extend({
  scoreDiff: z.number().int(),
});

export async function queryBarcodes(req: AuthenticatedRequest, res: Response) {
  const { reqBody, error } = await getReqBodyIfPermitted(req, reqBodySchema);

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  const tablePrefix = reqBody!.org;

  if (isClickHouseEnabled()) {
    const db = findDatabaseForPackage(PkgName.MEQ_CAMERA_PLUS);

    const resultSet = await chClient().query({
      query: `
        select
          barcode as BARCODE,
          camera_user as CAMERA_USER,
          grader as GRADER,
          aus_marbling as AUS_MARBLING,
          override_aus_marbling as OVERRIDE_AUS_MARBLING
        from ${db}.${tablePrefix}_cam_preds
        where
          photo_date between toDate({start_date: String}) and toDate({end_date: String})
          and location = {location: String}
          and if({brand: String} = '*', true, brand = {brand: String})
          and (override_aus_marbling - aus_marbling) = {score_diff: String}
      `,
      format: "JSON",
      query_params: {
        start_date: reqBody!.dateRange[0],
        end_date: reqBody!.dateRange[1],
        location: reqBody!.location,
        brand: reqBody!.brand,
        score_diff: reqBody!.scoreDiff,
      },
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    });

    const { meta, data } = await resultSet.json();
    res.json({ rows: data, columns: meta });
  } else {
    const db = findDb();
    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: `
        set location = ?;
        set brand = ?;
        set start_date = ?;
        set end_date = ?;
        set score_diff = ?;

        select
          BARCODE,
          CAMERA_USER,
          GRADER,
          AUS_MARBLING,
          OVERRIDE_AUS_MARBLING
        from ${db}.CAMERA_PLUS.${tablePrefix}_CAM_PREDS
        where
          PHOTO_DATE between to_date($start_date) and to_date($end_date)
          and LOCATION = $location
          and iff($brand = '*', true, BRAND = $brand)
          and (OVERRIDE_AUS_MARBLING - AUS_MARBLING) = $score_diff
      `,
      binds: [
        reqBody!.location,
        reqBody!.brand,
        reqBody!.dateRange[0],
        reqBody!.dateRange[1],
        reqBody!.scoreDiff,
      ],
      parameters: {
        MULTI_STATEMENT_COUNT: 6,
      },
      db,
    });

    res.json({ rows, columns });
  }
}
