import { Response } from "express";

import { AuthenticatedRequest } from "../Common";
import { filtersBodySchema, findDb, getReqBodyIfPermitted } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import {
  chClient,
  findDatabaseForPackage,
  isClickHouseEnabled,
} from "../../utils/ClickHouse";
import { PkgName } from "../../utils/Types";

export async function queryProbeCameraAverages(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfPermitted(
    req,
    filtersBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  const tablePrefix = reqBody!.org;

  if (isClickHouseEnabled()) {
    const db = findDatabaseForPackage(PkgName.MEQ_CAMERA_PLUS);

    const resultSet = await chClient().query({
      query: `
        with cam_probe as (
          select *
          from ${db}.${tablePrefix}_cam_probe
          where
            photo_date between toDate({start_date: String}) and toDate({end_date: String})
            and location = {location: String}
            and if({brand: String} = '*', true, brand = {brand: String})
            and cam_aus is not null
            and probe_aus is not null
        )
        select
          avg(cam_aus) as AVG_CAM_AUS,
          avg(probe_aus) as AVG_PROBE_AUS,
          avg(probe_aus - cam_aus) as AVG_PROBE_CAM_DIFF,
          countIf(cam_aus >= probe_aus) / count(*) as PROBE_PRECISION
        from cam_probe
      `,
      format: "JSON",
      query_params: {
        start_date: reqBody!.dateRange[0],
        end_date: reqBody!.dateRange[1],
        location: reqBody!.location,
        brand: reqBody!.brand,
      },
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    });

    const { meta, data } = await resultSet.json();
    res.json({ rows: data, columns: meta });
  } else {
    const db = findDb();
    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: `
        set location = ?;
        set brand = ?;
        set start_date = ?;
        set end_date = ?;

        with CAM_PROBE as (
          select *
          from ${db}.CAMERA_PLUS.${tablePrefix}_CAM_PROBE
          where
            PHOTO_DATE between to_date($start_date) and to_date($end_date)
            and LOCATION = $location
            and iff($brand = '*', true, BRAND = $brand)
            and CAM_AUS is not null
            and PROBE_AUS is not null
        )
        select
          avg(CAM_AUS) as AVG_CAM_AUS,
          avg(PROBE_AUS) as AVG_PROBE_AUS,
          avg(PROBE_AUS - CAM_AUS) as AVG_PROBE_CAM_DIFF,
          count_if(CAM_AUS >= PROBE_AUS) / count(*) as PROBE_PRECISION
        from CAM_PROBE
      `,
      binds: [
        reqBody!.location,
        reqBody!.brand,
        reqBody!.dateRange[0],
        reqBody!.dateRange[1],
      ],
      parameters: {
        MULTI_STATEMENT_COUNT: 5,
      },
      db,
    });

    res.json({ rows, columns });
  }
}
