import { Response } from "express";
import { z } from "zod";
import _ from "lodash";

import { AuthenticatedRequest } from "../Common";
import { authBodySchema, findDb, getReqBodyIfPermitted } from "./shared";
import { UserSettings } from "../../models/UserSettings";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import {
  chClient,
  findDatabaseForPackage,
  isClickHouseEnabled,
} from "../../utils/ClickHouse";
import { PkgName } from "../../utils/Types";

const bodySchema = authBodySchema.extend({
  location: z.string().min(1, "location should not be empty").toLowerCase(),
});

export async function queryBrands(req: AuthenticatedRequest, res: Response) {
  const { reqBody, error } = await getReqBodyIfPermitted(req, bodySchema);

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  const userSettings = await UserSettings.findOne({
    org: reqBody!.org,
    location: reqBody!.location,
  });

  const tablePrefix = reqBody!.org;

  if (isClickHouseEnabled()) {
    const db = findDatabaseForPackage(PkgName.MEQ_CAMERA_PLUS);

    const resultSet = await chClient().query({
      query: `
        select distinct brand as BRAND
        from ${db}.${tablePrefix}_cam_preds
        where location = {location: String}
      `,
      format: "JSON",
      query_params: {
        location: reqBody!.location,
      },
    });

    const rows = (await resultSet.json()).data;

    res.json({
      top: userSettings?.cameraPlus?.topBrands,
      all: rows.map((r) => _.get(r, "BRAND")),
    });
  } else {
    const db = findDb();
    const { rows } = await executeSnowflakeStatement({
      sqlText: `
        set location = ?;
        select distinct BRAND
        from ${db}.CAMERA_PLUS.${tablePrefix}_CAM_PREDS
        where LOCATION = $location
      `,
      binds: [reqBody!.location],
      parameters: {
        MULTI_STATEMENT_COUNT: 2,
      },
      db,
    });

    res.json({
      top: userSettings?.cameraPlus?.topBrands,
      all: rows?.map((r) => _.get(r, "BRAND") as string),
    });
  }
}
