import { Response } from "express";

import { AuthenticatedRequest } from "../Common";
import { filtersBodySchema, findDb, getReqBodyIfPermitted } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import {
  chClient,
  findDatabaseForPackage,
  isClickHouseEnabled,
} from "../../utils/ClickHouse";
import { PkgName } from "../../utils/Types";

export async function queryUserStats(req: AuthenticatedRequest, res: Response) {
  const { reqBody, error } = await getReqBodyIfPermitted(
    req,
    filtersBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }
  const tablePrefix = reqBody!.org;
  if (isClickHouseEnabled()) {
    const db = findDatabaseForPackage(PkgName.MEQ_CAMERA_PLUS);

    const resultSet = await chClient().query({
      query: `
        select
          camera_user as CAMERA_USER,
          photo_date as PHOTO_DATE,
          sum(count_barcodes) as COUNT_BARCODES,
          sum(count_overrides) as COUNT_OVERRIDES,
          sum(count_under_score) as COUNT_UNDER_SCORE,
          sum(count_over_score) as COUNT_OVER_SCORE
        from ${db}.${tablePrefix}_daily_stats
        where
          photo_date between toDate({start_date: String}) and toDate({end_date: String})
          and if({brand: String} = '*', true, brand = {brand: String})
          and location = {location: String}
        group by camera_user, photo_date
        order by camera_user
      `,
      format: "JSON",
      query_params: {
        start_date: reqBody!.dateRange[0],
        end_date: reqBody!.dateRange[1],
        location: reqBody!.location,
        brand: reqBody!.brand,
      },
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    });

    const { meta, data } = await resultSet.json();
    res.json({ rows: data, columns: meta });
  } else {
    const db = findDb();
    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: `
        set location = ?;
        set brand = ?;
        set start_date = ?;
        set end_date = ?;
        select
          CAMERA_USER,
          PHOTO_DATE,
          sum(COUNT_BARCODES) as COUNT_BARCODES,
          sum(COUNT_OVERRIDES) as COUNT_OVERRIDES,
          sum(COUNT_UNDER_SCORE) as COUNT_UNDER_SCORE,
          sum(COUNT_OVER_SCORE) as COUNT_OVER_SCORE,
        from ${db}.CAMERA_PLUS.${tablePrefix}_DAILY_STATS
        where
          PHOTO_DATE between to_date($start_date) and to_date($end_date)
          and iff($brand = '*', true, BRAND = $brand)
          and LOCATION = $location
        group by CAMERA_USER, PHOTO_DATE
        order by CAMERA_USER
      `,
      binds: [
        reqBody!.location,
        reqBody!.brand,
        reqBody!.dateRange[0],
        reqBody!.dateRange[1],
      ],
      parameters: {
        MULTI_STATEMENT_COUNT: 5,
      },
      db,
    });

    res.json({ rows, columns });
  }
}
