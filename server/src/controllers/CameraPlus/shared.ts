import { z } from "zod";
import { AuthenticatedRequest } from "../Common";
import { PkgName } from "../../utils/Types";
import { PackageDatabases } from "../../utils/SnowflakeDatabases";
import { isPackageUser, getReqBodyIfOrgLocationPermitted } from "../shared";

export async function isCameraPlusUser(
  req: AuthenticatedRequest,
): Promise<boolean> {
  return isPackageUser(req, PkgName.MEQ_CAMERA_PLUS);
}

export function findDb(): string {
  const db = PackageDatabases.find(
    (d) => d.id === PkgName.MEQ_CAMERA_PLUS.toString(),
  )?.database;
  if (db === undefined) {
    throw new Error(`Database not found for ${PkgName.MEQ_CAMERA_PLUS}`);
  }
  return db;
}

export const authBodySchema = z.object({
  org: z.string().min(1, "org should not be empty").toLowerCase(),
  location: z.string().min(1, "location should not be empty").toLowerCase(),
});

export const filtersBodySchema = authBodySchema.extend({
  brand: z.string().min(1, "brand should not be empty"),
  dateRange: z.tuple([z.string().date(), z.string().date()]),
});

export async function getReqBodyIfPermitted<T extends typeof authBodySchema>(
  req: AuthenticatedRequest,
  bodySchema: T,
): Promise<{
  reqBody?: z.infer<typeof bodySchema>;
  error?: { statusCode: number; message?: string };
}> {
  return getReqBodyIfOrgLocationPermitted(
    req,
    PkgName.MEQ_CAMERA_PLUS,
    bodySchema,
  );
}
