import { Response } from "express";

import { AuthenticatedRequest } from "../Common";
import { filtersBodySchema, findDb, getReqBodyIfPermitted } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import {
  chClient,
  findDatabaseForPackage,
  isClickHouseEnabled,
} from "../../utils/ClickHouse";
import { PkgName } from "../../utils/Types";

export async function queryDailyOverrideDiffTrend(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfPermitted(
    req,
    filtersBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  const tablePrefix = reqBody!.org;

  if (isClickHouseEnabled()) {
    const db = findDatabaseForPackage(PkgName.MEQ_CAMERA_PLUS);

    const resultSet = await chClient().query({
      query: `
        select
          photo_date as PHOTO_DATE,
          override_aus_marbling - aus_marbling as SCORE_DIFF,
          count(SCORE_DIFF) as FREQUENCY
        from ${db}.${tablePrefix}_cam_preds
        where
          photo_date between toDate({start_date: String}) and toDate({end_date: String})
          and location = {location: String}
          and if({brand: String} = '*', true, brand = {brand: String})
          and aus_marbling is not null
          and override_aus_marbling is not null
        group by photo_date, SCORE_DIFF
       `,
      format: "JSON",
      query_params: {
        start_date: reqBody!.dateRange[0],
        end_date: reqBody!.dateRange[1],
        location: reqBody!.location,
        brand: reqBody!.brand,
      },
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    });

    const { meta, data } = await resultSet.json();
    res.json({ rows: data, columns: meta });
  } else {
    const db = findDb();
    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: `
        set location = ?;
        set brand = ?;
        set start_date = ?;
        set end_date = ?;
        select
          PHOTO_DATE,
          OVERRIDE_AUS_MARBLING - AUS_MARBLING as SCORE_DIFF,
          count(SCORE_DIFF) as FREQUENCY
        from ${db}.CAMERA_PLUS.${tablePrefix}_CAM_PREDS
        where
          PHOTO_DATE between to_date($start_date) and to_date($end_date)
          and LOCATION = $location
          and iff($brand = '*', true, BRAND = $brand)
          and AUS_MARBLING is not null
          and OVERRIDE_AUS_MARBLING is not null
        group by PHOTO_DATE, SCORE_DIFF
      `,
      binds: [
        reqBody!.location,
        reqBody!.brand,
        reqBody!.dateRange[0],
        reqBody!.dateRange[1],
      ],
      parameters: {
        MULTI_STATEMENT_COUNT: 5,
      },
      db,
    });

    res.json({ rows, columns });
  }
}
