import { Response } from "express";

import { AuthenticatedRequest } from "../Common";
import { filtersBodySchema, findDb, getReqBodyIfPermitted } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import {
  chClient,
  findDatabaseForPackage,
  isClickHouseEnabled,
} from "../../utils/ClickHouse";
import { PkgName } from "../../utils/Types";

export async function queryDateRangeAverages(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfPermitted(
    req,
    filtersBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }
  const tablePrefix = reqBody!.org;

  if (isClickHouseEnabled()) {
    const db = findDatabaseForPackage(PkgName.MEQ_CAMERA_PLUS);

    const resultSet = await chClient().query({
      query: `
        with daily_stats as (
          select *
          from ${db}.${tablePrefix}_daily_stats
          where
            photo_date between toDate({start_date: String}) and toDate({end_date: String})
            and location = {location: String}
            and if({brand: String} = '*', true, brand = {brand: String})
        ),
        num_dates as (
          select count(distinct photo_date) as value
          from daily_stats
        ),
        averages as (
          select
            (select value from num_dates) as shift_days,
            sum(count_barcodes) / shift_days as avg_barcodes,
            sum(count_overrides) / shift_days as avg_overrides,
            sum(count_under_score) / shift_days as avg_under_score,
            sum(count_over_score) / shift_days as avg_over_score
          from daily_stats
        )
        select
          avg_barcodes as AVG_BARCODES,
          avg_overrides as AVG_OVERRIDES,
          avg_under_score as AVG_UNDER_SCORE,
          avg_over_score as AVG_OVER_SCORE
        from averages
      `,
      format: "JSON",
      query_params: {
        start_date: reqBody!.dateRange[0],
        end_date: reqBody!.dateRange[1],
        location: reqBody!.location,
        brand: reqBody!.brand,
      },
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    });

    const { meta, data } = await resultSet.json();
    res.json({ rows: data, columns: meta });
  } else {
    const db = findDb();
    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: `
        set location = ?;
        set brand = ?;
        set start_date = ?;
        set end_date = ?;

        with DAILY_STATS as (
          select *
          from ${db}.CAMERA_PLUS.${tablePrefix}_DAILY_STATS
          where
            PHOTO_DATE between to_date($start_date) and to_date($end_date)
            and LOCATION = $location
            and iff($brand = '*', true, BRAND = $brand)
        ),
        NUM_DATES as (
          select count(distinct PHOTO_DATE) as VALUE
          from DAILY_STATS
        ),
        AVERAGES as (
          select
            (select VALUE from NUM_DATES) as SHIFT_DAYS,
            sum(COUNT_BARCODES) / SHIFT_DAYS as AVG_BARCODES,
            sum(COUNT_OVERRIDES) / SHIFT_DAYS as AVG_OVERRIDES,
            sum(COUNT_UNDER_SCORE) / SHIFT_DAYS as AVG_UNDER_SCORE,
            sum(COUNT_OVER_SCORE) / SHIFT_DAYS as AVG_OVER_SCORE
          from DAILY_STATS
        )
        select
          AVG_BARCODES, AVG_OVERRIDES, AVG_UNDER_SCORE, AVG_OVER_SCORE
        from AVERAGES
      `,
      binds: [
        reqBody!.location,
        reqBody!.brand,
        reqBody!.dateRange[0],
        reqBody!.dateRange[1],
      ],
      parameters: {
        MULTI_STATEMENT_COUNT: 5,
      },
      db,
    });

    res.json({ rows, columns });
  }
}
