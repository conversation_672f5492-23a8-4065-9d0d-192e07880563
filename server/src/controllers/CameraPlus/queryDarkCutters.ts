import { Response } from "express";

import { AuthenticatedRequest } from "../Common";
import { filtersBodySchema, findDb, getReqBodyIfPermitted } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import {
  chClient,
  findDatabaseForPackage,
  isClickHouseEnabled,
} from "../../utils/ClickHouse";
import { PkgName } from "../../utils/Types";

export async function queryDarkCutters(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfPermitted(
    req,
    filtersBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  const tablePrefix = reqBody!.org;
  const bucketSize = 0.2;

  if (isClickHouseEnabled()) {
    const db = findDatabaseForPackage(PkgName.MEQ_CAMERA_PLUS);

    const resultSet = await chClient().query({
      query: `
        select
          floor(ph / ${bucketSize}) * ${bucketSize} as PH_BUCKET,
          count(ph) as FREQUENCY
        from ${db}.${tablePrefix}_cam_probe
        where
          photo_date between toDate({start_date: String}) and toDate({end_date: String})
          and location = {location: String}
          and if({brand: String} = '*', true, brand = {brand: String})
          and ph between 5.71 and 14
        group by
          PH_BUCKET
      `,
      format: "JSON",
      query_params: {
        start_date: reqBody!.dateRange[0],
        end_date: reqBody!.dateRange[1],
        location: reqBody!.location,
        brand: reqBody!.brand,
      },
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    });

    const { meta, data } = await resultSet.json();
    res.json({ rows: data, columns: meta, bucketSize });
  } else {
    const db = findDb();
    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: `
        set location = ?;
        set brand = ?;
        set start_date = ?;
        set end_date = ?;

        select
          floor(PH / ${bucketSize}) * ${bucketSize} as PH_BUCKET,
          count(PH) as FREQUENCY
        from ${db}.CAMERA_PLUS.${tablePrefix}_CAM_PROBE
        where
          PHOTO_DATE between to_date($start_date) and to_date($end_date)
          and LOCATION = $location
          and iff($brand = '*', true, BRAND = $brand)
          and PH between 5.71 and 14
        group by
          PH_BUCKET
      `,
      binds: [
        reqBody!.location,
        reqBody!.brand,
        reqBody!.dateRange[0],
        reqBody!.dateRange[1],
      ],
      parameters: {
        MULTI_STATEMENT_COUNT: 5,
      },
      db,
    });

    res.json({ rows, columns, bucketSize });
  }
}
