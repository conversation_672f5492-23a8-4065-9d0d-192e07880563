import { Response } from "express";

import { AuthenticatedRequest } from "../Common";
import { filtersBodySchema, findDb, getReqBodyIfPermitted } from "./shared";
import { executeSnowflakeStatement } from "../../utils/SnowflakeConnection";
import {
  chClient,
  findDatabaseForPackage,
  isClickHouseEnabled,
} from "../../utils/ClickHouse";
import { PkgName } from "../../utils/Types";

export async function queryDailyGradingFreq(
  req: AuthenticatedRequest,
  res: Response,
) {
  const { reqBody, error } = await getReqBodyIfPermitted(
    req,
    filtersBodySchema,
  );

  if (error) {
    res.status(error.statusCode).send(error.message);
    return;
  }

  const tablePrefix = reqBody!.org;

  if (isClickHouseEnabled()) {
    const db = findDatabaseForPackage(PkgName.MEQ_CAMERA_PLUS);

    const resultSet = await chClient().query({
      query: `
        select
          sum(if(graded_by = 'camera', frequency, 0)) as CAMERA_USE_FREQUENCY,
          sum(frequency) as TOTAL,
          grading_date as GRADING_DATE
        from ${db}.${tablePrefix}_grading_freq
        where
          location = {location: String}
          and if({brand: String} = '*', true, brand = {brand: String})
          and grading_date between {start_date: String} and {end_date: String}
        group by grading_date
       `,
      format: "JSON",
      query_params: {
        start_date: reqBody!.dateRange[0],
        end_date: reqBody!.dateRange[1],
        location: reqBody!.location,
        brand: reqBody!.brand,
      },
      clickhouse_settings: { output_format_json_quote_64bit_integers: 0 },
    });

    const { meta, data } = await resultSet.json();
    res.json({ rows: data, columns: meta });
  } else {
    const db = findDb();

    const { rows, columns } = await executeSnowflakeStatement({
      sqlText: `
        set location = ?;
        set brand = ?;
        set start_date = ?;
        set end_date = ?;
        select
          sum(IFF(GRADED_BY = 'camera', FREQUENCY, 0)) as CAMERA_USE_FREQUENCY,
          sum(FREQUENCY) as TOTAL,
          GRADING_DATE
        from ${db}.CAMERA_PLUS.${tablePrefix}_GRADING_FREQ
        where
          LOCATION = $location
          and iff($brand = '*', true, BRAND = $brand)
          and GRADING_DATE between $start_date and $end_date
        group by GRADING_DATE
      `,
      binds: [
        reqBody!.location,
        reqBody!.brand,
        reqBody!.dateRange[0],
        reqBody!.dateRange[1],
      ],
      parameters: {
        MULTI_STATEMENT_COUNT: 5,
      },
      db,
    });

    res.json({ rows, columns });
  }
}
