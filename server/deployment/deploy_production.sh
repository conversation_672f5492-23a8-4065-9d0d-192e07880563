#!/bin/bash

# Define variables
local_dir="$HOME/meq/meq-insights-v2-server"
dest_dir="/home/<USER>/meq-insights-server"
destination_server="10.6.0.8" # prod server
username="meq"

# Rsync push
rsync -abvizP --delete --exclude="build/" --exclude=".vscode/" --exclude=".git/" --exclude="node_modules/" --exclude=".env" --exclude="yarn.lock" --exclude="Test.ts" --exclude="TestJson.json" --exclude="server.log" --exclude="snowflake.log" --exclude="*.test.ts" $local_dir/ $username@$destination_server:$dest_dir/

# Install dependencies and run the project
ssh $username@$destination_server "cd $dest_dir && yarn && cd .. && sh pm2_restart.sh"
