{"name": "meq-insights-v2-server", "version": "1.0.0", "main": "index.js", "license": "MIT", "scripts": {"test": "jest", "test:watch": "jest --watch", "build": "yarn rimraf ./build && tsc", "start": "yarn run build && node build/index.js", "serve": "node build/index.js", "dev": "yarn nodemon", "lint": "eslint .", "alliance-sync": "node build/alliance-sync.js", "format:check": "prettier -c .", "format:write": "prettier -w ."}, "dependencies": {"@bufbuild/protobuf": "^2.0.0", "@clickhouse/client": "^1.10.1", "@faker-js/faker": "^8.4.1", "@types/body-parser": "^1.19.5", "@types/numeral": "^2.0.5", "@zitadel/node": "^2.0.21", "asn1.js": "^5.4.1", "asn1js": "^3.0.5", "axios": "^1.7.2", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.4.5", "elastic-apm-node": "^4.8.1", "express": "^4.19.2", "express-async-handler": "^1.2.0", "helmet": "^7.1.0", "http-status-codes": "^2.3.0", "jose": "^5.9.2", "lodash": "^4.17.21", "log4js": "^6.9.1", "lru-cache": "^10.3.0", "moment": "^2.30.1", "moment-timezone": "^0.5.47", "mongoose": "^8.5.0", "mongoose-paginate-v2": "^1.8.2", "nice-grpc-common": "^2.0.2", "node-rsa": "^1.1.1", "nodemailer": "^6.9.14", "numeral": "^2.0.6", "openpgp": "^5.11.2", "otpauth": "^9.3.1", "pg": "^8.12.0", "reflect-metadata": "^0.2.2", "snowflake-sdk": "^1.11.0", "typeorm": "^0.3.20", "uuid": "^10.0.0", "validator": "^13.12.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.12.0", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/eslint__js": "^8.42.3", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.6", "@types/node": "^20.14.10", "@types/node-rsa": "^1.1.4", "@types/nodemailer": "^6.4.15", "@types/snowflake-sdk": "^1.6.24", "@types/uuid": "^10.0.0", "@types/validator": "^13.12.0", "eslint": "^9.12.0", "eslint-config-prettier": "^9.1.0", "jest": "^29.7.0", "nodemon": "^3.1.4", "prettier": "3.3.3", "rimraf": "^5.0.9", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.5.3", "typescript-eslint": "^8.8.1"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/*.test.ts"], "transform": {"^.+\\.tsx?$": "ts-jest"}}, "packageManager": "yarn@1.22.22"}