// @ts-check

import eslint from "@eslint/js";
import tseslint from "typescript-eslint";
import eslint<PERSON>onfig<PERSON><PERSON><PERSON> from "eslint-config-prettier";

export default tseslint.config(
  eslint.configs.recommended,
  ...tseslint.configs.recommendedTypeChecked,
  {
    languageOptions: {
      parserOptions: {
        projectService: true,
        tsconfigRootDir: import.meta.dirname,
      },
    },
  },
  eslintConfigPrettier,
  {
    ignores: [".yarn/*", ".pnp.*"],
    rules: {
      "@typescript-eslint/no-explicit-any": ["warn"],
      "@typescript-eslint/no-empty-object-type": ["warn"],
      "@typescript-eslint/no-unused-vars": ["warn"],
      "@typescript-eslint/no-unsafe-member-access": ["warn"],
    },
  },
);
