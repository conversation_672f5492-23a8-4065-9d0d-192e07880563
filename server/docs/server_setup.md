# Update the journal.conf

Set SystemMaxUse=1000M to prevent the logs getting too big

```
sudo nano /etc/systemd/journald.conf

systemctl restart systemd-journald
```

# Install ZeroTier and join network

```
curl -s https://install.zerotier.com | sudo bash
sudo zerotier-cli join abfd31bd475048c2  # Note: We need to authorise on ZeroTier.com
```

# Install Node.js version 18

```
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs
```

# Install yarn

```
sudo npm install yarn -g
yarn --version
```

# Copy project repo from your _MacOS_ to server

On your _MacOS_, install `ssh-copy-id`

```
brew install ssh-copy-id
```

Then run `deploy.sh`

# Create .env file

In project root directory, create .env file and add the following

```
NODE_ENV = production
... Ask Someone to Get this Full List
```

# Install PM2 on your local machine

```
sudo npm install pm2 -g
```

# Start the project with PM2

Create PM2 config file

```
cd ~
nano pm2.config.js
```

Add the following content:

```
module.exports = {
  apps: [
    {
      name: 'meq-insights-server',
      cwd: '/home/<USER>/meq-insights-server',
      script: 'yarn',
      args: 'start',
    },
  ],
};
```

Start the project

```
pm2 start pm2.config.js
```

# Set PM2 auto start after crash or system reboot

```
pm2 startup
```

Then copy and run the path showing in the console to set PATH. And run

```
pm2 save
```

If want to delete the auto start scripts, run:

```
pm2 unstartup systemd
```

# Update nginx to serve the api on the same server with the frontend

```
cd /etc/nginx/sites-available
sudo mv default default-copy
sudo nano default
```

Add the following content

```
# Define upstream block for Node.js application
upstream nodejs {
  server 127.0.0.1:8443;
}

server {
  # listen 80 default_server;
  # listen [::]:80 default_server;
  listen 443 ssl http2 default_server;
  listen [::]:443 ssl http2 default_server;

  ssl_certificate /etc/letsencrypt/live/YOUR_DOMAIN/fullchain.pem;
  ssl_certificate_key /etc/letsencrypt/live/YOUR_DOMAIN/privkey.pem;

  gzip on;
  gzip_types application/javascript image/* text/css;
  gunzip on;

  root /var/www/meq-insights/build;
  index index.html;
  server_name YOUR_DOMAIN;

  location / {
    try_files $uri /index.html;
  }

  # Location block for API requests
  location /api/ {
    proxy_pass http://nodejs/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_http_version 1.1;
  }

  # Prevent caching of index.html
  location = /index.html {
    add_header Cache-Control "no-cache, no-store, must-revalidate";
  }
}

# Redirect http traffic to https
server {
  listen 0.0.0.0:80;
  server_name YOUR_DOMAIN;
  rewrite ^ https://$host$request_uri? permanent;
}
```

Test if the config is valid

```
sudo nginx -t
```

If no error, restart the server

```
sudo systemctl restart nginx
```
