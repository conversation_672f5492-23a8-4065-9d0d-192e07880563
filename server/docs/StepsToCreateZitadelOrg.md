# Overview

For premier clients, a dedicated ZITADEL organization is recommended. The benefits include:

1. Full customization of the ZITADEL user interface, including the login page, emails, logos, and more.
1. Enhanced security, as users for that organization will remain within their own ZITADEL organization, separate from others.

**All the following steps are performed on the ZITADEL UI Console. Use default settings if not mentioned.**

# Step 1: Create an organisation

At the top left of the web page, click the Orgnisation Switch icon to create a new organisation.

# Step 2: Create a project

Under `Project` tab, create a new project called `Insights`.

# Step 3: Create applications

## Create `React` project

This `React` project is for frontend authentication.

- Type in project name _React_ and select type as _User Agent_.
- Choose _PKCE_ as the authenctation method.
- Enable _Development Mode_ so the project can be tested on local machine. Type in _Redirect URIs (it will be `orgnisation name + host name + /callback`, eg, https://gmp.dev.meqinsights.com/callback or https://gmp.meqinsights.com/callback. Also add http://localhost:3000/callback for local test)._ Type in _Post Logout URIs (it will be `orgnisation name + host name`, eg, https://gmp.dev.meqinsights.com or https://gmp.meqinsights.com. Also add http://localhost:3000/callback for local test)._
- No need to keep _client secret_.

## Create `Node With Keys` project

This `Node With Keys` project is for backend introscpection authentication, ie, validating access tokens coming from frontend agains Zitadel server.

- Type in project name _Node With Keys_ and select type as _API_.
- Choose _JWT_ as the authenctation method.
- No need to keep _client secret_.

Then create a Key in the `Configuration` tab under this Project.

- Select type as _JSON_.
- Set a expire date if needed.
- Make sure to download the key right away as it will not be visable again.

# Step 4: Create a service user

Under `Users` tab, select `Service Users`, then click the _New_ button.

- type in _user name (it will be `orgnisation name + -machine`, eg, gmp-machine)._
- type in _user name (it will be `orgnisation name +  Machine`, eg, GMP Machine)._
- select _Bearer_ as the _Access Token Type_.

Then create a `Personal Access Token` for this service user.

- Set a expire date if needed.
- Make sure to copy the token right away as it will not be visable again.

**Finally, go to the `Orgnisation` tab, and in the top right corner add the newly created service user as a `Org User Manager` for this orgnisation.**

# Step 5: Put together the above information as a JSON object and import to MongoDB.

The JSON object is

```
{
    idString: "", // the orgnisation name
    authority: "", // the url of the Zitadel instance

    projectResourceId: "", // the id of Insights project

    reactClientId: "@insights", // the React application id
    reactRedirectUri: "", // the React redirect URI

    introspectionAppId: "", // the id of the application Node With Key
    introspectionKeyId: "", // the key id of the application Node With Key
    introspectionKey: "", // the key of the application Node With Key
    introspectionClientId: "@insights", // the client id of the application Node With Key

    serviceAccountPersonalAccessToken: "", // access token of the service account machine
}
```

Then excute function `createZitadelOrgManually` from `Helper` with it to import the info into MongoDB.

# Step 6: Add orgnisation name to `Types.ts`

Add the newly creatd orgnisation name to `enum ZitadelOrgIdString` in `src/Types.ts`. This is to make sure the server only reponse organisations in the correct Zitadel Organisation (or domain).

# Step 7: Modify the frontend

In the frontend, add the newly creatd orgnisation name to `enum ZitadelOrgIdString` in `src/utils/Types.ts`.
You may also check if you need to modify `getOrgFromHostname` function in `src/utils/Helper.ts`.
